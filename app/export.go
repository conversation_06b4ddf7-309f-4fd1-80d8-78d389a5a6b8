package app

import "fmt"

type InitFunc func() error

var InitFuncs = map[string]InitFunc{}

func Init() {

	apps := make([]string, 0, len(InitFuncs))
	for key, fn := range InitFuncs {
		fmt.Println("-----------------------------[app start init]", key, "-----------------------------")
		err := fn()
		if err != nil {
			panic(fmt.Sprintf("init %s fail: %s", key, err.Error()))
		}
		fmt.Println("-----------------------------[app success init]", key, "-----------------------------")
		apps = append(apps, key)
	}

	fmt.Println("---------------------------------------------\r\n app start:", apps, "\r\n---------------------------------------------\r\n")
}
