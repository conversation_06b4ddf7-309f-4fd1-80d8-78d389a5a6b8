package proto_intervene

import "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"

type Child struct {
	ID               string      `json:"id"`
	CreatedBy        interface{} `json:"createdBy"`
	CreatedDate      interface{} `json:"createdDate"`
	LastModifiedBy   interface{} `json:"lastModifiedBy"`
	LastModifiedDate interface{} `json:"lastModifiedDate"`
	TenantId         interface{} `json:"tenantId"`
	ParentId         string      `json:"parentId"`
	Name             string      `json:"name"`
	Code             string      `json:"code"`
	Description      interface{} `json:"description"`
	Enabled          bool        `json:"enabled"`
	Children         []Child     `json:"children"`
}

type SynonymTreePayload struct {
	Data []Child `json:"data"`
}

var ProtoSynonymTreeAPIV2 = pandora.NewPandoraProto[SynonymTreePayload, SynonymTreePayload]()
