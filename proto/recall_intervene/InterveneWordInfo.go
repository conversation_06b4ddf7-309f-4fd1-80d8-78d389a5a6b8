package proto_intervene

import "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"

type WordsPayLoad struct {
	Data []WordsDetail `json:"data"`
}

type WordsDetail struct {
	Word                string `json:"word"`
	RelationWords       string `json:"relationWords"`
	MutualRelationWords string `json:"mutualRelationWords"`
}

var InterveneWordDetailAPIV2 = pandora.NewPandoraProto[WordsPayLoad, WordsPayLoad]()
