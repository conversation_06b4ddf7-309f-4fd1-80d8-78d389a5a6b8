package proto_intervene

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
)

type ExtraInfo struct {
	Raw     string
	Replace string
}

type RecallData struct {
	Query     string            `json:"query"`
	Type      string            `json:"type"`
	Extra     []*ExtraInfo      `json:"extra"`
	IndexCode string            `json:"indexCode"`
	Message   string            `json:"message"`
	Docs      []*map[string]any `json:"docs"`
	MatchType string            `json:"matchType"`
}

type ProtoInterveneAPIV2RequestPayload struct {
	Data []*RecallData `json:"data"`
}

type ProtoInterveneAPIV2ResponsePayload struct {
	Data []*RecallData `json:"results"`
}

var ProtoInterveneAPIV2 = pandora.NewPandoraProto[ProtoInterveneAPIV2RequestPayload, ProtoInterveneAPIV2ResponsePayload]()
