package proto_qubase

import "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"

type WordsPayLoad struct {
	Data []WordsDetail `json:"data"`
}

type WordsDetail struct {
	Word                string `json:"word"`
	RelationWords       string `json:"relationWords"`
	MutualRelationWords string `json:"mutualRelationWords"`
}

var QubaseWordDetailAPIV2 = pandora.NewPandoraProto[WordsPayLoad, WordsPayLoad]()
