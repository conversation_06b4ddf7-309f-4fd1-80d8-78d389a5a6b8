package proto_qubase

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
)

type ProtoQubaseAPIV2RequestPayload struct {
	Texts           []string `json:"query"`
	CorrectStrategy []string `json:"correctStrategy"`
	RewriteStrategy []string `json:"rewriteStrategy"`
}

type ProtoQubaseAPIV2Result struct {
	Query string                       `json:"query"`
	Type  string                       `json:"type"`
	Extra []*ProtoQubaseAPIV2ExtraInfo `json:"extra"`
}

type ProtoQubaseAPIV2ResponsePayload struct {
	Results []*ProtoQubaseAPIV2Result `json:"results"`
}

type ProtoQubaseAPIV2ExtraInfo struct {
	Raw     string `json:"raw"`
	Replace string `json:"replace"`
	Query   string `json:"query"`
}

var ProtoQubaseAPIV2 = pandora.NewPandoraProto[ProtoQubaseAPIV2RequestPayload, ProtoQubaseAPIV2ResponsePayload]()
