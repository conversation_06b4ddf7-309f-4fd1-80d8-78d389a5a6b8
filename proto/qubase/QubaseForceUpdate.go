package proto_qubase

import "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"

type GetRespHeader struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	TraceId string `json:"traceId"`
}

type GetRespPayLoad struct {
	Timestamp int64 `json:"timestamp"`
}

type GetResponse struct {
	Header  GetRespHeader  `json:"header"`
	PayLoad GetRespPayLoad `json:"payload"`
}

var ProtoQubaseForceUpdateAPIV2 = pandora.NewPandoraProto[GetRespPayLoad, GetRespPayLoad]()
