package biz

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
)

type ProtoBizAPIV2Request struct {
	Query      []string `json:"query"`
	Business   string   `json:"business" default:"lynxiao-flow"`
	Limit      int      `json:"limit" default:"5"`
	FullText   bool     `json:"full_text" default:"true"`   // 改 Full_text 为 FullText
	OpenRerank bool     `json:"open_rerank" default:"true"` // 改 Open_rerank 为 OpenRerank
	Highlight  bool     `json:"highlight" default:"false"`
	Crawler    bool     `json:"crawler" default:"false"`
	Pipeline   string   `json:"pipeline"`
}

type ProtoBizAPIV2Response struct {
	Results []*ProtoBizAPIV2BizSearchData `json:"results"`
}

type ProtoBizAPIV2BizSearchData struct {
	Query string                     `json:"query"`
	Code  int                        `json:"code"`
	Msg   string                     `json:"message"`
	Data  []*ProtoBizAPIV2SearchItem `json:"docs"`
}

type ProtoBizAPIV2SearchItem struct {
	Title   string  `json:"title"`
	Summary string  `json:"summary"`
	Content string  `json:"content"`
	Url     string  `json:"url"`
	Score   float64 `json:"score"`
}

var ProtoBizAPIV2 = pandora.NewPandoraProto[ProtoBizAPIV2Request, ProtoBizAPIV2Response]()
