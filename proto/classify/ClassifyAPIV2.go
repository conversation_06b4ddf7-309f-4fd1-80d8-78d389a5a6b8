package proto_classify

import "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"

type ProtoClassifyAPIV2Request struct {
	Top     int      `json:"topk" default:"1"`
	Model   string   `json:"model"`
	Queries []string `json:"texts" `
	Texts   []string `json:"queries"` // 兼容老协议
}

type ProtoTimelinessAPIV2Request struct {
	Top               int         `json:"topk" default:"1"`
	Model             string      `json:"model"`
	Texts             []*TimeInfo `json:"texts" `
	Queries           []*TimeInfo `json:"queries"` // 兼容老协议
	TimelinessQueries []string    // 时效性协议
}

type TimeInfo struct {
	Query   string `json:"query"`
	Text    string `json:"text"`
	LabelId int32  `json:"label_id"`
	Domain  int32  `json:"domain"`
}

type ProtoClassifyAPIV2Response struct {
	Model   string                   `json:"model"`
	Results []*ProtoClassifyAPIV2Res `json:"results"`
}

type ProtoClassifyAPIV2Res struct {
	// 突发分类和领域分类
	Query   string                      `json:"text,omitempty"`
	Domains []*ProtoClassifyAPIV2Domain `json:"domains,omitempty"`
	// 时效性协议
	Score      float32 `json:"score,omitempty"`
	Timeliness int32   `json:"timeliness,omitempty"`
}

type ProtoClassifyAPIV2Domain struct {
	Label  string  `json:"label"`
	Score  float32 `json:"score"`
	Domain int     `json:"label_id,omitempty"`
	Index  int     `json:"index,omitempty"`
}

var ProtoClassifyAPIV2 = pandora.NewPandoraProto[ProtoClassifyAPIV2Request, ProtoClassifyAPIV2Response]()
var ProtoTimelinessAPIV2 = pandora.NewPandoraProto[ProtoTimelinessAPIV2Request, ProtoClassifyAPIV2Response]()
