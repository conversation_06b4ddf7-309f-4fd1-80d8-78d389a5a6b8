package proto_seg

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
)

// 请求的payload
type SegAPIV2TextsRequestPayLoad struct {
	Data []string `json:"data"`
}

// 结果的payload
type SegAPIV2TextsResponsePayLoad struct {
	Data []string `json:"data"`

	Mod int `json:"mode"`
}

var ProtoSegAPIV2Texts = pandora.NewPandoraProto[SegAPIV2TextsRequestPayLoad, SegAPIV2TextsResponsePayLoad]()
