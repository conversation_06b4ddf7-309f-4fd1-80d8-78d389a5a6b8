package proto_seg

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
)

// 请求的payload
type GetRespHeader struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
	TraceId string `json:"traceId"`
}

type GetRespPayLoad struct {
	Timestamp int64 `json:"timestamp"`
}

type GetResponse struct {
	Header  GetRespHeader  `json:"header"`
	PayLoad GetRespPayLoad `json:"payload"`
}

var ProtoSegForceUpdateAPIV2 = pandora.NewPandoraProto[GetRespPayLoad, GetRespPayLoad]()
