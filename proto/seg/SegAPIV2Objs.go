package proto_seg

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
)

// 请求的payload
type SegAPIV2ObjsRequestPayLoad struct {
	Data []*map[string]interface{} `json:"data"`
}

// 结果的payload
type SegAPIV2ObjsResponsePayLoad struct {
	Data []*map[string]any `json:"data"`
	Mod  int               `json:"mode"`

	DebugInfo []string `json:"debug_info,omitempty"`
}

var ProtoSegAPIV2Objs = pandora.NewPandoraProto[SegAPIV2ObjsRequestPayLoad, SegAPIV2ObjsResponsePayLoad]()
