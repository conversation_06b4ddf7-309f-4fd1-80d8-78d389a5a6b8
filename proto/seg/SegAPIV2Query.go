package proto_seg

import "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"

// 请求的payload
type SegAPIV2QueryRequestPayLoad struct {
	OpenSynonyms bool     `json:"openSynonyms"`
	OpenWeight   bool     `json:"openWeight"`
	Query        []string `json:"query"`
	Debug        bool     `json:"debug"`
}

type ParsedWord struct {
	Word     string   `json:"word"`     // 词
	Tag      string   `json:"tag"`      // 词性
	Weight   float32  `json:"weight"`   // 权重 -1 表示未计算权重
	Synonyms []string `json:"synonyms"` // 同义词列表
}

type ParsedDetail struct {
	Query     string        `json:"query"`      //原始query预处理后的文本串，或者纠错、改写后的文本串
	Words     []*ParsedWord `json:"words"`      //去除停用词后的结果
	NounRatio float32       `json:"noun_ratio"` //名词的占比
}

// 结果的payload
type SegAPIV2QueryResponsePayLoad struct {
	Results []*ParsedDetail `json:"results"`
}

var ProtoSegAPIV2Query = pandora.NewPandoraProto[SegAPIV2QueryRequestPayLoad, SegAPIV2QueryResponsePayLoad]()
