package proto_prerank

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
)

type RewriteWord struct {
	Raw     string `json:"raw"`
	Replace string `json:"replace"`
	Query   string `json:"query"`
}

type DataItem struct {
	Query string           `json:"query"`
	Type  string           `json:"type"`
	Extra []*RewriteWord   `json:"extra"`
	Docs  []map[string]any `json:"docs"`
}

// Payload 粗排请求负载
type Payload struct {
	TopK      int         `json:"topK" validate:"min=-1,max=1000"` // 限制TopK的取值范围为1到100
	Model     string      `json:"model" validate:"required"`
	MergeMode string      `json:"mergeMode" validate:"required,oneof=rewrite none"` // 限制MergeMode的取值
	Chunk     bool        `json:"chunk"`                                            // 是否分块
	Threshold float64     `json:"threshold" validate:"min=0.0,max=1.0"`             // 限制Threshold的取值范围为0.0到1.0
	RankExpr  string      `json:"rankExpr" validate:"required"`                     // RankExpr为必填项
	Data      []*DataItem `json:"data" validate:"required"`                         // Data为必填项
}

type ReqQA struct {
	Model   string   `json:"model"`
	Query   string   `json:"query"`
	Answers []string `json:"answers"`
}

// PrerankAPI 粗排协议
var PrerankAPI = pandora.NewPandoraProto[Payload, Payload]()
