package proto_process

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
	"go.mongodb.org/mongo-driver/bson"
)

var ProtoAPI = pandora.NewPandoraProto[Payload, Payload]()

type DistinctField struct {
	Field    string `json:"field" validate:"required"`
	Type     string `json:"type" validate:"required,oneof=string number"`
	Operator string `json:"operator" validate:"required,oneof=min max in"`
	Value    string `json:"value"`
}

type Distinct struct {
	Scene  string           `json:"scene"`
	Cond   []string         `json:"cond" validate:"required"`
	Fields []*DistinctField `json:"fields" validate:"required,min=1,dive,required"`
}

// LimitField 限制字段
type LimitField struct {
	Key  string `json:"key" validate:"required"`
	Num  int    `json:"num" validate:"required,gt=0"`
	Cond string `json:"cond" validate:"required"`
}

type SiftCondition struct {
	Field         string           `json:"field"`
	FieldType     string           `json:"fieldType" validate:"omitempty,oneof=INT LONG FLOAT32 DOUBLE STRING LENGTH"`
	Operator      string           `json:"operator" validate:"omitempty,oneof=in < > <= >= != == last_n_days last_n_hours last_n_months"`
	Origin        any              `json:"origin"`
	Logic         string           `json:"logic" validate:"omitempty,oneof=AND OR"`
	SubConditions []*SiftCondition `json:"subConditions" validate:"omitempty,dive"`
}

type SortField struct {
	Field string `json:"field"`
	Sort  string `json:"sort" validate:"oneof=asc desc"`
}

type RewriteWord struct {
	Raw     string `json:"raw"`
	Replace string `json:"replace"`
	Query   string `json:"query"`
}

type ReqData struct {
	Query string         `json:"query"`
	Type  string         `json:"type"`
	Extra []*RewriteWord `json:"extra"`
	Docs  []bson.M       `json:"docs"`
}

type Payload struct {
	Distinct []*Distinct    `json:"distinct" validate:"dive"`
	Sift     *SiftCondition `json:"sift" validate:""`
	Limit    []*LimitField  `json:"limit" validate:"dive"`
	SortList []*SortField   `json:"sortList" validate:"dive"`
	Data     []*ReqData     `json:"data" validate:"dive"`
}

func (s *SiftCondition) IsEmpty() bool {
	return s.Field == "" &&
		s.FieldType == "" &&
		s.Operator == "" &&
		s.Origin == nil &&
		s.Logic == "" &&
		len(s.SubConditions) == 0
}
