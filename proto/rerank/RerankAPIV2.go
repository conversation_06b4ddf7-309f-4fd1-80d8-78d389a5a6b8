package proto_rerank

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
)

type NormalKeywords struct {
	Time     []TimeInfo     `json:"time"`
	KeyWords []KeyWordsInfo `json:"keywords"`
}

// 定义结构体
type OriginKeywords struct {
	Location  []string `json:"location" bson:"location"`
	CoreWord  []string `json:"core" bson:"core"`
	StrongReq []string `json:"strong" bson:"strong"`
}

type TimeInfo struct {
	TimeType  string `json:"time_type" bson:"time_type"`
	StartTime string `json:"start_time" bson:"start_time"`
	EndTime   string `json:"end_time" bson:"end_time"`
	Raw       string `json:"raw" bson:"raw"`
}

type KeyWordsInfo struct {
	Type   string `json:"type" bson:"type"`
	Weight int    `json:"weight" bson:"weight"`
	Raw    string `json:"raw" bson:"raw"`
}

type QueryKeywords struct {
	OKeyWords OriginKeywords `json:"origin_keywords" bson:"origin_keywords"`
	NKeyWords NormalKeywords `json:"normal_keywords" bson:"normal_keywords"`
}

type RerankAPIV2QueryData struct {
	Query        string            `json:"query"`
	Type         string            `json:"type"`
	Extra        []*map[string]any `json:"extra"`
	Docs         []*map[string]any `json:"docs"`
	QKeywords    QueryKeywords     `json:"qKeywords" bson:"qKeywords"`
	DomainRecall bool              `json:"domainRecall"`
}

type RerankAPIV2RequestPayLoad struct {
	// Query          string          `json:"query"`
	TimeLiness      string                  `json:"timeliness"`
	Data            []*RerankAPIV2QueryData `json:"data"`
	DomainId        string                  `json:"domainId"`
	Intent          string                  `json:"intent"`
	TopK            int                     `json:"topk"`
	Limit           int                     `json:"limit"`
	ScoreThreshold  float64                 `json:"scoreThreshold"`
	Step            float64                 `json:"step"`
	Model           string                  `json:"model"`
	RankCollections []string                `json:"rankCollections"`
	RankSites       []string                `json:"rankSites"`
}

type RerankAPIV2RespPayLoad struct {
	Result []*RerankAPIV2QueryData `json:"results"`
	// JsonSpan *span.JsonSpan `json:"span,omitempty"`
}

func NewQueryKeyWords() *QueryKeywords {
	return &QueryKeywords{
		OKeyWords: OriginKeywords{
			Location:  []string{},
			CoreWord:  []string{},
			StrongReq: []string{},
		},
		NKeyWords: NormalKeywords{
			Time:     []TimeInfo{},
			KeyWords: []KeyWordsInfo{},
		},
	}
}

var ProtoRerankAPIV2 = pandora.NewPandoraProto[RerankAPIV2RequestPayLoad, RerankAPIV2RespPayLoad]()
