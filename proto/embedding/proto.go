package embedding

import "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"

// RespPayload 向量计算响应
type RespPayload struct {
	Model     string      `json:"model"`
	Embedding [][]float32 `json:"embedding"`
}

// ReqPayload 向量计算请求
type ReqPayload struct {
	Model string   `json:"model"`
	Texts []string `json:"texts" validate:"required,min=1"` // 长度大于0
}

// EmbeddingAPI 向量计算协议
var EmbeddingAPI = pandora.NewPandoraProto[ReqPayload, RespPayload]()
