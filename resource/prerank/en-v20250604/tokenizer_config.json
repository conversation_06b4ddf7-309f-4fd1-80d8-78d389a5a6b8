{"add_prefix_space": false, "added_tokens_decoder": {"0": {"content": "<s>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "1": {"content": "<pad>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "2": {"content": "</s>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "3": {"content": "<unk>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "4": {"content": "<mask>", "lstrip": true, "normalized": false, "rstrip": false, "single_word": false, "special": true}}, "bos_token": "<s>", "clean_up_tokenization_spaces": true, "cls_token": "<s>", "eos_token": "</s>", "errors": "replace", "mask_token": "<mask>", "max_length": 1024, "model_max_length": 512, "pad_token": "<pad>", "sep_token": "</s>", "stride": 0, "tokenizer_class": "<PERSON><PERSON><PERSON><PERSON>", "trim_offsets": true, "truncation_side": "right", "truncation_strategy": "longest_first", "unk_token": "<unk>"}