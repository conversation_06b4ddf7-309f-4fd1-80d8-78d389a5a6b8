[[models]]
enabled = true
name = "embedding_v20241218"
timeout = 2000
batch_size = 1
res_dim = 768
default = true

[[tokenizers]]
enabled = true
name = "embedding_v20241218"
path = "/root/embedding-med-1218-res/tokenizer/tokenizer.json"
lib = "/fengli16/projects/lynxiao-ai-search/resource/tokenizer/libtokenizer_arm64.so"
use_local = 1
use_simple = 0
pad_id = 1
pad_fixed = 0
max_length = 512
worker_num = 96
queue_num = 1024

[[onnxruntime]]
enabled = true
name = "embedding_v20241218"
model_path = "/root/embedding-med-1218-res/gte-med-op12.onnx"
lib_path = "/fengli16/projects/lynxiao-ai-search/resource/onnxinfer/libonnxinfer.so"
device_type = "npu"
enble_batch = false
engine_type = "onnxinfer"
device_id = 0
infer_thread_num = 1	# 推理线程数：多个onnx实例+多个处理线程。默认=2
infer_queue_length = 1024 # 不开启batch时，推理队列的长度，默认1024，开启batch时，长度等于InferThreadNum，该设置无效。