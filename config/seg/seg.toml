[seg_res]
enabled = true
name = "seg_res"
fileCount = 1
parseCount = 10
posPath = "./resource/jieba/dict.txt"
baseDir = "./resource/seg"

[seg_intervene]
enabled = true
name = "seg_intervene"
open = false
interveneEnabled = false
segEnabled = true
serviceName = "region-manager"
synonymUrl = "/meta/api/v1/word/synonym_word/list"
cutLength = 0
tricker = 1
# puncStr = "[!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~，《。》、？；￥…（）]+"
# emojiStr = "[\x{1F600}-\x{1F64F}]|[\x{1F300}-\x{1F5FF}]|[\x{1F680}-\x{1F6FF}]|[\x{2600}-\x{26FF}]|[\x{2700}-\x{27BF}]|[\x{FE00}-\x{FE0F}]|[\x{1F1E6}-\x{1F1FF}]|[\x{1F200}-\x{1F2FF}]|[\x{1F900}-\x{1F9FF}]"
# zeroWidthStr = "�|[\x{200B}-\x{200F}\x{202A}-\x{202E}\x{2060}-\x{206F}\x{FEFF}]"



[[tokenizers]]
enabled = true
name = "Xenova-ernie-3.0"
path = "./resource/tokenizer/Xenova-ernie-3.0-nano-zh/tokenizer.json"
count = 3
use_local = 1
pad_id = 0
pad_fixed = 0
max_length = 512
lib = "./resource/tokenizer/libtokenizer_wrapper.so"
