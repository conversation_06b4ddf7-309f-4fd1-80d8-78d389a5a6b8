[segment]
enabled=true
name = "segment"
segmentPath = "./resource/rerank/data/dict/s_1.txt"
stopWordsPath = "./resource/rerank/data/dict/stop_word.txt"
ccModPath = "./resource/rerank/data/opencc/"
segmentPaths = ["./resource/rerank/data/dict/biochemical.txt","./resource/rerank/data/dict/gse.txt","./resource/rerank/data/dict/ICD-10.txt","./resource/rerank/data/dict/medicine.txt"]

[res]
enabled=true
name = "res"
resBaseDir = "./resource/rerank"
stwPath = "./resource/rerank/stopwords.txt"
medicalDomainPath = "./resource/rerank/medical_domain.txt"
# 屏蔽词
blockDict = ["股票", "股市", "A股", "a股", "概念股", "个股",
    "时政", "时事", "政治",
	"科技", "科技类", "科研", "前沿",
    "财经", "金融", "经济",
    "社会",
    "娱乐", "娱乐圈", "八卦",
    "教育",
    "体育",
    "游戏",

	"事件", "大事", "实事", "消息", "动态",
	"热门", "热点", "热搜", "热议", "头条", "最热", "最火", "火爆", "比较受关注", "有代表性的",
	"新闻", "要闻", "趣闻", "资讯", "快讯", "话题", "问题", "内容", "情况", "状况",
    "聚焦", "焦点", "盘点", "回顾",
	"分析", "报道", "讨论", "探讨", "评价", "介绍", "推荐",
    "形势", "局势", "状况",

    "当前", "当下", "现状", "现况", "近状", "近况", "最近", "最新", "实时",
    "重要", "重点", "重大", "主要", "综合",

    "国内", "中国", "我国", "全国", "国家",
    "国外", "海外", "外国", "国际", 
    "国内外", "全球", "世界", "全世界", "世界上", "全网",]
# 前缀词
prefixDict = ["中国"]
# 后缀词
suffixDict = ["省", "市", "区", "县", "乡", "镇", "街道","公园", "森林公园", "景区","年"]
# 同义词
synonymsDict = [["人工智能","AI"]]



[params]
enabled=true
name="params"
healthLevel = "L06"
domain2baikeWeight = {"201"=15.0}
commonLevel = "L03"
sportsLevel = "L09"

[[tokenizers]]
enabled = true
name = "rerank-Xenova-ernie-3.0"
path = "./resource/tokenizer/Xenova-ernie-3.0-nano-zh/tokenizer.json"
count = 3
use_local = 1
pad_id = 0
pad_fixed = 0
max_length = 512
lib = "./resource/tokenizer/libtokenizer_wrapper.so"

[[ases]]
enabled = true
name = "termweight_v20231221"
tlb_server = "ai-model-server-v2-2"
local_url = "*************:32173/termweight_v20231221"
#0 ase 模式，1 tlb 模式, 2 ip 地址模式
mode = 1
timeout_mills = 2000

[[ants_pools]]
enabled = true
name="default"
goroutine_num=8192