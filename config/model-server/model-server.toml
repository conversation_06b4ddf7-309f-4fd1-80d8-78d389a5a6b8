[[onnxruntime]]
enabled = false
name = "prerank_v20240906"
model_path = "/jfs/fengli16/IflySearch_Resource/Prerank/0906/fp16_model_0906.onnx"
lib_path = "/jfs/fengli16/IflySearch_Resource/ort_lib/libonnxruntime.so"
device_type = "nvidia"
device_id = 0

infer_thread_num = 2	# 推理线程数：多个onnx实例+多个处理线程。默认=2
infer_queue_length = 1024 # 不开启batch时，推理队列的长度，默认1024，开启batch时，长度等于InferThreadNum，该设置无效。

[[onnxruntime]]
enabled = false
name = "rank_v20241111"
model_path = "/jfs/fengli16/IflySearch_Resource/Rank/1111/model/fp16_model.onnx"
lib_path = "/jfs/fengli16/IflySearch_Resource/ort_lib/libonnxruntime.so"
device_type = "nvidia"
device_id = 0

infer_thread_num = 2	# 推理线程数：多个onnx实例+多个处理线程。默认=2
infer_queue_length = 1024 # 不开启batch时，推理队列的长度，默认1024，开启batch时，长度等于InferThreadNum，该设置无效。

[[onnxruntime]]
enabled = false
name = "embedding_v20240913"
model_path = "/jfs/fengli16/IflySearch_Resource/Semantic/0913/model/gte-ft-v3.0.onnx"
lib_path = "/jfs/fengli16/IflySearch_Resource/ort_lib/libonnxruntime.so"
device_type = "nvidia"
device_id = 0

infer_thread_num = 2	# 推理线程数：多个onnx实例+多个处理线程。默认=2
infer_queue_length = 1024 # 不开启batch时，推理队列的长度，默认1024，开启batch时，长度等于InferThreadNum，该设置无效。

[[onnxruntime]]
enabled = false
name = "rank_v20250325"
model_path = "/jfs/fengli16/IflySearch_Resource/Rank/v20250325/jina-reranker-v2-base-multilingual_cast.onnx"
lib_path = "/jfs/fengli16/IflySearch_Resource/ort_lib/libonnxruntime.so"
device_type = "nvidia"
device_id = 0
infer_thread_num = 2	# 推理线程数：多个onnx实例+多个处理线程。默认=2
infer_queue_length = 1024 # 不开启batch时，推理队列的长度，默认1024，开启batch时，长度等于InferThreadNum，该设置无效。

[[onnxruntime]]
enabled = false
name = "embedding_v20240913"
model_path = "/jfs/fengli16/IflySearch_Resource/Semantic/0913/model/gte-ft-v3.0.onnx"
lib_path = "/jfs/fengli16/IflySearch_Resource/ort_lib/libonnxruntime.so"
device_type = "nvidia"
device_id = 0
infer_thread_num = 2	# 推理线程数：多个onnx实例+多个处理线程。默认=2
infer_queue_length = 1024 # 不开启batch时，推理队列的长度，默认1024，开启batch时，长度等于InferThreadNum，该设置无效。

[[onnxruntime]]
enabled = true
name = "rank_v20250422"
model_path = "/jfs/fengli16/IflySearch_Resource/Rank/v20250422/fp16_model.onnx"
lib_path = "/jfs/fengli16/IflySearch_Resource/ort_lib/libonnxruntime.so"
device_type = "nvidia"
device_id = 0
infer_thread_num = 2	# 推理线程数：多个onnx实例+多个处理线程。默认=2
infer_queue_length = 1024 # 不开启batch时，推理队列的长度，默认1024，开启batch时，长度等于InferThreadNum，该设置无效。