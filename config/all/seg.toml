[seg_res]
enabled = true
name = "seg_res"
fileCount = 1
parseCount = 10
posPath = "./resource/jieba/dict.txt"
baseDir = "./resource/seg"

[seg_intervene]
enabled = true
name = "seg_intervene"
interveneEnabled = false
segEnabled = true
serviceName = "region-manager"
synonymUrl = "/meta/api/v1/word/synonym_word/list"
tricker = 1
cutLength = -1

[[tokenizers]]
enabled = true
name = "seg_Xenova-ernie-3.0"
path = "./resource/tokenizer/Xenova-ernie-3.0-nano-zh/tokenizer.json"
count = 3
use_local = 1
pad_id = 0
pad_fixed = 0
max_length = 512
lib = "./resource/tokenizer/libtokenizer_wrapper.so"



[[ases]]
enabled = true
name = "termweight_v20231221"
tlb_server = "ai-termweight-v2"
local_url = "**************:32171/termweight_v20231221"
#0 ase 模式，1 tlb 模式, 2 ip 地址模式
mode = 1
timeout_mills = 2000

