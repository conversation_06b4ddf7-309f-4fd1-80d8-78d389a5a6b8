[[prerank_models]]
enabled = true
name = "prerank_v20240906"
timeout = 2000
batch_size = 4

[[ases]]
enabled = true
name = "prerank_v20240906"
app_id = "9d4f7017"
api_key = "461a9ca5ba93757f2e7d8aa9e33b818c"
api_secret = "NWJlZDU2MmY4MTFlY2RjNGQ3NjJkYzhm"
ase_url = "http://hu-ase-searchapi.aicp.private/v1/private/sd7de34e3"
tlb_server = "modelserver_test"
local_url = "127.0.0.1:32030/prerank_v20240906"
#0 ase 模式，1 tlb 模式, 2 ip 地址模式
mode = 0
timeout_mills = 2000

[[tokenizers]]
enabled = true
name = "prerank_v20240906"
path = "./resource/prerank/ernie-3.0-nano-zh/tokenizer.json"
lib = "./resource/tokenizer/libtokenizer_wrapper.so"
use_simple = 0
use_local = 1
pad_id = 0
pad_fixed = 0
max_length = 512
worker_num = 96
queue_num = 1024

