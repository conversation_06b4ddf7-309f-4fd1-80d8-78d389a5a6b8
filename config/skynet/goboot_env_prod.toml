[goboot]
enabled=true
service_name="${SKYNET_ACTION_CODE}"

#server 配置
[[http_server]]
enabled=true
port=${PORT}

[http_server.metrics]
url="/actuator/prometheus"

#tlb配置
[tlb_sdk]
enabled=true
servers="${skynet.tlb.endpoints}"
port=${PORT}
tlb_tag="${skynet.tlb.tag}"

#默认日志
[[loggers]]
enabled=true
file_path="./log/${SKYNET_ACTION_CODE}.log"
level="info"

[elk]
enabled=true
name="elk_log"
log_level="info"
tags="region:${lynxiao.region-code},env:${lynxiao.env-code}"

[elk.local_log]
reuse="default"

[elk.kafka]
brokers = "${lynxiao.elk.kafka.servers}"
topic = "${lynxiao.elk.kafka.topic}"