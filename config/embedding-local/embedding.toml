[[models]]
enabled = true
name = "embedding_v20241218"
timeout = 2000
batch_size = 1
res_dim = 768
default = true

[[tokenizers]]
enabled = true
name = "embedding_v20241218"
path = "./resource/embedding/Alibaba-NLP-gte-multilingual-base/tokenizer.json"
lib = "./resource/tokenizer/libtokenizer_v1.so"
use_local = 1
use_simple = 0
pad_id = 1
pad_fixed = 0
max_length = 512
worker_num = 96
queue_num = 1024

[[onnxruntime]]
enabled = true
name = "embedding_v20241218"
model_path = "/jfs/fengli16/IflySearch_Resource/Semantic/1218/model/iflytek-medical-embedder-v0.2.onnx"
lib_path = "/jfs/fengli16/IflySearch_Resource/ort_lib/libonnxruntime.so"
device_type = "nvidia"
enble_batch = false
engine_type = "onnxruntime"
device_id = 0
infer_thread_num = 2	# 推理线程数：多个onnx实例+多个处理线程。默认=2
infer_queue_length = 1024 # 不开启batch时，推理队列的长度，默认1024，开启batch时，长度等于InferThreadNum，该设置无效。

[[models]]
enabled = true
name = "embedding_v20250617"
timeout = 2000
batch_size = 1
res_dim = 768
default = false

[[tokenizers]]
enabled = true
name = "embedding_v20250617"
path = "./resource/embedding/v20250617/tokenizer.json"
lib = "./resource/tokenizer/libtokenizer_v1.so"
use_local = 1
use_simple = 0
pad_id = 1
pad_fixed = 0
max_length = 512
worker_num = 96
queue_num = 1024

[[onnxruntime]]
enabled = true
name = "embedding_v20250617"
model_path = "/jfs/fengli16/IflySearch_Resource/Semantic/embedding-v0617/gte-ft-v0617-op12.onnx"
lib_path = "/jfs/fengli16/IflySearch_Resource/ort_lib/libonnxruntime.so"
device_type = "nvidia"
enble_batch = false
engine_type = "onnxruntime"
device_id = 0
infer_thread_num = 2	# 推理线程数：多个onnx实例+多个处理线程。默认=2