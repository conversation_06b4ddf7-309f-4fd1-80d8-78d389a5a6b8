# 第一阶段：使用Go镜像来构建应用程序
FROM artifacts.iflytek.com/docker-private/datahub/go1.23.3-gcc9.4:v1.0.0 AS builder

# 设置工作目录
WORKDIR /lynxiao

# 复制所有文件到工作目录
COPY . .

# # 设置 Go 的路径
ENV PATH="/root/go/bin:${PATH}"
ENV GOPROXY="https://${BUILDER}:${DEPEND_APIKEY}@depend.iflytek.com/artifactory/api/go/go-repo"

RUN ./build_all.sh
# 下载依赖并构建 Go 应用程序
RUN go mod tidy && go build -tags=gpu -o ./output/lynxiao-ai-search

# 第二阶段：创建一个更小的运行时镜像,添加了libssl依赖
FROM artifacts.iflytek.com/docker-private/datahub/cuda:11.7.1-cudnn8-runtime-ubuntu20.04

# 设置工作目录
WORKDIR /lynxiao

RUN export LD_LIBRARY_PATH=$(pwd)/resource/intent/lib:$(pwd)/resource/tokenizer:$LD_LIBRARY_PATH
RUN echo $LD_LIBRARY_PATH
RUN apt-get update
RUN apt-get install -y libopenblas-dev

# 从第一阶段复制构建的应用程序二进制文件
COPY --from=builder /lynxiao/output/lynxiao-ai-search .
COPY --from=builder /lynxiao/resource/ ./resource