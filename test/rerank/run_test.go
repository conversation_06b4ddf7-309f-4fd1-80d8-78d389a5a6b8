package main

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"testing"

	test2 "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/test"
)

func TestRerankV2(t *testing.T) {
	requestData := "{\"header\":{\"traceId\":\"rl1oifeg@lynxiao-portal@lynxiao-center@89382\"},\"payload\":{\"model\":\"5001_V1120\",\"topK\":5,\"scoreThreshold\":0.7,\"step\":0,\"rankCollections\":[\"search_api.kb_health\",\"search_api.kb_health_0814\"],\"rankSites\":[\"m.baidu.com/bh/m/detail/ar\",\"health.baidu.com/m/detail/ar\"],\"intent\":\"\",\"timeliness\":\"\",\"data\":[{\"query\":\"心脏疾病研究背景介绍_x005f_x000D_\",\"docs\":[{\"url\":\"https://ask.39.net/question/61656149.html\",\"id\":\"9016808571398035811\",\"domain\":\"ask.39.net\",\"title\":\"心脏疾病有哪些？\",\"summary\":\"心脏病是心脏疾病的总称，其病因有多种，风湿性心脏病、先天性心脏病、高血压这毛病性心脏病、肺原性心脏病，贫血性心脏病，梅毒性心脏病，地方性心肌病，冠心病要重视治疗和调理，心肌炎的问题等，先考虑风湿性心脏病、心肌炎的问题，建议去当地正规医院或者机构心电图与心脏彩超，血尿常规，心肌酶检查，早诊断，早针对性治疗，脏病主要包括冠心病要重视治疗和调理。\",\"pv\":0,\"ts\":1663136820000,\"realm\":\"教育\",\"authority\":6,\"pr\":5,\"top\":0,\"content\":\"心脏病是心脏疾病的总称，其病因有多种，风湿性心脏病、先天性心脏病、高血压这毛病性心脏病、肺原性心脏病，贫血性心脏病，梅毒性心脏病，地方性心肌病，冠心病要重视治疗和调理，心肌炎的问题等，先考虑风湿性心脏病、心肌炎的问题，建议去当地正规医院或者机构心电图与心脏彩超，血尿常规，心肌酶检查，早诊断，早针对性治疗，脏病主要包括冠心病要重视治疗和调理。\",\"rank_score\":0.949773447,\"level\":\"A\",\"collection\":\"search_api.v5_health_elite\",\"content_collection\":\"C\",\"pos_id\":\"7\",\"_rank_score\":0.949773447,\"levels\":{\"L06\":3},\"_indexCode\":\"search_api.v5_health_elite\",\"post_ts\":1663136820000,\"_rank_index\":1}]}]}}"

	// 发送请求
	resp, err := http.Post(test2.RerankApiV2, "application/json", bytes.NewBuffer([]byte(requestData)))
	if err != nil {
		fmt.Println("发送请求失败:", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println("响应状态码:", resp.StatusCode)
	fmt.Println("响应内容:", string(body))
}
