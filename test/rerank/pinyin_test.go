package main

import (
	"fmt"
	"testing"

	"github.com/mozillazg/go-pinyin"
)

func TestPinyin(t *testing.T) {
	// 创建懒人拼音转换器
	args := pinyin.NewArgs()
	args.Style = pinyin.NORMAL // 无音标模式
	args.Separator = ""        // 禁用分隔符
	args.Fallback = func(r rune, a pinyin.Args) []string {
		return []string{string(r)} // 非汉字字符回退策略
	}

	// 执行转换（效果最接近lazy_pinyin）
	result := pinyin.Pinyin("刘德华", args)
	fmt.Println(flatten(result)) // 输出: [liu de hua]
}

// 二维切片转一维
func flatten(input [][]string) []string {
	var res []string
	for _, group := range input {
		res = append(res, group...)
	}
	return res
}

// BenchmarkPinyin 对拼音转换函数进行基准测试
func BenchmarkPinyin(b *testing.B) {
	args := pinyin.NewArgs()
	args.Style = pinyin.NORMAL // 无音标模式
	args.Separator = ""        // 禁用分隔符
	args.Fallback = func(r rune, a pinyin.Args) []string {
		return []string{string(r)} // 非汉字字符回退策略
	}

	// b.ResetTimer() 用于重置计时器，忽略准备代码的时间
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		// 执行基准测试的操作
		pinyin.Pinyin("刘德华", args)
	}
}

// BenchmarkFlatten 对二维切片转一维函数进行基准测试
func BenchmarkFlatten(b *testing.B) {
	// 准备测试数据
	testData := [][]string{
		{"liu"}, {"de"}, {"hua"},
	}

	// b.ResetTimer() 用于重置计时器，忽略准备代码的时间
	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		// 执行基准测试的操作
		flatten(testData)
	}
}
