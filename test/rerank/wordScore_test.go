package main

import (
	"fmt"
	"math"
	"strings"
	"testing"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/arena"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/arena/arena_api"
	"github.com/mozillazg/go-pinyin"
)

func ComparePinyin(word1, word2 string) bool {
	args := pinyin.NewArgs()
	getPinyinStr := func(word string) string {
		var s []string
		for _, parts := range pinyin.Pinyin(word, args) {
			s = append(s, parts[0])
		}
		return strings.Join(s, "")
	}
	return getPinyinStr(word1) == getPinyinStr(word2)
}

func MergeWordsWithTag(words []string, scores []float64, tags []bool, threshold float64) ([]string, []float64, []bool) {
	var mergedWords []string
	var mergedScores []float64
	var mergedTags []bool

	var currentWords []string
	var currentScore float64
	var currentTag bool

	for i := 0; i < len(words); i++ {
		word := words[i]
		score := scores[i]
		tag := tags[i]

		if score > threshold && !tag { // 分数大于阈值且标签为 false
			currentWords = append(currentWords, word)
			currentScore += score
		} else {
			if len(currentWords) > 0 {
				// 如果有合并的单词，存储并重置
				mergedWords = append(mergedWords, strings.Join(currentWords, ""))
				mergedScores = append(mergedScores, currentScore)
				mergedTags = append(mergedTags, currentTag)
				currentWords = nil
				currentScore = 0
				currentTag = false
			}
			// 存储未合并的单词
			mergedWords = append(mergedWords, word)
			mergedScores = append(mergedScores, score)
			mergedTags = append(mergedTags, tag)
		}
	}

	// 合并最后剩下的单词
	if len(currentWords) > 0 {
		mergedWords = append(mergedWords, strings.Join(currentWords, ""))
		mergedScores = append(mergedScores, currentScore)
		mergedTags = append(mergedTags, currentTag)
	}

	return mergedWords, mergedScores, mergedTags
}

func MatchingIntersection(substring, str string) int {
	set := make(map[rune]bool)
	for _, char := range substring {
		set[char] = true
	}

	intersectionCount := 0
	for _, char := range str {
		if set[char] {
			intersectionCount++
		}
	}

	return intersectionCount
}

func getIndexes(values []float64, threshold float64) []int {
	var indexes []int
	for i, value := range values {
		if value >= threshold {
			indexes = append(indexes, i)
		}
	}
	return indexes
}

func getMaxWeightIndexes(values []float64) ([]int, error) {

	var indexes []int
	maxValue, err := MaxValues(values)
	if err != nil {
		return indexes, err
	}
	for i, value := range values {
		if math.Abs(value-maxValue) <= maxValue*0.20 {
			indexes = append(indexes, i)
		}
	}
	return indexes, nil
}

func MaxValues(values []float64) (float64, error) {
	if len(values) > 0 {
		maxVal := values[0]
		for _, value := range values[1:] {
			if value > maxVal {
				maxVal = value
			}
		}
		return maxVal, nil
	} else {
		return 0, fmt.Errorf("slice is empty")
	}
}

func TestWordscore(t *testing.T) {
	arena := arena.NewFixArena(1024 * 1024)

	// var query, _ = arena_api.MakeWithDefault[string](arena)
	// var title, _ = arena_api.MakeWithDefault[string](arena)
	// var queryKeysOrigin, _ = arena_api.MakeSliceWithDefault[string](arena, 0, 1024)
	// var queryValues, _ = arena_api.MakeSliceWithDefault[float64](arena, 0, 1024)
	// var titleKeys, _ = arena_api.MakeSliceWithDefault[string](arena, 0, 1024)
	// var titleValues, _ = arena_api.MakeSliceWithDefault[float64](arena, 0, 1024)
	e1 := common.Objects(nil)
	fmt.Println(e1)
	var query = "氨咖黄敏胶囊"
	var title = "氨咖黄敏胶囊"
	var queryKeysOrigin = []string{"氨", "咖", "黄", "敏", "胶囊"}
	var queryValues = []float64{0.22868585586547852, 0.2083163857460022, 0.20137475430965424, 0.227092444896698, 0.13453060388565063}
	var titleKeys = []string{"氨", "咖", "黄", "敏", "胶囊"}
	var titleValues = []float64{0.22868585586547852, 0.2083163857460022, 0.20137475430965424, 0.227092444896698, 0.13453060388565063}

	var wordsScore float64

	var queryKeys, _ = arena_api.MakeSliceWithDefault[string](arena, 0, len(queryKeysOrigin))
	queryKeys = append(queryKeys, queryKeysOrigin...)

	// 如果查询中含有数字，直接返回0得分
	if strings.ContainsAny(query, "0123456789") {
		wordsScore = 0
	} else {
		queryLen := len(queryKeys)
		titleLen := len(titleKeys)

		// 用来标记匹配的数组
		queryTag, _ := arena_api.MakeSliceWithDefault[bool](arena, 0, queryLen)
		titleTag, _ := arena_api.MakeSliceWithDefault[bool](arena, 0, titleLen)
		for i := 0; i < queryLen; i++ {
			queryTag[i] = false
		}
		for i := 0; i < titleLen; i++ {
			titleTag[i] = false
		}

		// 遍历标题和查询的关键词
		for i := 0; i < titleLen; i++ {
			for j := 0; j < queryLen; j++ {
				// 如果两个关键词都未匹配过且长度相同
				if !titleTag[i] && !queryTag[j] && len(titleKeys[i]) == len(queryKeys[j]) {
					// 直接匹配
					if titleKeys[i] == queryKeys[j] {
						// 直接匹配
						wordsScore += math.Min(titleValues[i], queryValues[j])
						titleTag[i] = true
						queryTag[j] = true

					} else if len(titleKeys[i]) > 3 && ComparePinyin(titleKeys[i], queryKeys[j]) {
						// 拼音匹配
						wordsScore += math.Min(titleValues[i], queryValues[j])
						titleTag[i] = true
						queryTag[j] = true
					}
				}
				if titleTag[i] {
					break
				}
			}
		}

		queryTagTemp, _ := arena_api.MakeSliceWithDefault[bool](arena, 0, len(queryTag))
		queryTagTemp = append(queryTagTemp, queryTag...)

		titleTagTemp, _ := arena_api.MakeSliceWithDefault[bool](arena, 0, len(titleTag))
		titleTagTemp = append(titleTagTemp, titleTag...)

		i := 0
		for i < titleLen {
			j := 0
			for j < queryLen {
				// 如果两个词都没有匹配
				if !titleTag[i] && !queryTag[j] {
					// 检查 titleKeys[i] 是否是 queryKeys[j] 的子串，反之亦然
					if strings.Contains(queryKeys[j], titleKeys[i]) || strings.Contains(titleKeys[i], queryKeys[j]) {
						// 计算子串比率
						substringRatio := float64(math.Min(float64(len(titleKeys[i])), float64(len(queryKeys[j])))) / float64(math.Max(float64(len(titleKeys[i])), float64(len(queryKeys[j]))))
						wordsScore += math.Min(titleValues[i], queryValues[j]) * substringRatio

						if len(titleKeys[i]) < len(queryKeys[j]) {
							// 子串匹配一：更新标记并减少查询词
							titleTag[i] = true
							queryTagTemp[j] = true
							queryKeys[j] = strings.Replace(queryKeys[j], titleKeys[i], "", 1)
							i = 0
						} else if len(titleKeys[i]) > len(queryKeys[j]) {
							// 子串匹配二：更新标记并减少标题词
							queryTag[j] = true
							titleTagTemp[i] = true
							titleKeys[i] = strings.Replace(titleKeys[i], queryKeys[j], "", 1)
							j = 0
						} else {
							// 子串匹配三：完全匹配
							titleTag[i] = true
							queryTag[j] = true
							break
						}
					} else {
						j++
					}
				} else {
					j++
				}
			}
			i++
		}
		for i := 0; i < len(queryTag); i++ {
			queryTag[i] = queryTag[i] || queryTagTemp[i]
		}

		for num := 0; num < 2; num++ {
			for i := 0; i < len(titleKeys); i++ {
				for j := 0; j < len(queryKeys); j++ {
					// 如果title和query的关键词还没有匹配
					if !titleTag[i] && !queryTag[j] {
						intersection := MatchingIntersection(queryKeys[j], titleKeys[i])
						// 判断交集的大小是否超过阈值
						if intersection >= int(math.Ceil(math.Min(float64(len(titleKeys[i])), float64(len(queryKeys[j])))*0.6/3)) {
							// 计算子串匹配的比例
							substringRatio := float64(intersection) / float64(math.Max(float64(len(queryKeys[j])), float64(len(titleKeys[i])))) * 3
							// 根据匹配得分计算最终得分
							wordsScore += math.Min(titleValues[i], queryValues[j]) * substringRatio
							// 标记已经匹配过的关键词
							queryTag[j] = true
							titleTag[i] = true
						}
					}
				}
			}
			if num == 0 {
				for i := 0; i < len(queryTag); i++ {
					queryTag[i] = queryTag[i] || queryTagTemp[i]
				}

				for i := 0; i < len(titleTag); i++ {
					titleTag[i] = titleTag[i] || titleTagTemp[i]
				}
				// 根据阈值合并查询和标题中的关键词和分数
				queryKeys, queryValues, queryTag = MergeWordsWithTag(queryKeys, queryValues, queryTag, 0.2)
				titleKeys, titleValues, titleTag = MergeWordsWithTag(titleKeys, titleValues, titleTag, 0.2)
			}
		}

		for i := 0; i < len(titleKeys); i++ {
			if len(titleKeys[i]) == 3 && !titleTag[i] {
				titleValues[i] /= 2
			}
		}

		for j := 0; j < len(queryKeys); j++ {
			if len(queryKeys[j]) == 3 && !queryTag[j] {
				queryValues[j] /= 2
			}
		}

		// 处理权重大于 0.05 且未匹配的词
		if wordsScore != 0 {
			queryIndexes := getIndexes(queryValues, 0.1)
			if len(queryIndexes) > 0 {
				for _, index := range queryIndexes {
					if !queryTag[index] && !strings.Contains(title, queryKeys[index]) {
						wordsScore -= queryValues[index]
						// ratio := float64(MatchingIntersection(title, queryKeys[index])) / float64(len(queryKeys[index])) * 3
						// wordsScore -= queryValues[index] * (1 - ratio)
					}
				}
			}

			titleIndexes := getIndexes(titleValues, 0.1)
			if len(titleIndexes) > 0 {
				for _, index := range titleIndexes {
					if !titleTag[index] && !strings.Contains(query, titleKeys[index]) {
						wordsScore -= titleValues[index]
					}
				}
			}
		}

		// 确保分数不会低于 0
		if wordsScore < 0 {
			wordsScore = 0
		}

		// 处理查询中最大权重的词如果未匹配
		queryMaxIndexes, err := getMaxWeightIndexes(queryValues)
		if err != nil {
			// return wordsScore, err
			t.Error(err)
		}
		for _, index := range queryMaxIndexes {
			if !queryTag[index] {
				if MatchingIntersection(queryKeys[index], title) <= len(queryKeys[index])/2 {
					wordsScore = 0
					if wordsScore > 0 {
						wordsScore = 0
					}
				}
			}
		}

		// 处理权重大于 0.1 的词
		queryIndexes := getIndexes(queryValues, 0.1)
		titleIndexes := getIndexes(titleValues, 0.1)

		// 构建临时的查询和标题字符串
		tempQuery := ""
		for _, index := range queryIndexes {
			tempQuery += queryKeys[index]
		}

		tempTitle := ""
		for _, index := range titleIndexes {
			tempTitle += titleKeys[index]
		}

		wordsScoreOrigin := wordsScore

		// 检查查询词是否匹配
		for _, index := range queryIndexes {
			if queryTag[index] { // Term with weight > 0.1 not matched
				wordsScore = wordsScoreOrigin
				break
			}
			if MatchingIntersection(tempQuery, title) <= len(tempQuery)/2 { // Not matched enough
				wordsScore = -1
			}
		}

		if wordsScore == -1 {
			for _, index := range titleIndexes {
				if titleTag[index] { // Term with weight > 0.1 not matched
					wordsScore = wordsScoreOrigin
					break
				}
				if MatchingIntersection(tempTitle, query) <= len(tempTitle)/2 { // Not matched enough
					wordsScore = -1
				}
			}
		}

	}

	e2 := common.Objects(nil)
	fmt.Println(e2 - e1)

	// return wordsScore, nil
}
