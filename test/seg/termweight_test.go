package main

import (
	"context"
	"fmt"
	"sync"
	"testing"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/global"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/service/utils"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	aseclient "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/ase_sdk/client"
)

// 定义一个 sync.Once 变量，用于确保初始化只执行一次
var initOnce sync.Once
var initErr error

func Init() error {

	initOnce.Do(func() {
		err := goboot.InitFromConfig("./config") //可以传文件夹，也可以传文件。使用默认Init(),默认获取"./config"下所有配置文件进行加载。
		if err != nil {
			panic(err)
		}

		err = goboot.RegisterCustomSingletonModule(config.G_ParamsFactory)
		if err != nil {
			initErr = err
			return
		}

		err = config.G_ParamModule.Need()
		if err != nil {
			initErr = err
			return
		}

		err = goboot.RegisterCustomSingletonModule(config.G_SegmentFactory)
		if err != nil {
			initErr = err
			return
		}

		err = config.G_SegmentModule.Need()
		if err != nil {
			initErr = err
			return
		}

		err = goboot.RegisterCustomSingletonModule(config.G_ResFactory)
		if err != nil {
			initErr = err
			return
		}

		err = config.G_ResModule.Need()
		if err != nil {
			initErr = err
			return
		}

		err = config.Init()
		if err != nil {
			initErr = err
			return
		}
	})
	return initErr
}

func TestAssembleToken_SingleTerm(t *testing.T) {
	err := Init()
	if err != nil {
		t.Fatalf("Init failed: %v", err)
	}
	global.Init()
	terms := [][]string{{"hello"}}
	tokenMap, shapeMap, err := utils.AssembleToken(terms)
	if err != nil {
		t.Fatalf("AssembleToken failed: %v", err)
	}

	fmt.Println(tokenMap)
	fmt.Println(shapeMap)
}

func TestAssembleToken_MultipleTerms(t *testing.T) {
	err := Init()
	if err != nil {
		t.Fatalf("Init failed: %v", err)
	}
	global.Init()
	terms := [][]string{
		{"hello", "world"},
		{"test", "case"},
	}
	tokenMap, shapeMap, err := utils.AssembleToken(terms)
	if err != nil {
		t.Fatalf("AssembleToken failed: %v", err)
	}

	fmt.Println(tokenMap)
	fmt.Println(shapeMap)
}

// func TestAssembleToken_VariableLengthTerms(t *testing.T) {
// 	err := Init()
// 	if err != nil {
// 		t.Fatalf("Init failed: %v", err)
// 	}
// 	global.Init()

// 	terms := [][]string{
// 		{"short"},
// 		{"longer", "phrase"},
// 		{"the", "longest", "phrase", "here"},
// 	}
// 	tokenMap, shapeMap, err := utils.AssembleToken(terms)
// 	if err != nil {
// 		t.Fatalf("AssembleToken failed: %v", err)
// 	}

// 	fmt.Println(tokenMap)
// 	fmt.Println(shapeMap)
// 	var values = make([]float64, len(terms))

// 	client := goboot.AseSdk().GetInstance(global.TermWeightVersion).Request().
// 		SetTraceId("unit_test").
// 		SetHeaderTag("A")
// 	resp, err := client.Send(context.TODO(), &aseclient.InferReq{
// 		Inputs: tokenMap,
// 		Shapes: shapeMap,
// 	})

// 	if err != nil {
// 		fmt.Printf("推理错误:%v\n", err.Error())

// 	}
// 	if resp != nil && resp.Header.Code != 0 {
// 		fmt.Printf("推理错误:%v\n", resp.Header.Success)
// 	}
// 	outputs := util.BytesToSlice[float32](resp.Payload.Outputs["term_weights"])
// 	termWeights := common.Normalize(outputs)

// 	for i := 0; i < len(terms); i++ {
// 		values[i] = common.Interface2F64(termWeights[i])
// 	}
// }

// 基准测试 utils.AssembleToken 函数，单个词项场景
// func BenchmarkAssembleToken_SingleTerm(b *testing.B) {
// 	err := Init()
// 	if err != nil {
// 		b.Fatalf("Init failed: %v", err)
// 	}
// 	for true {

// 		global.Init()
// 		terms := [][]string{{"肝胆胰", "脾", "彩色", "多普勒", "超声", "检查报告", "结果", "解读", "；"}}

// 		b.ResetTimer() // 重置计时器，忽略初始化时间
// 		for i := 0; i < b.N; i++ {
// 			_, _, err := utils.AssembleToken(terms)
// 			if err != nil {
// 				b.Fatalf("AssembleToken failed: %v", err)
// 			}
// 		}
// 	}

// }

// 基准测试 utils.AssembleToken 函数，多个词项场景
// func BenchmarkAssembleToken_MultipleTerms(b *testing.B) {
// 	err := Init()
// 	if err != nil {
// 		b.Fatalf("Init failed: %v", err)
// 	}
// 	global.Init()
// 	terms := [][]string{
// 		{"hello", "world"},
// 		{"test", "case"},
// 	}

// 	b.ResetTimer() // 重置计时器，忽略初始化时间
// 	for i := 0; i < b.N; i++ {
// 		_, _, err := utils.AssembleToken(terms)
// 		if err != nil {
// 			b.Fatalf("AssembleToken failed: %v", err)
// 		}
// 	}
// }

// // 基准测试 utils.AssembleToken 函数，可变长度词项场景
// func BenchmarkAssembleToken_VariableLengthTerms(b *testing.B) {
// 	err := Init()
// 	if err != nil {
// 		b.Fatalf("Init failed: %v", err)
// 	}
// 	global.Init()
// 	terms := [][]string{
// 		{"short"},
// 		{"longer", "phrase"},
// 		{"the", "longest", "phrase", "here"},
// 	}

// 	b.ResetTimer() // 重置计时器，忽略初始化时间
// 	for i := 0; i < b.N; i++ {
// 		_, _, err := utils.AssembleToken(terms)
// 		if err != nil {
// 			b.Fatalf("AssembleToken failed: %v", err)
// 		}
// 	}
// }

// 基准测试 aseclient 推理操作
func BenchmarkAseClientInference(b *testing.B) {
	err := Init()
	if err != nil {
		b.Fatalf("Init failed: %v", err)
	}
	global.Init()
	for true {

		terms := [][]string{{"肝胆胰", "脾", "彩色", "多普勒", "超声", "检查报告", "结果", "解读", "；"}}
		tokenMap, shapeMap, err := utils.AssembleToken(terms)
		if err != nil {
			b.Fatalf("AssembleToken failed: %v", err)
		}

		client := goboot.AseSdk().GetInstance(global.TermWeightVersion).Request().
			SetTraceId("benchmark_test").
			SetHeaderTag("A")

		b.ResetTimer() // 重置计时器，忽略初始化时间
		for i := 0; i < b.N; i++ {
			_, err := client.Send(context.TODO(), &aseclient.InferReq{
				Inputs: tokenMap,
				Shapes: shapeMap,
			})
			if err != nil {
				b.Fatalf("Inference failed: %v", err)
			}
		}
	}
}
