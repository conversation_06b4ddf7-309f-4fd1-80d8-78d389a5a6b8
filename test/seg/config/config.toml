[goboot]
enabled=true
service_name="rerank_test"

#server 配置
[[http_server]]
enabled=true
port=50903

[http_server.metrics]
url="/actuator/prometheus"

#tlb配置
[tlb_sdk]
enabled=true
servers="**************:30132"
port=50903
tlb_tag="A"

#默认日志
[[loggers]]
enabled=true
file_path="/datas/logs/goboot_test.json"

[elk]
enabled=true
name="elk_log"
log_level="info"
tags="region:hf,env:test"

[elk.local_log]
reuse="default"

[elk.kafka]
brokers = "kafka-0.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092, kafka-1.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092, kafka-2.kafka-hf04-1quuxb.svc.hfb.ipaas.cn:9092"
topic = "lynxiao_flow"

[cloud_lock]
enabled=false
# lib 库路径
lib_path = "/opt/jfs/lynxiao-deploy/lynxiao/auth_demo/python-SDK/libdongle.so"
# 授权文件路径
auth_file_path = "/opt/jfs/lynxiao-deploy/lynxiao/dongle"
# 产品名称
product_name = "Lynxiao"
# 版本号，必须是三位，eg：1.0.0
version = "2.1.0"
# 授权码，hasp授权时为空字符串
author_code = ""

[segment]
enabled=true
name = "segment"
segmentPath = "/data/lynxiao/lynxiao-ai-search/resource/rerank/data/dict/s_1.txt"
stopWordsPath = "/data/lynxiao/lynxiao-ai-search/resource/rerank/data/dict/stop_word.txt"
ccModPath = "/data/lynxiao/lynxiao-ai-search/resource/rerank/data/opencc/"
segmentPaths = ["/data/lynxiao/lynxiao-ai-search/resource/rerank/data/dict/biochemical.txt","/data/lynxiao/lynxiao-ai-search/resource/rerank/data/dict/gse.txt","/data/lynxiao/lynxiao-ai-search/resource/rerank/data/dict/ICD-10.txt","/data/lynxiao/lynxiao-ai-search/resource/rerank/data/dict/medicine.txt"]

[res]
enabled=true
name = "res"
resBaseDir = "/data/lynxiao/lynxiao-ai-search/resource/rerank"
stwPath = "/data/lynxiao/lynxiao-ai-search/resource/rerank/stopwords.txt"
medicalDomainPath = "/data/lynxiao/lynxiao-ai-search/resource/rerank/medical_domain.txt"

[params]
enabled=true
name="params"
healthLevel = "L06"
domain2baikeWeight = {"201"=15.0}
commonLevel = "L03"
sportsLevel = "L09"

[[tokenizers]]
enabled = true
name = "Xenova-ernie-3.0"
path = "/data/lynxiao/lynxiao-ai-search/resource/tokenizer/Xenova-ernie-3.0-nano-zh/tokenizer.json"
count = 3
use_local = 1
pad_id = 0
pad_fixed = 0
max_length = 512
lib = "/data/lynxiao/lynxiao-ai-search/resource/tokenizer/libtokenizer_wrapper.so"

[[ases]]
enabled = true
name = "termweight_v20231221"
tlb_server = "ai-termweight-v2"
local_url = "**************:32171/termweight_v20231221"
#0 ase 模式，1 tlb 模式, 2 ip 地址模式
mode = 1
timeout_mills = 2000

[[ants_pools]]
enabled = true
name="default"
goroutine_num=8192

