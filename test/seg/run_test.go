package main

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"testing"

	test2 "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/test"
)

func TestSegV2(t *testing.T) {
	requestData := "{\"header\":{\"traceId\":\"e5cb771a-3759-11ee-8dbe-fa163ef10010\"},\"payload\":{\"openWeight\":true,\"query\":[\"小学信息科技学科跨学科融合举例子\"]}}"
	// 发送请求
	resp, err := http.Post(test2.SegApiV2, "application/json", bytes.NewBuffer([]byte(requestData)))
	if err != nil {
		fmt.Println("发送请求失败:", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println("响应状态码:", resp.StatusCode)
	fmt.Println("响应内容:", string(body))
}
