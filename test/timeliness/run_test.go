package main

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"testing"

	test2 "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/test"
)

func TestTimelinessApiV2(t *testing.T) {
	requestData := "{\"header\":{\"traceId\":\"ase0dd87a1c802\",\"dataType\":0},\"payload\":{\"model\":\"V20240911\",\"texts\":[{\"query\":\"北京欢迎您\",\"domain\":5102}]}}"

	// 发送请求
	resp, err := http.Post(test2.TimelinessApiV2, "application/json", bytes.NewBuffer([]byte(requestData)))
	if err != nil {
		fmt.Println("发送请求失败:", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println("响应状态码:", resp.StatusCode)
	fmt.Println("响应内容:", string(body))
}
