package prerank_test

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"testing"

	test2 "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/test"
)

func TestPrerank(t *testing.T) {
	requestData := `{"header":{"traceId":"01150510"},"payload":{"topK":20,"threshold":0.01,"model":"prerank_v20241212","mergeMode":"none","rankExpr":"Truncate(Sprintf(\"%s %s\", title, ss[0]), 512)","data":[{"query":"胃管的作用是什么","docs":[{"id":8139008775942700664,"title":"食管有什么作用","ss":["食管的作用是通过食物，食物在口腔里咀嚼，然后通过食管进入胃部，进入下一阶段的消化和吸收。","72分 及格 。根据往年分数线 ，二级建造师专业实务的及格 "]}]}]}}`

	// 发送请求
	resp, err := http.Post(test2.PrerankApiV2, "application/json", bytes.NewBuffer([]byte(requestData)))
	if err != nil {
		fmt.Println("发送请求失败:", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println("响应状态码:", resp.StatusCode)
	fmt.Println("响应内容:", string(body))
}
