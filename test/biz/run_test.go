package main

import (
	"bytes"
	"fmt"
	"io"
	"net/http"
	"testing"

	test2 "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/test"
)

func TestDomainApiV2(t *testing.T) {
	requestData := "{\"header\":{\"traceId\":\"ase0dd87a1c802-1\"},\"payload\":{\"limit\":5,\"business\":\"lynxiao_flow\",\"query\":[\"今天的热搜新闻\"],\"pipeline\":\"pl_map_agg_search_medical\"}}"

	// 发送请求
	resp, err := http.Post(test2.BizApiV2, "application/json", bytes.NewBuffer([]byte(requestData)))
	if err != nil {
		fmt.Println("发送请求失败:", err)
		return
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		t.Fatal(err)
	}

	fmt.Println("响应状态码:", resp.StatusCode)
	fmt.Println("响应内容:", string(body))
}
