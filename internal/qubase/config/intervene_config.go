package config

import goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"

/*
[intervene]
serviceName = "XXX"
wordsPath = "XXX"
*/
type configWrapper struct {
	IConf InterveneConfig `toml:"qubase_intervene"`
}

type InterveneConfig struct {
	Open        bool   `toml:"open"`
	ServiceName string `toml:"serviceName"`
	WordsPath   string `toml:"wordsPath"`
	TreePath    string `toml:"treePath"`
	Tricker     int    `toml:"tricker"`
}

var G_IConfig *InterveneConfig

func Init() error {

	icw := &configWrapper{}
	err := goboot.UnmarshalConf(icw)
	if err != nil {
		return err
	}

	G_IConfig = &icw.IConf
	return nil
}
