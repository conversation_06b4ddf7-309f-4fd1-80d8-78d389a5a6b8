package qubase

import (
	"fmt"
	"time"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/qubase/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/qubase/global"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/qubase/service"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/qubase/service/intervene"
	proto_qubase "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/qubase"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/elk"
	http_server "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/http_server"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

func pandoraProcess(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_qubase.ProtoQubaseAPIV2RequestPayload]) (resp *pandora_proto.PandoraResponseMessage[proto_qubase.ProtoQubaseAPIV2ResponsePayload]) {
	service := &service.QueryParseService{
		Resp: proto_qubase.ProtoQubaseAPIV2.NewPandoraResponseMessage(),
		Ctx:  ctx,
		Req:  req,
	}
	service.HttpHandler()
	return service.Resp
}

func pandoraForceUpdateProcess(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_qubase.GetRespPayLoad]) (resp *pandora_proto.PandoraResponseMessage[proto_qubase.GetRespPayLoad]) {
	service := &service.ForceUpdateService{
		Resp: proto_qubase.ProtoQubaseForceUpdateAPIV2.NewPandoraResponseMessage(),
		Ctx:  ctx,
	}
	service.ForceUpdateHandler()
	return service.Resp
}

func startInterveneScheduler() {
	ticker := time.NewTicker(time.Duration(config.G_IConfig.Tricker) * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			err := intervene.QubaseIntervene.InitIntervene()
			if err != nil {
				goboot.Elklog().DefaultInstance().Error(global.InterveneTraceId,
					elk.Type(global.InterveneTraceId),
					elk.Data(map[string]any{"msg": fmt.Sprintf("Auto update intervene data error: %s, addr: %s!!!!", err.Error())}))
				http_server.Metrics().StatsErrors(goboot.BootConf().DefaultConfig().ServiceName, config.G_IConfig.WordsPath, "GET", selferrors.QuBaseError_InterveneError.Code(), 1)
			} else {
				http_server.Metrics().StatsErrors(goboot.BootConf().DefaultConfig().ServiceName, config.G_IConfig.WordsPath, "GET", 0, 1)
			}
		}
	}
}

func Init() error {
	err := config.Init()
	if err != nil {
		return err
	}

	if config.G_IConfig.Open {
		err = intervene.QubaseIntervene.InitIntervene()
		if err != nil {
			goboot.Elklog().DefaultInstance().Error(global.InterveneTraceId,
				elk.Type(global.InterveneTraceId),
				elk.Data(map[string]any{"msg": fmt.Sprintf("Auto update intervene data error: %s", err.Error())}))
			http_server.Metrics().StatsErrors(goboot.BootConf().DefaultConfig().ServiceName, config.G_IConfig.WordsPath, "GET", selferrors.QuBaseError_InterveneError.Code(), 1)
			return err
		} else {
			http_server.Metrics().StatsErrors(goboot.BootConf().DefaultConfig().ServiceName, config.G_IConfig.WordsPath, "GET", 0, 1)
		}

		go startInterveneScheduler()
	}

	router := goboot.HttpServer().DefaultInstance().Router
	v1 := router.Group("/qubase")
	{
		v1.POST("/api/v2", proto_qubase.ProtoQubaseAPIV2.GinWrapper().SetHandler(pandoraProcess).HandlerFunc())
		v1.GET("/api/v2/forceUpdate", proto_qubase.ProtoQubaseForceUpdateAPIV2.GinWrapper().SetHandler(pandoraForceUpdateProcess).HandlerFunc())
	}

	return nil
}
