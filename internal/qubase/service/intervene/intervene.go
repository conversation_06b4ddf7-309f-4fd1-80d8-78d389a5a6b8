package intervene

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/qubase/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/qubase/global"

	proto_qubase "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/qubase"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	concurrent_map "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/concurrent/map"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

type Intervene struct {
	RewriteMap        map[string]*concurrent_map.ConcurrentMap[string, string]
	RewriteEnabledMap map[string]bool
	RewriteListMap    map[string]*[]string
	CorrectMap        map[string]*concurrent_map.ConcurrentMap[string, string]
	CorrectEnabledMap map[string]bool
	CorrectListMap    map[string]*[]string
	Code2Obj          map[string][]byte
	SynonymCodes      []string
	CorrectCodes      []string
}

func NewIntervene() *Intervene {
	return &Intervene{
		RewriteMap:        make(map[string]*concurrent_map.ConcurrentMap[string, string]),
		RewriteEnabledMap: make(map[string]bool),
		RewriteListMap:    make(map[string]*[]string),
		CorrectMap:        make(map[string]*concurrent_map.ConcurrentMap[string, string]),
		CorrectEnabledMap: make(map[string]bool),
		CorrectListMap:    make(map[string]*[]string),
		Code2Obj:          make(map[string][]byte),
	}
}

var (
	QubaseIntervene = NewIntervene()
	WordTreeClient  = pandora.NewPandoraProto[proto_qubase.SynonymTreePayload, proto_qubase.SynonymTreePayload]()
	WordInfoClient  = pandora.NewPandoraProto[proto_qubase.WordsPayLoad, proto_qubase.WordsPayLoad]()
)

func (r *Intervene) InitIntervene() error {
	r.SynonymCodes = make([]string, 0)
	r.CorrectCodes = make([]string, 0)

	if err := r.InitInterveneRewriteFromDict(); err != nil {
		return fmt.Errorf("failed to init rewrite intervene: %w", err)
	}

	if err := r.InitInterveneCorrectFromDict(); err != nil {
		return fmt.Errorf("failed to init correct intervene: %w", err)
	}

	return nil
}

func (r *Intervene) compareSerializedObjects(obj1, obj2 interface{}) bool {
	serialized1, err1 := json.Marshal(obj1)
	if err1 != nil {
		return false
	}
	serialized2, err2 := json.Marshal(obj2)
	if err2 != nil {
		return false
	}
	return string(serialized1) == string(serialized2)
}

func (r *Intervene) fetchCodes(name string) ([]string, error) {
	response, err := WordTreeClient.Request().
		SetServerName(config.G_IConfig.ServiceName).
		SetHeaderTag(goboot.TlbSdk().DefaultInstance().Conf.TlbTag).
		SetPath(config.G_IConfig.TreePath).
		SetTraceId(global.InterveneTraceId).
		Get(context.TODO())

	if err != nil {
		return nil, fmt.Errorf("failed to get tree info from %s,%w", config.G_IConfig.ServiceName, err)
	}

	if response != nil && response.Header.Code != 0 {
		return nil, fmt.Errorf("failed to get tree info from %s, response code is:%d", config.G_IConfig.ServiceName, response.Header.Code)
	}

	if name == global.SynonymName {
		r.extractCodes(response, name)
		return r.SynonymCodes, nil
	} else if name == global.CorrectName {
		r.extractCodes(response, name)
		return r.CorrectCodes, nil
	}

	return nil, fmt.Errorf("unknown name: %s", name)
}

func (r *Intervene) extractCodes(response *pandora_proto.PandoraResponseMessage[proto_qubase.SynonymTreePayload], name string) {
	for _, item := range response.Payload.Data {
		if item.Name == name {
			r.parseItem(item, name)
		}
	}
}

func (r *Intervene) parseItem(item proto_qubase.Child, name string) {
	if item.Enabled {
		if name == global.SynonymName {
			r.SynonymCodes = append(r.SynonymCodes, item.Code)
			r.RewriteEnabledMap[item.Code] = true
		} else if name == global.CorrectName {
			r.CorrectCodes = append(r.CorrectCodes, item.Code)
			r.CorrectEnabledMap[item.Code] = true
		}
	} else {
		if name == global.SynonymName {
			r.RewriteEnabledMap[item.Code] = false
		} else if name == global.CorrectName {
			r.CorrectEnabledMap[item.Code] = false
		}
	}

	for _, child := range item.Children {
		r.parseItem(child, name)
	}
}

func (r *Intervene) initInterveneFromDict(name string) error {
	codes, err := r.fetchCodes(name)
	if err != nil {
		return fmt.Errorf("failed to fetch codes: %w", err)
	}

	apiWordsURL := config.G_IConfig.WordsPath
	for _, code := range codes {
		wordURL := strings.ReplaceAll(apiWordsURL, "{code}", code)
		response, err := WordInfoClient.Request().
			SetServerName(config.G_IConfig.ServiceName).
			SetHeaderTag(goboot.TlbSdk().DefaultInstance().Conf.TlbTag).
			SetPath(wordURL).
			SetTraceId(global.InterveneTraceId).
			Get(context.TODO())

		if err != nil {
			return fmt.Errorf("failed to get wordsDetail info from %s,%w", config.G_IConfig.ServiceName, err)
		}

		if response != nil && response.Header.Code != 0 {
			return fmt.Errorf("failed to get tree info from %s, response code is:%d", config.G_IConfig.ServiceName, response.Header.Code)
		}

		obj, err := json.Marshal(response.Payload.Data)
		if err != nil {
			return fmt.Errorf("failed to marshal word data: %w", err)
		}

		if value, ok := r.Code2Obj[code]; ok && r.compareSerializedObjects(value, obj) {
			continue
		}

		r.Code2Obj[code] = obj
		goboot.Logger().DefaultInstance().Info(fmt.Sprintf("%s resource for code %s updated", name, code))

		var rawList []string
		wordMap := concurrent_map.New[string, string]()
		for _, d := range response.Payload.Data {
			if len(d.RelationWords) > 0 {
				wordMap.Set(d.Word, strings.Split(d.RelationWords, ",")[0])
				rawList = append(rawList, d.Word)
			}

			for _, word := range strings.Split(d.MutualRelationWords, ",") {
				if len(word) > 0 {
					wordMap.Set(word, d.Word)
					rawList = append(rawList, word)
				}
			}
		}

		if name == global.SynonymName {
			r.RewriteMap[code] = wordMap
			r.RewriteListMap[code] = &rawList
		} else if name == global.CorrectName {
			r.CorrectMap[code] = wordMap
			r.CorrectListMap[code] = &rawList
		}
	}
	return nil
}

func (r *Intervene) InitInterveneRewriteFromDict() error {
	return r.initInterveneFromDict(global.SynonymName)
}

func (r *Intervene) InitInterveneCorrectFromDict() error {
	return r.initInterveneFromDict(global.CorrectName)
}
