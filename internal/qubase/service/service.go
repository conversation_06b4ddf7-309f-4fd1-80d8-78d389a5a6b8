package service

import (
	"fmt"
	"sync"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/qubase/global"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/qubase/service/intervene"
	proto_qubase "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/qubase"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	concurrent_map "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/concurrent/map"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
	"golang.org/x/sync/errgroup"
)

type QueryParseService struct {
	Ctx  *pandora_context.PandoraContext
	Resp *pandora_proto.PandoraResponseMessage[proto_qubase.ProtoQubaseAPIV2ResponsePayload]
	Req  *pandora_proto.PandoraRequestMessage[proto_qubase.ProtoQubaseAPIV2RequestPayload]
}

func (s *QueryParseService) checkRequest() *errtypes.SelfError {
	if len(s.Req.Header.TraceId) == 0 {
		return selferrors.CommonError_MissingField.Detaild(fmt.Sprintf("%s: traceId is empty", goboot.BootConf().DefaultInstance().Conf.ServiceName))
	}
	if len(s.Req.Payload.Texts) == 0 {
		return selferrors.CommonError_InvalidInput.Detaild(fmt.Sprintf("%s: empty input texts", goboot.BootConf().DefaultInstance().Conf.ServiceName))
	}
	for _, text := range s.Req.Payload.Texts {
		if len(text) == 0 {
			return selferrors.CommonError_InvalidInput.Detaild(fmt.Sprintf("%s: empty input texts", goboot.BootConf().DefaultInstance().Conf.ServiceName))
		}
	}
	return nil
}

func (s *QueryParseService) parse() {
	parseSpan := s.Ctx.RootSpan().AddSpan("Query Parser")
	defer func() {
		parseSpan.Finish()
		parseSpan.TraceInfo("requestInfo", s.Req)
		parseSpan.TraceInfo("responseInfo", s.Resp)
	}()

	var (
		errGroup errgroup.Group
		mu       sync.Mutex
		results  = make([][]*proto_qubase.ProtoQubaseAPIV2Result, len(s.Req.Payload.Texts))
	)

	if err := s.checkRequest(); err != nil {
		s.setErrorResponse(err)
		return
	}

	for idx := range s.Req.Payload.Texts {
		idx := idx // 捕获局部变量
		rawQuery := s.Req.Payload.Texts[idx]

		errGroup.Go(func() error {
			parseResults := make([]*proto_qubase.ProtoQubaseAPIV2Result, 0, 3)
			parseResults = append(parseResults, newRawResult(rawQuery))

			parseResults, err := s.processStrategies(parseSpan, rawQuery, parseResults)
			if err != nil {
				return err
			}

			mu.Lock()
			results[idx] = parseResults
			mu.Unlock()
			return nil
		})
	}

	if err := errGroup.Wait(); err != nil {
		s.setErrorResponse(selferrors.QuBaseError_InternelError.Detaild(err.Error()))
		return
	}

	s.Resp.Payload.Results = flattenResults(results)
}

func (s *QueryParseService) processStrategies(span *span.Span, rawQuery string, parseResults []*proto_qubase.ProtoQubaseAPIV2Result) ([]*proto_qubase.ProtoQubaseAPIV2Result, error) {
	var err error
	currentQuery := rawQuery
	// 处理纠错策略
	currentQuery, parseResults, err = s.handleStrategy(
		span,
		s.Req.Payload.CorrectStrategy,
		intervene.QubaseIntervene.CorrectEnabledMap,
		intervene.QubaseIntervene.CorrectMap,
		global.Correct,
		CorrectInst.Exec,
		currentQuery,
		parseResults,
	)
	if err != nil {
		return parseResults, fmt.Errorf("correct strategy failed: %w", err)
	}

	// 处理改写策略
	currentQuery, parseResults, err = s.handleStrategy(
		span,
		s.Req.Payload.RewriteStrategy,
		intervene.QubaseIntervene.RewriteEnabledMap,
		intervene.QubaseIntervene.RewriteMap,
		global.Rewrite,
		RewriteInst.Exec,
		currentQuery,
		parseResults,
	)
	if err != nil {
		return parseResults, fmt.Errorf("rewrite strategy failed: %w", err)
	}

	return parseResults, nil
}

func (s *QueryParseService) handleStrategy(
	span *span.Span,
	strategies []string,
	enabledMap map[string]bool,
	strategyMap map[string]*concurrent_map.ConcurrentMap[string, string],
	resultType string,
	executor func(string, string, *concurrent_map.ConcurrentMap[string, string], string) (string, []*proto_qubase.ProtoQubaseAPIV2ExtraInfo, *errtypes.SelfError),
	currentQuery string,
	parseResults []*proto_qubase.ProtoQubaseAPIV2Result,
) (string, []*proto_qubase.ProtoQubaseAPIV2Result, error) {
	handlerSpan := span.AddSpan(resultType)
	defer handlerSpan.Finish()

	for _, code := range strategies {
		// 兼容初版5001策略，后续逐步替换
		if code == global.HealthRewriteStrategy {
			code = "synonym_word"
		}
		if !enabledMap[code] {
			continue
		}

		strategyData, ok := strategyMap[code]
		if !ok {
			continue
		}

		text, extra, err := executor(currentQuery, currentQuery, strategyData, code)
		if err != nil {
			return "", parseResults, fmt.Errorf("strategy %s error: %v", code, err)
		}
		if len(extra) == 0 {
			continue
		}

		parseResults = append(parseResults, &proto_qubase.ProtoQubaseAPIV2Result{
			Query: text,
			Type:  resultType,
			Extra: extra,
		})
		return text, parseResults, nil // 仅处理第一个有效策略
	}
	return currentQuery, parseResults, nil
}

func newRawResult(rawQuery string) *proto_qubase.ProtoQubaseAPIV2Result {
	return &proto_qubase.ProtoQubaseAPIV2Result{
		Query: rawQuery,
		Type:  global.Raw,
		Extra: []*proto_qubase.ProtoQubaseAPIV2ExtraInfo{},
	}
}

func flattenResults(results [][]*proto_qubase.ProtoQubaseAPIV2Result) []*proto_qubase.ProtoQubaseAPIV2Result {
	flattened := make([]*proto_qubase.ProtoQubaseAPIV2Result, 0)
	for _, r := range results {
		flattened = append(flattened, r...)
	}
	return flattened
}

func (s *QueryParseService) setErrorResponse(err *errtypes.SelfError) {
	s.Resp.Header.Code = err.Code()
	s.Resp.Header.Success = err.String()
}

func (s *QueryParseService) HttpHandler() {
	s.parse()
}
