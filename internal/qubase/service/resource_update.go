package service

import (
	"fmt"
	"time"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/qubase/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/qubase/global"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/qubase/service/intervene"
	proto_qubase "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/qubase"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/elk"
	http_server "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/http_server"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

type ForceUpdateService struct {
	Ctx  *pandora_context.PandoraContext
	Resp *pandora_proto.PandoraResponseMessage[proto_qubase.GetRespPayLoad]
}

func genTimeStamp() int64 {
	// 获取当前时间
	now := time.Now()
	// 将当前时间转换为 Unix 时间戳（秒级）
	seconds := now.Unix()
	// 将秒级时间戳转换为毫秒级时间戳
	milliseconds := seconds * 1000
	return milliseconds
}

func (s *ForceUpdateService) ForceUpdateHandler() {
	err := intervene.QubaseIntervene.InitIntervene()
	if err != nil {
		goboot.Elklog().DefaultInstance().Error(global.InterveneTraceId,
			elk.Type(global.InterveneTraceId),
			elk.Data(map[string]any{"msg": fmt.Sprintf("Auto update intervene data error: %s", err.Error())}))
		http_server.Metrics().StatsErrors(goboot.BootConf().DefaultConfig().ServiceName, config.G_IConfig.WordsPath, "GET", selferrors.QuBaseError_InterveneError.Code(), 1)
		s.Resp.Header.Code = selferrors.QuBaseError_InterveneError.Code()
		s.Resp.Header.Success = selferrors.QuBaseError_InterveneError.String()
	} else {
		http_server.Metrics().StatsErrors(goboot.BootConf().DefaultConfig().ServiceName, config.G_IConfig.WordsPath, "GET", 0, 1)
	}

	s.Resp.Payload = proto_qubase.GetRespPayLoad{
		Timestamp: genTimeStamp(),
	}
}
