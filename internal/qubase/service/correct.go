package service

import (
	"strings"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/qubase/service/intervene"
	proto_qubase "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/qubase"
	concurrent_map "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/concurrent/map"
)

var CorrectInst = new(Correct)

type Correct struct {
}

func (correct *Correct) Exec(rawQuery, query string, dict *concurrent_map.ConcurrentMap[string, string], code string) (text string, extra []*proto_qubase.ProtoQubaseAPIV2ExtraInfo, Err *errtypes.SelfError) {
	// 1. 提前校验并获取替换词表
	replacements, exists := intervene.QubaseIntervene.CorrectListMap[code]
	if !exists || len(*replacements) == 0 {
		return "", nil, nil
	}

	// 2. 初始化返回值并预分配内存
	extras := make([]*proto_qubase.ProtoQubaseAPIV2ExtraInfo, 0, len(*replacements))

	// 3. 单次遍历完成所有替换
	for _, target := range *replacements {
		if replacement, ok := dict.Get(target); ok && strings.Contains(rawQuery, target) {
			query = strings.ReplaceAll(query, target, replacement)
			extras = append(extras, createExtraInfo(target, replacement, rawQuery))
		}
	}

	return query, extras, nil
}

func createExtraInfo(raw, replace, query string) *proto_qubase.ProtoQubaseAPIV2ExtraInfo {
	return &proto_qubase.ProtoQubaseAPIV2ExtraInfo{
		Raw:     raw,
		Replace: replace,
		Query:   query,
	}
}
