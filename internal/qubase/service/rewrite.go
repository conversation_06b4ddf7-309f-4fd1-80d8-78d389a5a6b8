package service

import (
	"strings"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/qubase/service/intervene"
	proto_qubase "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/qubase"
	concurrent_map "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/concurrent/map"
)

var RewriteInst = new(Rewrite)

type Rewrite struct {
}

func (r *Rewrite) Exec(rawQuery, query string, dict *concurrent_map.ConcurrentMap[string, string], code string) (text string, extra []*proto_qubase.ProtoQubaseAPIV2ExtraInfo, Err *errtypes.SelfError) {
	// 1.获取候选词表时即进行安全断言
	rawWords, exists := intervene.QubaseIntervene.RewriteListMap[code]
	if !exists || len(*rawWords) == 0 {
		return
	}

	// 2.核心算法：寻找最优匹配词
	selectedWord, found := findOptimalWord(rawQuery, *rawWords)
	if !found {
		return
	}

	// 3.字典取值与结果组装
	if replaceValue, ok := dict.Get(selectedWord); ok {
		return assembleResult(rawQuery, selectedWord, replaceValue)
	}

	return
}

// findOptimalWord 实现O(n)级高效查找算法
func findOptimalWord(rawQuery string, candidates []string) (string, bool) {
	var (
		earliestIndex = len(rawQuery) // 初始化为最大可能值
		bestWord      string
		found         bool
	)

	for _, word := range candidates {
		if idx := strings.Index(rawQuery, word); idx != -1 {
			switch {
			case idx < earliestIndex: // 更靠左的候选词
				earliestIndex, bestWord, found = idx, word, true
			case idx == earliestIndex && len(word) < len(bestWord): // 相同位置更短
				bestWord = word
			}
		}
	}
	return bestWord, found
}

// assembleResult 预分配内存+批量赋值（避免append开销）
func assembleResult(rawQuery, rawWord, replace string) (string, []*proto_qubase.ProtoQubaseAPIV2ExtraInfo, *errtypes.SelfError) {
	return strings.ReplaceAll(rawQuery, rawWord, replace),
		[]*proto_qubase.ProtoQubaseAPIV2ExtraInfo{ // 预分配单元素slice
			{
				Raw:     rawWord,
				Replace: replace,
				Query:   rawQuery,
			},
		},
		nil
}
