package config

import (
	"encoding/json"
	"fmt"
	"log"
	"os"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/biz/model"
)

func LoadServiceConfig(path string) (*model.ServiceConfig, error) {
	// 添加配置文件路径检查
	if path == "" {
		return nil, fmt.Errorf("config path is empty")
	}

	configData, err := os.ReadFile(path)
	if err != nil {
		log.Fatalf("Failed to read file: %v", err)
		return nil, err
	}

	config := model.ServiceConfig{
		Timeout:         5000,
		DefaultPipeline: "pl_map_agg_search_medical",
	}
	err = json.Unmarshal(configData, &config)
	if err != nil {
		log.Fatalf("Failed to unmarshal Config JSON: %v", err)
		return nil, err
	}

	// 添加配置有效性验证
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("invalid config: %v", err)
	}

	return &config, nil
}

func validateConfig(config *model.ServiceConfig) error {
	if config.Host == "" {
		return fmt.Errorf("biz host is empty")
	}
	if config.AppID == "" {
		return fmt.Errorf("biz appid is empty")
	}
	if config.APISecret == "" {
		return fmt.Errorf("biz apisecret is empty")
	}
	if config.APIKey == "" {
		return fmt.Errorf("biz apikey is empty")
	}
	return nil
}
