package biz

import (
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/biz/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/biz/model"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/biz/service"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
)

func Init() error {
	// 解析自定义toml配置块
	modelConfig := &model.ModelConfig{}

	err := goboot.UnmarshalConf(modelConfig)
	if err != nil {
		return err
	}

	// 读取自定义业务配置
	service.Gservice = &service.Service{}

	c, err := config.LoadServiceConfig(modelConfig.Config.ConfigPath)
	if err != nil {
		return err
	}
	service.Gservice.Config = c

	service.Gservice.RegisterRouter()

	return nil
}
