package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/biz/model"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/biz/utils"
	proto_biz "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/biz"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

func (s *Service) jsonUnmarshal(out []byte, req *pandora_proto.PandoraRequestMessage[model.Request]) error {
	req.Payload = model.Request{
		Limit:      5,
		FullText:   true,
		OpenRerank: true,
		Highlight:  false,
		Crawler:    false,
		Business:   "lynxiao-flow",
		Pipeline:   s.Config.DefaultPipeline,
	}

	return json.Unmarshal(out, req)
}

type Service struct {
	Config *model.ServiceConfig
}

// 读取配置
var Gservice *Service

func (s *Service) RegisterRouter() {
	router := goboot.HttpServer().DefaultInstance().Router

	// 保留biz-search/api/v2，兼容老版本
	// 后续希望保留biz/api/v2
	router.POST("/biz-search/api/v2", proto_biz.ProtoBizAPIV2.GinWrapper().SetHandler(s.BizProcess).SetRequestUnmarshal(s.jsonUnmarshal).HandlerFunc())
	router.POST("/biz/api/v2", proto_biz.ProtoBizAPIV2.GinWrapper().SetHandler(s.BizProcess).SetRequestUnmarshal(s.jsonUnmarshal).HandlerFunc())

}

func (s *Service) BizProcess(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[model.Request]) *pandora_proto.PandoraResponseMessage[model.Response] {
	span := ctx.RootSpan().AddSpan("pandora_process")
	defer span.Finish()

	appidSpan := ctx.RootSpan().AddSpan(req.Header.AppId)
	defer appidSpan.Finish()

	var selfErr *errtypes.SelfError
	resp := proto_biz.ProtoBizAPIV2.NewPandoraResponseMessage()
	resp.Header.TraceId = req.Header.TraceId

	defer func() {
		if selfErr != nil {
			span.TraceInfo("selfError", selfErr)
			resp.Header.Code = selfErr.Code()
			resp.Header.Success = selfErr.Error()
		}

	}()

	// 参数校验
	if selfErr = s.ValidateParameters(req); selfErr != nil {
		return resp
	}

	// 执行处理
	resp.Payload.Results, selfErr = s.executeWithTimeout(ctx, req)

	return resp
}

// 执行带有超时的函数
func (s *Service) executeWithTimeout(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[model.Request]) ([]*model.BizSearchData, *errtypes.SelfError) {

	// 创建一个带有超时的上下文
	ctxTimeout, cancel := context.WithTimeout(ctx.GetContext(), time.Duration(s.Config.Timeout)*time.Millisecond)
	defer cancel()
	var results []*model.BizSearchData

	// 遍历请求中的查询，将查询结果添加到结果列表中
	// 外部预制结果是为了后续超时能返回已经有的结果
	// 预制超时，内部处理错误码覆盖，成功也覆盖success
	for _, query := range req.Payload.Query {
		results = append(results, &model.BizSearchData{
			Query: query,
			Code:  selferrors.BizSearchError_Timeout.Code(),
			Msg:   selferrors.BizSearchError_Timeout.Error(),
		})
	}

	f, err := goboot.AntsPool().DefaultInstance().Submit(func() (any, error) {
		s.Processor(ctx, req, results)
		return nil, nil
	})

	if err != nil {
		// 返回内部错误
		return nil, selferrors.CommonError_InternalError.Detaild(err.Error())
	}
	// 选择返回结果或超时
	select {
	case <-f.Wait():
		return results, nil
	case <-ctxTimeout.Done():
		return results, nil
	}
}

// 定义一个名为Processor的方法，该方法属于Service结构体
func (s *Service) Processor(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[model.Request], results []*model.BizSearchData) {
	span := ctx.RootSpan().AddSpan("processor_biz")
	defer span.Finish()
	// 这儿未加锁，for循环内是对独立的*model.BizSearchData进行操作，不会有并发问题

	var sg sync.WaitGroup
	for _, result := range results {
		sg.Add(1)
		go func(searchData *model.BizSearchData) {
			defer sg.Done()
			spanChild := span.AddSpan("processor_biz_child")
			defer spanChild.Finish()
			jhrequest := &model.BizRequest{
				AuditQuestionDesc: "{\"suggest\":\"pass\",\"content\":\"\",\"system\":\"\",\"seq\":0,\"category_list\":[],\"request_id\":\"T202409191420011197d18ef88306000\",\"sid\":\"e266c1e507d54d4b92f6b3084dea37dd\",\"error_msg\":\"\",\"audit_type\":\"\",\"safe_classification\":\"13b_safe_model\"}",
				PipelineName:      req.Payload.Pipeline,
				Sid:               req.Header.TraceId,
				UID:               "",
				AppID:             s.Config.AppID,
				Limit:             strconv.Itoa(req.Payload.Limit),
				Business:          req.Payload.Business,
				Name:              searchData.Query,
				DisableHighlight:  !req.Payload.Highlight,
				OpenRerank:        req.Payload.OpenRerank,
				FullText:          req.Payload.FullText,
				DisableCrawler:    !req.Payload.Crawler,
				Timestamp:         time.Now().Unix(),
			}

			var serr *errtypes.SelfError
			defer func() {
				if serr != nil {
					searchData.Code = serr.Code()
					searchData.Msg = serr.Error()
				}
			}()

			// 生成请求数据
			spanChild.TraceInfo("request", jhrequest)
			jhRequestData, err := json.Marshal(jhrequest)
			if err != nil {
				serr = selferrors.BizSearchError_JsonMarshal.Detaild(err.Error())
				return
			}
			authRequestURL, jhdata, headers := utils.Prepare(s.Config.Host, jhRequestData, "POST", s.Config.AppID, s.Config.APIKey, s.Config.APISecret)
			spanChild.TraceInfo("authRequestURL", authRequestURL)
			spanChild.TraceInfo("data", jhdata)
			spanChild.TraceInfo("headers", headers)
			if authRequestURL == "" || jhdata == "" || headers == nil {
				serr = selferrors.BizSearchError_RequestFailed.Detaild("authRequestURL or jhdata or headers is empty")
				return
			}

			// 发送请求
			jhresponseData, err := utils.Execute(req.Header.TraceId, authRequestURL, jhdata, headers)
			if err != nil {
				serr = selferrors.BizSearchError_CalcFailed.Detaild(err.Error())
				return
			}
			spanChild.TraceInfo("response", jhresponseData)

			// 解析响应数据
			var jhResponseData model.BizResponse
			err = json.Unmarshal(jhresponseData, &jhResponseData)
			if err != nil {
				serr = selferrors.BizSearchError_ResponseDecode.Detaild(err.Error())
				return
			}

			// 验证响应数据
			if jhResponseData.ErrCode != "0" {
				serr = selferrors.BizSearchError_CodeFailed.Detaild(fmt.Sprintf("response code is not 0 , is %v", jhResponseData.ErrCode))
				return
			}

			// 将响应数据转换为结果数据
			resultItems := []*model.BizSearchItem{}
			for _, result := range jhResponseData.Data.Documents {
				resultItems = append(resultItems, &model.BizSearchItem{
					Title:   result.Name,
					Summary: result.Summary,
					Content: result.Content,
					Url:     result.URL,
					Score:   result.FinalScore,
				})
			}
			searchData.Data = resultItems
			serr = selferrors.SuccessTypes_Success
		}(result)
	}

	// 去除channel，直接routing内部处理

	sg.Wait()

}

// ValidateParameters 验证请求参数
func (s *Service) ValidateParameters(req *pandora_proto.PandoraRequestMessage[model.Request]) *errtypes.SelfError {

	// 如果请求参数中的查询为空，则返回错误
	if len(req.Payload.Query) == 0 {
		return selferrors.CommonError_InvalidInput.Detaild("queries is empty")
	}

	// 如果请求参数中的管道为空，则使用默认管道
	if req.Payload.Pipeline == "" {
		req.Payload.Pipeline = s.Config.DefaultPipeline
	}

	// 返回nil表示验证通过
	return nil
}
