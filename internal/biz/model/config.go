package model


type ModelConfig struct {
	Config BizConfig `toml:"biz"`
}

type BizConfig struct {
	ConfigPath string `toml:"config_path"`
}

type ServiceConfig struct {
	Host            string `json:"host"`
	AppID           string `json:"appId"`     // 统一使用 ID 大写
	APIKey          string `json:"apiKey"`    // API 大写
	APISecret       string `json:"apiSecret"` // API 大写
	Timeout         int    `json:"timeout"`
	DefaultPipeline string `json:"defaultPipeline"`
}
