package model

type BizRequest struct {
	AuditQuestionDesc string `json:"auditQuestionDesc"`
	DisableCrawler    bool   `json:"disable_crawler"`
	PipelineName      string `json:"pipeline_name"`
	Sid               string `json:"sid"`
	UID               string `json:"uId"`   // 统一大写缩写
	AppID             string `json:"appId"` // 统一使用 ID 大写
	Limit             string `json:"limit"`
	Business          string `json:"business"`
	Name              string `json:"name"`
	DisableHighlight  bool   `json:"disable_highlight"`
	OpenRerank        bool   `json:"open_rerank"`
	FullText          bool   `json:"full_text"`
	Timestamp         int64  `json:"timestamp"`
}

type BizResponse struct {
	Success bool   `json:"success"`
	ErrCode string `json:"err_code"`
	Message string `json:"err_message"`
	Data    Data   `json:"data"`
}

type Data struct {
	ClassifyDomain []string   `json:"classify_domain"`
	Documents      []Document `json:"documents"`
	MoreDocuments  []Document `json:"more_documents"`
	Timeliness     []string   `json:"timeliness"`
	Sid            string     `json:"sid"`
}

type Document struct {
	Summary         string  `json:"summary"`
	Img             string  `json:"img"`
	FrScore         float64 `json:"fr_score"`
	TimeWeightScore float64 `json:"time_weight_score"`
	Source          string  `json:"source"`
	TimeScore       float64 `json:"time_score"`
	Content         string  `json:"content"`
	URL             string  `json:"url"`
	SiteName        string  `json:"site_name"`
	SiteWeightScore float64 `json:"site_weight_score"`
	FinalScore      float64 `json:"final_score"`
	URLRepeatScore  float64 `json:"url_repeat_score"`
	DailyDiff       int64   `json:"daily_diff"`
	Name            string  `json:"name"`
	PublishedDate   *string `json:"published_date,omitempty"`
}
