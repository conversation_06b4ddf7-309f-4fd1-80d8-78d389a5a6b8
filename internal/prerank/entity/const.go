package entity

const (
	// MergeRewrite 合并模式-改写
	MergeRewrite string = "rewrite"

	// TypeRaw 原始
	TypeRaw string = "raw"
	// TypeRewritten 改写
	TypeRewritten string = "rewritten"

	// ID id
	ID string = "id"
	// TYPE 类型
	TYPE string = "_type"
	// PrerankIndex 索引
	PrerankIndex string = "_prerank_index"
	// PrerankScore 分数
	PrerankScore string = "_prerank_score"
	// SpanIDs 标签
	SpanIDs string = "_spanIds"

	// InputIds 模型输入token
	InputIds string = "input_ids"
	// AttentionMask 模型输入mask
	AttentionMask string = "attention_mask"
	// TokenType 模型输入
	TokenType string = "token_type_ids"
	// LogitsName 模型输出
	LogitsName string = "output"

	// KeyTag tlbTag
	KeyTag string = "tlb_tag"
)
