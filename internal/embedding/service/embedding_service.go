package service

import (
	"fmt"
	"math"

	"golang.org/x/sync/errgroup"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"github.com/samber/lo"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/embedding/entity"
	proto_embedding "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/embedding"
	pandora_span "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	util "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/data_tool"
	aseclient "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/ase_sdk_v2/client"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
)

type service struct {
	defaultModel string
	aseMap       map[string]*entity.ModelConfig
}

// Service 向量服务单例
var Service = new(service)

// 初始化服务
func (s *service) Init() error {
	models := goboot.GetCustomModule(entity.ModelFactory).GetAllConfig()
	s.aseMap = make(map[string]*entity.ModelConfig, len(models))
	for _, model := range models {
		// ase配置
		s.aseMap[model.UName] = model
		if model.Default {
			s.defaultModel = model.UName
		}
	}

	return nil
}

// 向量计算请求
func (s *service) Process(ctx *pandora_context.PandoraContext, span *pandora_span.Span, req *proto_embedding.ReqPayload) (resp *proto_embedding.RespPayload, err error) {
	processSpan := span.AddSpan("embedding")
	defer func() {
		if err != nil {
			processSpan.TraceInfo("Embedding process failed: ", err.Error())
		}
		processSpan.Finish()
	}()

	// 参数不传，走默认值
	if req.Model == "" {
		req.Model = s.defaultModel
	}

	// 根据模型获取ase配置
	aseConf, ok := s.aseMap[req.Model]
	if !ok {
		return nil, selferrors.EmbeddingError_ModelNotSupport.Detaild(fmt.Sprintf("invalid model: %s", req.Model))
	}

	inferSpan := span.AddSpan("embedding-infer")
	// 按照指定batch_size重新划分doc
	var g errgroup.Group
	textsBatch := lo.Chunk(req.Texts, aseConf.BatchSize)
	resBatch := make([][][]float32, len(textsBatch))

	for batchID, texts := range textsBatch {
		g.Go(func() error {
			// 调用模型推理
			client := goboot.AseSdkV2().GetInstance(req.Model).Request().SetParentSpan(span).SetTraceId(ctx.TraceId)
			resp, err := client.Send(ctx.GetContext(), &aseclient.InferReq{
				Queries: texts,
			})

			if err != nil {
				return selferrors.EmbeddingError_AseRequestFailed.Detaild(err.Error())
			}

			// 结果处理
			logits := util.BytesToSlice[float32](resp.Payload.Outputs[entity.Logits])

			results := lo.Chunk(logits, aseConf.ResDim)
			resBatch[batchID] = results

			return nil
		})
	}

	if err = g.Wait(); err != nil {
		return nil, err
	}
	inferSpan.Finish()

	// batch-merge
	l2Span := span.AddSpan("batch-merge")
	embedding := make([][]float32, 0, len(req.Texts))
	for _, batch := range resBatch {
		embedding = append(embedding, batch...)
	}
	l2Span.Finish()

	return &proto_embedding.RespPayload{Model: req.Model, Embedding: embedding}, nil
}

// L2Normalization l2归一化
func L2Normalization(data []float32) []float32 {
	var sum float32
	for _, v := range data {
		sum += v * v
	}
	sum = float32(math.Sqrt(float64(sum)))
	for i := range data {
		data[i] /= sum
	}
	return data
}
