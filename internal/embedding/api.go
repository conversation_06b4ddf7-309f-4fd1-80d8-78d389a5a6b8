package embedding

import (
	"fmt"
	"time"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/embedding/entity"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/embedding/service"
	proto_embedding "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/embedding"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
	"github.com/go-playground/validator/v10"
)

type embedding struct {
	validate *validator.Validate
}

func (r *embedding) EmbeddingHandler(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_embedding.ReqPayload]) *pandora_proto.PandoraResponseMessage[proto_embedding.RespPayload] {
	now := time.Now()
	span := ctx.RootSpan().AddSpan("embedding handler")
	defer span.Finish()

	resp := proto_embedding.EmbeddingAPI.NewPandoraResponseMessage()

	// 请求参数校验
	if err := r.validate.Struct(req); err != nil {
		return fillHeader(resp, selferrors.CommonError_InvalidInput.Detaild(err.Error()))
	}

	AppidSpan := ctx.RootSpan().AddSpan(req.Header.AppId)
	defer AppidSpan.Finish()

	// 调用prerank服务
	respPayload, err := service.Service.Process(ctx, span, &req.Payload)
	if err != nil {
		if selfError, ok := err.(*errtypes.SelfError); ok {
			return fillHeader(resp, selfError)
		}
		return fillHeader(resp, selferrors.EmbeddingError_InferFailed.Detaild(err.Error()))
	}

	resp.Payload = *respPayload

	resp.Header.Success = fmt.Sprintf("success, cost: %dms", time.Since(now).Milliseconds())
	return resp
}

func fillHeader(resp *pandora_proto.PandoraResponseMessage[proto_embedding.RespPayload], err *errtypes.SelfError) *pandora_proto.PandoraResponseMessage[proto_embedding.RespPayload] {
	resp.Header.Code = err.Code()
	resp.Header.Success = err.Error()
	return resp
}

// Init 服务初始化
func Init() error {
	// 添加自定义配置
	if err := goboot.RegisterCustomMultiModule(entity.ModelFactory); err != nil {
		panic(fmt.Sprintf("register custom module failed, error: %s", err.Error()))
	}

	// 检查自定义配置是否正确加载
	goboot.GetCustomModule(entity.ModelFactory).Must()

	// asesdk依赖是否加载
	goboot.AseSdkV2().Must()

	// 服务初始化
	if err := service.Service.Init(); err != nil {
		return err
	}

	embeddingInst := &embedding{
		validate: validator.New(validator.WithRequiredStructEnabled()),
	}

	//注册服务
	goboot.HttpServer().DefaultInstance().Router.POST("/embedding/api/v2", proto_embedding.EmbeddingAPI.GinWrapper().SetHandler(embeddingInst.EmbeddingHandler).HandlerFunc())

	return nil
}
