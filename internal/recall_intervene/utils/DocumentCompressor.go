package utils

import (
	"bytes"
	"compress/gzip"
	"encoding/binary"
	"fmt"
	"io"
	"log"
	"strings"

	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

// DEFAULT_ARRAY_COMPRESSION_THRESHOLD 默认数组压缩阈值
const DEFAULT_ARRAY_COMPRESSION_THRESHOLD = 200

// DEFAULT_STRING_COMPRESSION_THRESHOLD 默认字符串压缩阈值
const DEFAULT_STRING_COMPRESSION_THRESHOLD = 100

// LIMIT_COMPRESSION_THRESHOLD 压缩阈值下限
const LIMIT_COMPRESSION_THRESHOLD = 100

// GZIP_SUFFIX GZIP 后缀
const GZIP_SUFFIX = "_gzip"

// STRING_SUFFIX 字符串压缩后缀
const STRING_SUFFIX = "_str" + GZIP_SUFFIX

// DOUBLE_ARRAY_SUFFIX 双精度浮点数数组压缩后缀
const DOUBLE_ARRAY_SUFFIX = "_da" + GZIP_SUFFIX

// FLOAT_ARRAY_SUFFIX 单精度浮点数数组压缩后缀
const FLOAT_ARRAY_SUFFIX = "_fa" + GZIP_SUFFIX

// INT_ARRAY_SUFFIX 整数数组压缩后缀
const INT_ARRAY_SUFFIX = "_ia" + GZIP_SUFFIX

// LONG_ARRAY_SUFFIX 长整型数组压缩后缀
const LONG_ARRAY_SUFFIX = "_la" + GZIP_SUFFIX

// DocumentCompressor 文档压缩器结构体
type DocumentCompressor struct {
	arrayCompressionThreshold  int
	stringCompressionThreshold int
}

var defaultCompressor = NewDocumentCompressor(DEFAULT_ARRAY_COMPRESSION_THRESHOLD, DEFAULT_STRING_COMPRESSION_THRESHOLD)

// NewDocumentCompressor 创建一个新的文档压缩器实例
func NewDocumentCompressor(arrayThreshold, stringThreshold int) *DocumentCompressor {
	return &DocumentCompressor{
		arrayCompressionThreshold:  arrayThreshold,
		stringCompressionThreshold: stringThreshold,
	}
}

// GetDefault 获取默认的文档压缩器实例
func GetDefault() *DocumentCompressor {
	return defaultCompressor
}

// Compress 压缩单个文档
func (dc *DocumentCompressor) Compress(document map[string]any) map[string]any {
	if dc.arrayCompressionThreshold < LIMIT_COMPRESSION_THRESHOLD || document == nil {
		log.Printf("Skipping compression: threshold %d < limit %d or document is null", dc.arrayCompressionThreshold, LIMIT_COMPRESSION_THRESHOLD)
		return document
	}

	log.Printf("Starting document compression with array len threshold=%d; string len threshold=%d", dc.arrayCompressionThreshold, dc.stringCompressionThreshold)
	result := make(map[string]any, len(document))

	for key, value := range document {
		// 如果 key 以 "_gzip" 结尾，则不进行压缩处理
		if strings.HasSuffix(key, GZIP_SUFFIX) {
			result[key] = value
			continue
		}

		switch v := value.(type) {
		case string:
			if len(v) > dc.stringCompressionThreshold {
				log.Printf("Compressing string field '%s' with length %d", key, len(v))
				compressed, err := compressBytes([]byte(v))
				if err != nil {
					log.Printf("Error compressing string field '%s': %v", key, err)
					result[key] = v
				} else {
					result[key+STRING_SUFFIX] = compressed
				}
			} else {
				result[key] = v
			}
		case []float64:
			if len(v) > dc.arrayCompressionThreshold {
				// log.Printf("Compressing double array field '%s' with length %d", key, len(v))
				binaryData := doubleArrayToBinary(v)
				compressed, err := compressBytes(binaryData)
				if err != nil {
					log.Printf("Error compressing double array field '%s': %v", key, err)
					result[key] = v
				} else {
					result[key+DOUBLE_ARRAY_SUFFIX] = compressed
				}
			} else {
				result[key] = v
			}
		case []float32:
			if len(v) > dc.arrayCompressionThreshold {
				// log.Printf("Compressing float array field '%s' with length %d", key, len(v))
				binaryData := floatArrayToBinary(v)
				compressed, err := compressBytes(binaryData)
				if err != nil {
					log.Printf("Error compressing float array field '%s': %v", key, err)
					result[key] = v
				} else {
					result[key+FLOAT_ARRAY_SUFFIX] = compressed
				}
			} else {
				result[key] = v
			}
		case []int:
			if len(v) > dc.arrayCompressionThreshold {
				// log.Printf("Compressing int array field '%s' with length %d", key, len(v))
				binaryData := intArrayToBinary(v)
				compressed, err := compressBytes(binaryData)
				if err != nil {
					log.Printf("Error compressing int array field '%s': %v", key, err)
					result[key] = v
				} else {
					result[key+INT_ARRAY_SUFFIX] = compressed
				}
			} else {
				result[key] = v
			}
		case []int64:
			if len(v) > dc.arrayCompressionThreshold {
				// log.Printf("Compressing long array field '%s' with length %d", key, len(v))
				binaryData := longArrayToBinary(v)
				compressed, err := compressBytes(binaryData)
				if err != nil {
					log.Printf("Error compressing long array field '%s': %v", key, err)
					result[key] = v
				} else {
					result[key+LONG_ARRAY_SUFFIX] = compressed
				}
			} else {
				result[key] = v
			}
		case []any:
			if len(v) > 0 && len(v) > dc.arrayCompressionThreshold {
				// log.Printf("Compressing list field '%s' with size %d", key, len(v))
				compressList(key, v, result, dc)
			} else {
				result[key] = compressNestedList(v, dc)
			}
		case map[string]any:
			result[key] = dc.Compress(v)
		default:
			result[key] = value
		}
	}
	// log.Println("Document compression completed")
	return result
}

// compressList 压缩列表
func compressList(key string, list []any, result map[string]any, dc *DocumentCompressor) {
	if len(list) == 0 {
		result[key] = list
		return
	}

	first := list[0]
	switch first.(type) {
	case float64:
		// log.Printf("Compressing double list '%s' to array", key)
		array := make([]float64, len(list))
		for i, item := range list {
			array[i] = item.(float64)
		}
		binaryData := doubleArrayToBinary(array)
		compressed, err := compressBytes(binaryData)
		if err != nil {
			log.Printf("Error compressing double list '%s': %v", key, err)
			result[key] = list
		} else {
			result[key+DOUBLE_ARRAY_SUFFIX] = compressed
		}
	case float32:
		// log.Printf("Compressing float list '%s' to array", key)
		array := make([]float32, len(list))
		for i, item := range list {
			array[i] = item.(float32)
		}
		binaryData := floatArrayToBinary(array)
		compressed, err := compressBytes(binaryData)
		if err != nil {
			log.Printf("Error compressing float list '%s': %v", key, err)
			result[key] = list
		} else {
			result[key+FLOAT_ARRAY_SUFFIX] = compressed
		}
	case int:
		// log.Printf("Compressing integer list '%s' to array", key)
		array := make([]int, len(list))
		for i, item := range list {
			array[i] = item.(int)
		}
		binaryData := intArrayToBinary(array)
		compressed, err := compressBytes(binaryData)
		if err != nil {
			log.Printf("Error compressing integer list '%s': %v", key, err)
			result[key] = list
		} else {
			result[key+INT_ARRAY_SUFFIX] = compressed
		}
	case int64:
		// log.Printf("Compressing long list '%s' to array", key)
		array := make([]int64, len(list))
		for i, item := range list {
			array[i] = item.(int64)
		}
		binaryData := longArrayToBinary(array)
		compressed, err := compressBytes(binaryData)
		if err != nil {
			log.Printf("Error compressing long list '%s': %v", key, err)
			result[key] = list
		} else {
			result[key+LONG_ARRAY_SUFFIX] = compressed
		}
	default:
		result[key] = list
	}
}

// compressNestedList 压缩嵌套列表
func compressNestedList(list []any, dc *DocumentCompressor) []any {
	newList := make([]any, len(list))
	for i, item := range list {
		if m, ok := item.(map[string]any); ok {
			newList[i] = dc.Compress(m)
		} else {
			newList[i] = item
		}
	}
	return newList
}

// Decompress 解压单个文档
func (dc *DocumentCompressor) Decompress(document map[string]any) map[string]any {
	if document == nil {
		log.Println("Skipping decompression: document is null")
		return nil
	}

	// log.Println("Starting document decompression")
	result := make(map[string]any, len(document))

	for key, value := range document {
		if strings.HasSuffix(key, STRING_SUFFIX) {
			// log.Printf("Decompressing string field '%s'", key)
			decompressed, err := decompressValue(key, value)
			if err != nil {
				log.Printf("Error decompressing string field '%s': %v", key, err)
				result[key] = value
			} else {
				result[strings.TrimSuffix(key, STRING_SUFFIX)] = decompressed
			}
		} else if strings.HasSuffix(key, DOUBLE_ARRAY_SUFFIX) {
			// log.Printf("Decompressing double array field '%s'", key)
			decompressed, err := decompressValue(key, value)
			if err != nil {
				log.Printf("Error decompressing double array field '%s': %v", key, err)
				result[key] = value
			} else {
				result[strings.TrimSuffix(key, DOUBLE_ARRAY_SUFFIX)] = decompressed
			}
		} else if strings.HasSuffix(key, FLOAT_ARRAY_SUFFIX) {
			// log.Printf("Decompressing float array field '%s'", key)
			decompressed, err := decompressValue(key, value)
			if err != nil {
				log.Printf("Error decompressing float array field '%s': %v", key, err)
				result[key] = value
			} else {
				result[strings.TrimSuffix(key, FLOAT_ARRAY_SUFFIX)] = decompressed
			}
		} else if strings.HasSuffix(key, INT_ARRAY_SUFFIX) {
			// log.Printf("Decompressing int array field '%s'", key)
			decompressed, err := decompressValue(key, value)
			if err != nil {
				log.Printf("Error decompressing int array field '%s': %v", key, err)
				result[key] = value
			} else {
				result[strings.TrimSuffix(key, INT_ARRAY_SUFFIX)] = decompressed
			}
		} else if strings.HasSuffix(key, LONG_ARRAY_SUFFIX) {
			// log.Printf("Decompressing long array field '%s'", key)
			decompressed, err := decompressValue(key, value)
			if err != nil {
				log.Printf("Error decompressing long array field '%s': %v", key, err)
				result[key] = value
			} else {
				result[strings.TrimSuffix(key, LONG_ARRAY_SUFFIX)] = decompressed
			}
		} else if m, ok := value.(map[string]any); ok {
			result[key] = dc.Decompress(m)
		} else if l, ok := value.([]any); ok {
			result[key] = decompressNestedList(l, dc)
		} else if bin, ok := value.(primitive.Binary); ok {
			// 获取 Binary 数据

			var res map[string]any
			// 将 Binary 数据解析为 BSON
			err := bson.Unmarshal(bin.Data, &res)
			if err != nil {
				log.Printf("Error unmarshalling primitive.Binary field '%s': %v", key, err)
			}
			// 这里可以根据需求处理 binaryData
			// 例如，假设是字符串类型的二进制数据，可以进行解压操作
			result[key] = dc.Decompress(res)

		} else if A, ok := value.(primitive.A); ok {
			// 获取 Binary 数据

			result[key] = decompressNestedList(A, dc)
		} else {
			result[key] = value
		}
	}
	// log.Println("Document decompression completed")
	return result
}

// decompressValue 解压值
func decompressValue(key string, value any) (any, error) {
	var binaryData []byte
	switch v := value.(type) {
	case []byte:
		binaryData = v
	case primitive.Binary:
		binaryData = v.Data
	default:
		return nil, fmt.Errorf("unsupported value type for decompression: %T", value)
	}

	decompressed, err := decompressBytes(binaryData)
	if err != nil {
		return nil, err
	}

	if strings.HasSuffix(key, STRING_SUFFIX) {
		return string(decompressed), nil
	} else if strings.HasSuffix(key, DOUBLE_ARRAY_SUFFIX) {
		return binaryToDoubleArray(decompressed)
	} else if strings.HasSuffix(key, FLOAT_ARRAY_SUFFIX) {
		return binaryToFloatArray(decompressed)
	} else if strings.HasSuffix(key, INT_ARRAY_SUFFIX) {
		return binaryToIntArray(decompressed)
	} else if strings.HasSuffix(key, LONG_ARRAY_SUFFIX) {
		return binaryToLongArray(decompressed)
	}
	return nil, fmt.Errorf("unknown suffix for decompression: %s", key)
}

// decompressNestedList 解压嵌套列表
func decompressNestedList(list []any, dc *DocumentCompressor) []any {
	newList := make([]any, len(list))
	for i, item := range list {
		if m, ok := item.(map[string]any); ok {
			newList[i] = dc.Decompress(m)
		} else {
			newList[i] = item
		}
	}
	return newList
}

// compressBytes 使用 GZIP 压缩字节数据
func compressBytes(data []byte) ([]byte, error) {
	var b bytes.Buffer
	gz := gzip.NewWriter(&b)
	if _, err := gz.Write(data); err != nil {
		return nil, err
	}
	if err := gz.Close(); err != nil {
		return nil, err
	}
	return b.Bytes(), nil
}

// decompressBytes 使用 GZIP 解压字节数据
func decompressBytes(data []byte) ([]byte, error) {
	b := bytes.NewBuffer(data)
	gz, err := gzip.NewReader(b)
	if err != nil {
		return nil, err
	}
	defer gz.Close()
	return io.ReadAll(gz)
}

// doubleArrayToBinary 将双精度浮点数数组转换为字节数组
func doubleArrayToBinary(arr []float64) []byte {
	buf := new(bytes.Buffer)
	for _, v := range arr {
		if err := binary.Write(buf, binary.BigEndian, v); err != nil {
			log.Printf("Error converting double array to binary: %v", err)
			return nil
		}
	}
	return buf.Bytes()
}

// floatArrayToBinary 将单精度浮点数数组转换为字节数组
func floatArrayToBinary(arr []float32) []byte {
	buf := new(bytes.Buffer)
	for _, v := range arr {
		if err := binary.Write(buf, binary.BigEndian, v); err != nil {
			log.Printf("Error converting float array to binary: %v", err)
			return nil
		}
	}
	return buf.Bytes()
}

// intArrayToBinary 将整数数组转换为字节数组
func intArrayToBinary(arr []int) []byte {
	buf := new(bytes.Buffer)
	for _, v := range arr {
		if err := binary.Write(buf, binary.BigEndian, int32(v)); err != nil {
			log.Printf("Error converting int array to binary: %v", err)
			return nil
		}
	}
	return buf.Bytes()
}

// longArrayToBinary 将长整型数组转换为字节数组
func longArrayToBinary(arr []int64) []byte {
	buf := new(bytes.Buffer)
	for _, v := range arr {
		if err := binary.Write(buf, binary.BigEndian, v); err != nil {
			log.Printf("Error converting long array to binary: %v", err)
			return nil
		}
	}
	return buf.Bytes()
}

// binaryToDoubleArray 将字节数组转换为双精度浮点数数组
func binaryToDoubleArray(data []byte) ([]float64, error) {
	buf := bytes.NewReader(data)
	var result []float64
	for buf.Len() >= 8 {
		var val float64
		if err := binary.Read(buf, binary.BigEndian, &val); err != nil {
			return nil, err
		}
		result = append(result, val)
	}
	return result, nil
}

// binaryToFloatArray 将字节数组转换为单精度浮点数数组
func binaryToFloatArray(data []byte) ([]float32, error) {
	buf := bytes.NewReader(data)
	var result []float32
	for buf.Len() >= 4 {
		var val float32
		if err := binary.Read(buf, binary.BigEndian, &val); err != nil {
			return nil, err
		}
		result = append(result, val)
	}
	return result, nil
}

// binaryToIntArray 将字节数组转换为整数数组
func binaryToIntArray(data []byte) ([]int, error) {
	buf := bytes.NewReader(data)
	var result []int
	for buf.Len() >= 4 {
		var val int32
		if err := binary.Read(buf, binary.BigEndian, &val); err != nil {
			return nil, err
		}
		result = append(result, int(val))
	}
	return result, nil
}

// binaryToLongArray 将字节数组转换为长整型数组
func binaryToLongArray(data []byte) ([]int64, error) {
	buf := bytes.NewReader(data)
	var result []int64
	for buf.Len() >= 8 {
		var val int64
		if err := binary.Read(buf, binary.BigEndian, &val); err != nil {
			return nil, err
		}
		result = append(result, val)
	}
	return result, nil
}
