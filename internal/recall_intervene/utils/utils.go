package utils

import (
	"encoding/json"
	"fmt"
	"strconv"

	"go.mongodb.org/mongo-driver/bson"
)

func Interface2S(value any) (result string) {
	if value == nil {
		return ""
	}

	switch value := value.(type) {

	case []byte: // 取缔 []uint8 --> string
		result = string(value)

	case int64:
		result = strconv.FormatInt(value, 10)

	case float64:
		result = strconv.FormatFloat(value, 'f', -1, 64)

	case json.Number:
		result = value.String()

	case string:
		result = value

	default:
		newValue, _ := json.Marshal(value)
		result = string(newValue)
	}

	return
}

func If[T any](condition bool, trueVal, falseVal T) T {
	if condition {
		return trueVal
	}
	return falseVal
}

func Interface2I32(value any) int32 {
	switch v := value.(type) {
	case int:
		return int32(v)
	case int8:
		return int32(v)
	case int16:
		return int32(v)
	case int32:
		return v
	case int64:
		return int32(v)
	case uint:
		return int32(v)
	case uint8:
		return int32(v)
	case uint16:
		return int32(v)
	case uint32:
		return int32(v)
	case uint64:
		return int32(v)
	case float32:
		return int32(v)
	case float64:
		return int32(v)
	case string:
		if i, err := strconv.Atoi(v); err == nil {
			return int32(i)
		}
	case json.Number:
		parsedValue, err := v.Int64()
		if err != nil {
			fmt.Println("Error parsing json.Number to int64:", err)
			return 0
		}
		return int32(parsedValue)

	default:
		fmt.Printf("unsupported type: %T\n", v)
	}
	return 0
}

// ConvertFullWidthToHalfWidth converts full-width characters to half-width characters.
func ConvertFullWidthToHalfWidth(input string) string {
	output := []rune{}
	for _, r := range input {
		if r >= '！' && r <= '～' { // Full-width ASCII range
			r -= 0xFEE0
		} else if r == '　' { // Full-width space
			r = ' '
		}
		output = append(output, r)
	}
	return string(output)
}

func BsonToMap(data bson.M) (map[string]any, error) {
	// 将 BSON 转为 JSON
	jsonBytes, err := json.Marshal(data)
	if err != nil {
		return nil, err
	}

	// 将 JSON 解析为 map[string]any
	var result map[string]any
	if err := json.Unmarshal(jsonBytes, &result); err != nil {
		return nil, err
	}
	return result, nil
}
