package recall_intervene

import (
	"fmt"
	"time"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/intervene"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/service"
	"github.com/bytedance/sonic"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/global"
	proto_intervene "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/recall_intervene"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/elk"
	http_server "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/http_server"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

var sonicAPI sonic.API

func sonicUnmarshal(out []byte, req *pandora_proto.PandoraRequestMessage[proto_intervene.ProtoInterveneAPIV2RequestPayload]) error {
	// 自定义反序列化逻辑
	return sonicAPI.Unmarshal(out, req)
}

func pandoraProcess(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_intervene.ProtoInterveneAPIV2RequestPayload]) (resp *pandora_proto.PandoraResponseMessage[proto_intervene.ProtoInterveneAPIV2ResponsePayload]) {
	service := &service.Service{
		Resp: proto_intervene.ProtoInterveneAPIV2.NewPandoraResponseMessage(),
		Ctx:  ctx,
		Req:  req,
	}
	service.HttpHandler()
	return service.Resp
}

func pandoraForceUpdateProcess(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_intervene.GetRespPayLoad]) (resp *pandora_proto.PandoraResponseMessage[proto_intervene.GetRespPayLoad]) {
	service := &service.ForceUpdateService{
		Resp: proto_intervene.ProtoInterveneForceUpdateAPIV2.NewPandoraResponseMessage(),
		Ctx:  ctx,
	}
	service.ForceUpdateHandler()
	return service.Resp
}

func startInterveneScheduler() {
	ticker := time.NewTicker(time.Duration(config.G_Config.IConf.Tricker) * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			err := intervene.InterveneInst.LoadPatternFromApi()
			if err != nil {
				goboot.Elklog().DefaultInstance().Error(global.TraceId,
					elk.Type(global.TraceId),
					elk.Data(map[string]any{"msg": fmt.Sprintf("Auto update intervene data error: %s, addr: %s!!!!", err.Error())}))
				http_server.Metrics().StatsErrors(goboot.BootConf().DefaultConfig().ServiceName, config.G_Config.IConf.WordsPath, "GET", selferrors.RecallInterveneError_InterveneError.Code(), 1)
			} else {
				http_server.Metrics().StatsErrors(goboot.BootConf().DefaultConfig().ServiceName, config.G_Config.IConf.WordsPath, "GET", 0, 1)
			}
		}
	}
}

func Init() error {
	err := config.Init()
	if err != nil {
		return err
	}

	sonicAPI = sonic.Config{UseNumber: true}.Froze()

	err = intervene.InterveneInst.LoadPatternFromApi()
	if err != nil {
		goboot.Elklog().DefaultInstance().Error(global.TraceId,
			elk.Type(global.TraceId),
			elk.Data(map[string]any{"msg": fmt.Sprintf("Auto update intervene data error: %s", err.Error())}))
		http_server.Metrics().StatsErrors(goboot.BootConf().DefaultConfig().ServiceName, config.G_Config.IConf.WordsPath, "GET", selferrors.RecallInterveneError_InterveneError.Code(), 1)
		return err
	} else {
		fmt.Println("干预资源加载成功")
		http_server.Metrics().StatsErrors(goboot.BootConf().DefaultConfig().ServiceName, config.G_Config.IConf.WordsPath, "GET", 0, 1)
	}

	go startInterveneScheduler()
	sonicAPI = sonic.Config{UseNumber: true}.Froze()

	router := goboot.HttpServer().DefaultInstance().Router
	v1 := router.Group("/intervene")
	{
		v1.POST("/api/v2", proto_intervene.ProtoInterveneAPIV2.GinWrapper().SetHandler(pandoraProcess).SetRequestUnmarshal(sonicUnmarshal).HandlerFunc())
		v1.GET("/api/v2/forceUpdate", proto_intervene.ProtoInterveneForceUpdateAPIV2.GinWrapper().SetHandler(pandoraForceUpdateProcess).HandlerFunc())
	}

	return nil
}
