package intervene

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"sync"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/global"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/utils"
	proto_intervene "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/recall_intervene"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
)

type Intervene struct {
	mu sync.Mutex
	// 通用句式
	CommonSentencePattern []string
	// 泛化句式
	GeneralSentencePattern []string
	// 通用句式
	CommonPatternMap map[string]*regexp.Regexp
	// 泛化句式
	GeneralPatternMap map[string]*regexp.Regexp
}

func NewIntervene() *Intervene {
	return &Intervene{
		mu:                     sync.Mutex{},
		CommonPatternMap:       make(map[string]*regexp.Regexp),
		GeneralPatternMap:      make(map[string]*regexp.Regexp),
		CommonSentencePattern:  []string{},
		GeneralSentencePattern: []string{},
	}
}

var InterveneInst = NewIntervene()

var (
	WordInfoClient = pandora.NewPandoraProto[proto_intervene.WordsPayLoad, proto_intervene.WordsPayLoad]()
)

func (s *Intervene) LoadPatternFromApi() error {
	tmpPatterns, err := s.loadSentenceByCode(utils.If(config.G_Config.IConf.CommonSentencePattern != "",
		config.G_Config.IConf.CommonSentencePattern, global.CommonSentencePattern))
	if err != nil {
		return err
	}
	tmpPatternMap, err := s.reCompile(tmpPatterns)
	if err != nil {
		return err
	}
	s.mu.Lock()
	s.CommonPatternMap = tmpPatternMap
	s.mu.Unlock()
	s.mu.Lock()
	s.CommonSentencePattern = tmpPatterns
	s.mu.Unlock()

	tmpPatterns, err = s.loadSentenceByCode(utils.If(config.G_Config.IConf.GeneralSentencePattern != "",
		config.G_Config.IConf.GeneralSentencePattern, global.GeneralSentencePattern))
	if err != nil {
		return err
	}
	tmpPatternMap, err = s.reCompile(tmpPatterns)
	if err != nil {
		return err
	}

	s.mu.Lock()
	s.GeneralPatternMap = tmpPatternMap
	s.mu.Unlock()
	s.mu.Lock()
	s.GeneralSentencePattern = tmpPatterns
	s.mu.Unlock()
	return nil
}

func (s *Intervene) reCompile(patterns []string) (map[string]*regexp.Regexp, error) {
	var patternMap = make(map[string]*regexp.Regexp)
	for _, pattern := range patterns {
		re, err := regexp.Compile(pattern)
		if err != nil {
			return patternMap, err
		}
		patternMap[pattern] = re
	}
	return patternMap, nil
}

func (s *Intervene) loadSentenceByCode(code string) ([]string, error) {
	var patterns = []string{}
	response, err := WordInfoClient.Request().
		SetServerName(config.G_Config.IConf.ServiceName).
		SetPath(strings.ReplaceAll(config.G_Config.IConf.WordsPath, "{code}", code)).
		SetTraceId(global.TraceId).
		SetHeaderTag(goboot.TlbSdk().DefaultInstance().Conf.TlbTag).
		Get(context.TODO())

	if err != nil {
		return patterns, err
	}

	if response != nil && response.Header.Code != 0 {
		return patterns, fmt.Errorf("资源不存在")
	}

	for _, data := range response.Payload.Data {
		patterns = append(patterns, data.Word)
	}

	return patterns, nil
}
