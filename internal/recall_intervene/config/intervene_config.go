package config

import (
	"context"
	"fmt"
	"time"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
	"go.mongodb.org/mongo-driver/mongo/options"
)

/*
[intervene]
serviceName = "XXX"
wordsPath = "XXX"
*/
type ConfigWrapper struct {
	IConf InterveneConfig `toml:"intervene_intervene"`
	MConf MongoConfig     `toml:"intervene_mongo"`
}

type InterveneConfig struct {
	ServiceName            string `toml:"serviceName"`
	WordsPath              string `toml:"wordsPath"`
	TreePath               string `toml:"treePath"`
	Tricker                int    `toml:"tricker"`
	BKSentencePattern      string `toml:"bkSentencePattern"`
	CommonSentencePattern  string `toml:"commonSentencePattern"`
	GeneralSentencePattern string `toml:"generalSentencePattern"`
	OnlyRaw                bool   `toml:"onlyRaw"`
}

type MongoConfig struct {
	Uri        string `toml:"uri"`
	Database   string `toml:"database"`
	Collection string `toml:"collection"`
}

var G_Config *ConfigWrapper
var G_MongoClient *mongo.Client
var G_Collection string

func Init() error {

	icw := &ConfigWrapper{}
	err := goboot.UnmarshalConf(icw)
	if err != nil {
		return err
	}

	G_Config = icw
	err = InitMongo()
	if err != nil {
		return err
	}
	return nil
}

var G_mongoCollection *mongo.Collection
var G_MongoDatabase *mongo.Database

// func InitMongo() error {
// 	clientOptions := options.Client().ApplyURI(G_Config.MConf.Uri)
// 	G_MongoClient, err := mongo.NewClient(clientOptions)
// 	if err != nil {
// 		return err
// 	}

// 	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
// 	defer cancel()

// 	err = G_MongoClient.Connect(ctx)
// 	if err != nil {
// 		return err
// 	}

// 	err = G_MongoClient.Ping(ctx, nil)
// 	if err != nil {
// 		return err
// 	}

// 	G_MongoDatabase = G_MongoClient.Database(G_Config.MConf.Database)

// 	return nil
// }

func InitMongo() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 连接MongoDB
	G_MongoClient, err := mongo.Connect(ctx, options.Client().ApplyURI(G_Config.MConf.Uri))
	if err != nil {
		return err
	}
	// defer G_MongoClient.Disconnect(ctx)

	// 检查数据库是否存在
	dbExists, err := G_MongoClient.ListDatabaseNames(ctx, bson.M{"name": G_Config.MConf.Database})
	if err != nil {
		return fmt.Errorf("获取数据库列表失败:", err)
	}
	if len(dbExists) == 0 {
		return fmt.Errorf("数据库 '%s' 不存在", G_Config.MConf.Database)
	}

	// 获取数据库句柄
	G_MongoDatabase = G_MongoClient.Database(G_Config.MConf.Database)

	// 检查集合是否存在

	return nil
}
