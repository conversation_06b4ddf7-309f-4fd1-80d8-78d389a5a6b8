package global

var (
	BKSentencePattern      = "BKSentencePattern"
	CommonSentencePattern  = "Common"
	GeneralSentencePattern = "General"
)

var TraceId = "updateResource"

var (
	QTFullMatched = 0 // query和title完全匹配中
	QTXXXMatched  = 1 // query和title中词条匹配中
	QTNoMatched   = 2 // query和title无关
	QIsBaike      = 3 // query是百科词条
)

var (
	StrategyFullMatched = 0 // query和title完全匹配中
	StrategyGeneral     = 1 // 泛化句式匹配中
	StrategyCommon      = 2 // 通用句式匹配中
	StrategyQIsBaike    = 3 // query是百科词条
)

var StrategyMap = map[int]string{
	0: "query和title完全匹配",
	1: "泛化句式匹配",
	2: "通用句式匹配",
	3: "query是百科词条",
}

const (
	Raw     = "raw"
	Rewrite = "rewritten"
	Correct = "corrected"
)
