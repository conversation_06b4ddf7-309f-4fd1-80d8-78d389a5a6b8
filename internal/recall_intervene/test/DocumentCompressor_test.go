package test

import (
	"reflect"
	"strings"
	"testing"

	"go.mongodb.org/mongo-driver/bson/primitive"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/utils"
)

func TestDocumentCompressor_CompressAndDecompress(t *testing.T) {
	compressor := utils.GetDefault()
	// 准备测试文档
	document := map[string]interface{}{
		"description": strings.Repeat("这是一个很长的描述文本，用于测试文档压缩功能。", 2),
		"scores":      []float32{98.5, 92.0, 88.5},
		"tags":        []string{"tag1", "tag2", "tag3"},
		"nestedDoc": map[string]interface{}{
			"innerDescription": strings.Repeat("这是嵌套文档的描述", 2),
			"innerScores":      []float64{99.0, 95.0, 93.0},
		},
	}

	// 压缩文档
	compressed := compressor.Compress(document)

	// 解压文档
	decompressed := compressor.Decompress(compressed)

	// 验证解压后的文档是否与原始文档相等
	if !reflect.DeepEqual(decompressed, document) {
		t.<PERSON><PERSON><PERSON>("Decompressed document does not match original document. Expected: %v, Got: %v", document, decompressed)
	}
}

func TestDocumentCompressor_DecompressPrimitiveBinary(t *testing.T) {
	compressor := utils.GetDefault()
	// 模拟包含 primitive.Binary 的压缩文档
	// compressedData := []byte("compressed data")
	compressedDoc := map[string]any{
		"data": primitive.Binary{
			Subtype: 0,
			Data:    []byte("H4sIAAAAAAAA/51Uy27bVhDd8yu8bDdGExdFy2ULFF32F4IgcIsiCtAH0KUsK6Yok5LsUKKitxzZku1SEhVFD+rBf0k4d8hVfqHn6jpCuu3u3Jm5Z+aembnRwowWpx/XVrTwYntEJftDOhMtgihwKN/h/oXo2WzeRruwn5+knvydevHni+NfU388Sx1/8f1fv/92+OWPhz8d/vDLsxTXsrScsHEtOkuw0Mu5aJhkVbiaFt5V3E0jjZiuAGhYY7cu4/NtUc7tUq7EZEVGAC+7BiLjuSXMV8JuR+sejiCBK86EuCjJc/cHKFZnZ4zCPqRPoiCgYkbViePnWFQMmrfUUeOypas3AWmJO9Djq3vySsqGs8bBRKeiBRsVZw+hwUTj+lCP1i3aXAFpUEbfa6SR39JVAUDw2fpDNsqd0eKUrIDabURckO3KanwfBVEpS4VQaqIshRC1cqe2x8ntP/TyrcLxdcjlqcK0DMEDzJ5JjbGG1kkK70YK8pA4MQzhzIQ7S9ypzv1z8t7EeZ/WaTJt6bMqmhhdHoh0XxduB1CLp8OD2C3qvBkm1SkZpuol9bIoMalO4npaY2dwkBhF/ejjpvXdMTttLp2xuQSL9kj2AE3sLOkix/6KGmeJcw21RK4CLNo+sGQqh49w+5unz2VT61PaZuX566fP0dW4O0iaV3wyjnul2JzgKnCyygJLnRqmqHi88jA4HLzjms/Bki4t4YyQlt0yGJLujO9HiIytAiy4RRcDqk9h4WqP8zO+O4nfzeXEbV4lhi0HbTqgZo2GrV0rPFUAr0IklU1YZRVWxfDrzadiBsnpgLZDPFc4WxpnQS4Kl5RzQS7GDly77G/j8xuqt9hpwK49lj32zkWzi3t7XZRGSi+l0eO9RtEijxcppTi4+erw2wfz+X/NRzsFtSM5IOYdFd8gQbSw436Wcq+lYpBrfUtNV9aZq6ACAPn8neZS2zJW6U5UC6K6RbwSkIp5Loyg814cTBuFp3uJpCyfNIm3S15Xxdz/XLGdjBktCvpqVldAUVD4f8uw34T9GkhXgLFzqdmVxpFFwxNpzPhkudgQOWZ9vCx4n24ocuzH+3STijdgkGzOOKlK5qRZwVHmlZ9KQ7Jt51HYVGsWLdtyiPo2vgRw/gtmQkesOQUAAA=="),
		},
	}

	// 解压文档
	decompressed := compressor.Decompress(compressedDoc)

	expected := "compressed data"
	if actual, ok := decompressed["content"].(string); !ok || actual != expected {
		t.Errorf("Decompression failed. Expected: %s, Got: %v", expected, actual)
	}
}
