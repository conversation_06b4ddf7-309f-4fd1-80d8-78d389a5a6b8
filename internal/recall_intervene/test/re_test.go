package test

import (
	"fmt"
	"regexp"
	"testing"
)

func TestPattern(t *testing.T) {
	pattern := "(.*)的定义"
	query := "人工智能的定义"

	// 编译正则表达式
	re, err := regexp.Compile(pattern)
	if err != nil {
		t.Fatalf("Failed to compile regex: %v", err)
	}

	// 查找所有匹配
	matches := re.FindAllStringSubmatch(query, -1)
	if len(matches) == 0 {
		t.Fatal("No matches found")
	}

	// 提取捕获组的内容
	var res []string
	for _, match := range matches {
		if len(match) > 1 { // match[0] 是整个匹配，match[1] 是第一个捕获组
			res = append(res, match[1])
		}
	}

	// 打印结果
	fmt.Println(res) // 输出: [人工智能]
}

func TestPatternCommon(t *testing.T) {
	pattern := "(.{1,10}?)的?定义$"
	query := "血液病的定义"

	// 编译正则表达式
	re, err := regexp.Compile(pattern)
	if err != nil {
		t.Fatalf("Failed to compile regex: %v", err)
	}

	// 查找所有匹配
	matches := re.FindAllStringSubmatch(query, -1)
	if len(matches) == 0 {
		t.Fatal("No matches found")
	}
	fmt.Println(matches)
	// 提取捕获组的内容
	var res []string
	for _, match := range matches {
		if len(match) > 1 { // match[0] 是整个匹配，match[1] 是第一个捕获组
			res = append(res, match[1])
		}
	}

	// 打印结果
	fmt.Println(res) // 输出: [人工智能]
}

func TestPatternGeneral(t *testing.T) {
	pattern := "^(.{1,10})的?(用法用量)$"
	query := "布洛芬用法用量"

	// 编译正则表达式
	re, err := regexp.Compile(pattern)
	if err != nil {
		t.Fatalf("Failed to compile regex: %v", err)
	}

	// 查找所有匹配
	matches := re.FindAllStringSubmatch(query, -1)
	if len(matches) == 0 {
		t.Fatal("No matches found")
	}

	// 提取捕获组的内容
	var res []string
	fmt.Println(matches)
	for _, match := range matches {
		if len(match) > 1 { // match[0] 是整个匹配，match[1] 是第一个捕获组
			res = append(res, match...)
		}
	}

	// 打印结果
	fmt.Println(res) // 输出: [人工智能]
}
