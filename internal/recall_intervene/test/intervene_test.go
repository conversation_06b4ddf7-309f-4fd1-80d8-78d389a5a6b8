package test

import (
	"fmt"
	"log"
	"testing"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/intervene"
	"github.com/BurntSushi/toml"
)

func TestIntervene(t *testing.T) {
	var config config.ConfigWrapper

	// Load the config.toml file
	if _, err := toml.DecodeFile("./config.toml", &config); err != nil {
		log.Fatal(err)
	}

	// Print the loaded configuration
	fmt.Printf("ServiceName: %s\n", config.IConf.ServiceName)

	intervene.InterveneInst.LoadPatternFromApi()

}
