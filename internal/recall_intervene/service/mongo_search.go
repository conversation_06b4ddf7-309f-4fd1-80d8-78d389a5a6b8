package service

import (
	"context"
	"fmt"
	"time"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/utils"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	"go.mongodb.org/mongo-driver/bson"
	"go.mongodb.org/mongo-driver/mongo"
)

var fields = []string{"embeddings", "content_terms", "title_terms", "_id", "indexCode", "_class"}

// 删除指定字段的递归函数
func removeFields(data map[string]any) {

	for key, value := range data {
		// 检查当前键是否在要删除的字段列表中
		for _, field := range fields {
			if key == field {
				delete(data, key)
				break
			}
		}

		// 如果值是嵌套的 map，递归调用 removeFields
		if nestedMap, ok := value.(map[string]any); ok {
			removeFields(nestedMap)
		}

		// 如果值是切片，遍历切片中的元素
		if slice, ok := value.([]any); ok {
			for _, item := range slice {
				if nestedMap, ok := item.(map[string]any); ok {
					removeFields(nestedMap)
				}
			}
		}
	}
}

func QueryByLemmaId(span *span.Span, indexName string, indexCode string, lemmaId int32) ([]map[string]any, error) {
	compressor := utils.GetDefault()
	opSpan := span.AddSpan("Mongo查询")
	defer opSpan.Finish()

	if indexCode == "" {
		indexCode = config.G_Config.MConf.Collection
	}

	G_mongoCollection := config.G_MongoDatabase.Collection(indexCode)

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	filter := bson.M{"lemma_id": lemmaId}
	cursor, err := G_mongoCollection.Find(ctx, filter)
	if err != nil {
		// 检查是否是 "NamespaceNotFound" 错误（集合不存在）
		if isNamespaceNotFoundError(err) {
			return nil, fmt.Errorf("集合 '%s' 不存在", indexCode)
		}
		return nil, fmt.Errorf("查询执行失败: %v", err)
	}
	defer cursor.Close(ctx)

	var results []map[string]any
	for cursor.Next(ctx) {
		var result map[string]any
		if err := cursor.Decode(&result); err != nil {
			return nil, fmt.Errorf("结果解码失败: %v", err)
		}
		res := compressor.Decompress(result)
		res["id"] = res["_id"]
		res["_indexCode"] = indexCode
		res["_indexName"] = indexName
		removeFields(res)
		results = append(results, res)
	}

	if err := cursor.Err(); err != nil {
		return nil, fmt.Errorf("游标错误: %v", err)
	}

	return results, nil
}

// 检查是否是 "NamespaceNotFound" 错误
func isNamespaceNotFoundError(err error) bool {
	if cmdErr, ok := err.(mongo.CommandError); ok {
		return cmdErr.Code == 26 // 26 是 MongoDB 的 NamespaceNotFound 错误代码
	}
	if writeErr, ok := err.(mongo.WriteException); ok {
		for _, we := range writeErr.WriteErrors {
			if we.Code == 26 {
				return true
			}
		}
	}
	return false
}
