package service

import (
	"fmt"
	"strings"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/global"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/intervene"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/utils"
	proto_intervene "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/recall_intervene"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

type Service struct {
	Ctx  *pandora_context.PandoraContext
	Req  *pandora_proto.PandoraRequestMessage[proto_intervene.ProtoInterveneAPIV2RequestPayload]
	Resp *pandora_proto.PandoraResponseMessage[proto_intervene.ProtoInterveneAPIV2ResponsePayload]
}

var PreprocessInst = NewPreprocess()

func (s *Service) checkRequest(span *span.Span) *errtypes.SelfError {
	opSpan := span.AddSpan("参数检查")
	defer opSpan.Finish()
	// traceId校验
	if len(s.Req.Header.TraceId) == 0 {
		return selferrors.CommonError_MissingField.Detaild(fmt.Sprintf("%s: traceId is empty", goboot.BootConf().DefaultInstance().Conf.ServiceName))
	}
	// data校验
	if len(s.Req.Payload.Data) == 0 {
		return selferrors.CommonError_MissingField.Detaild(fmt.Sprintf("%s: empty input data", goboot.BootConf().DefaultInstance().Conf.ServiceName))
	}
	// docs校验
	// for _, data := range s.Req.Payload.Data {
	// 	if len(data.Docs) == 0 {
	// 		return selferrors.CommonError_MissingField.Detaild(fmt.Sprintf("%s: empty input docs", goboot.BootConf().DefaultInstance().Conf.ServiceName))
	// 	}
	// }
	return nil
}

func (s *Service) setErrorResponse(err *errtypes.SelfError) {
	s.Resp.Header.Code = err.Code()
	s.Resp.Header.Success = err.String()
}

func (s *Service) strategyFullMatched(span *span.Span, query string, data *proto_intervene.RecallData) (bool, *errtypes.SelfError) {
	/*
		策略一：完全匹配
	*/
	opSpan := span.AddSpan("完全匹配")
	defer opSpan.Finish()

	d, tag, err := QueryTitleFullMatched(query, data.Docs)
	if err != nil {
		return false, selferrors.RecallInterveneError_InternelError.Detaild(err.Error())
	}
	if tag == global.QTFullMatched {
		goboot.Logger().DefaultInstance().Debug("query和title完全匹配成功")
		data.Docs = []*map[string]any{d}
		if s, ok := global.StrategyMap[global.StrategyFullMatched]; ok {
			data.MatchType = s
		}
		s.Resp.Payload.Data = append(s.Resp.Payload.Data, data)
		return true, nil
	}
	goboot.Logger().DefaultInstance().Debug("完全匹配干预失败")
	return false, nil
}

func (s *Service) strategyGeneralMatched(span *span.Span, query string, data *proto_intervene.RecallData) (bool, *errtypes.SelfError) {
	/*
		策略二：泛化句式匹配
	*/
	opSpan := span.AddSpan("泛化句式匹配")
	defer opSpan.Finish()
	var (
		d       *map[string]any
		tag     int
		lemmaId int32
	)
	// 泛化句式匹配,term为正则抽出的百科词条
	genernalMatched, term, err := SentenceMatch(query, intervene.InterveneInst.GeneralSentencePattern)
	if err != nil {
		return false, selferrors.RecallInterveneError_InternelError.Detaild(err.Error())
	}

	// 词条完全匹配和泛化句式匹配，最终返回结果需要定位到词条切片的第一段(description)
	if genernalMatched {
		// 泛化句式匹配成功
		d, lemmaId, tag, err = QueryTitleMatch(query, term, data.Docs)
	} else {
		// 泛化句式，判断query是否就是纯词条
		d, lemmaId, tag, err = QueryTitleMatch(query, query, data.Docs)
	}
	if err != nil {
		return false, err
	}
	if tag == global.QTFullMatched {
		goboot.Logger().DefaultInstance().Debug("泛化句式匹配成功，召回有好结果")
		data.Docs = []*map[string]any{d}
		if s, ok := global.StrategyMap[global.StrategyQIsBaike]; ok {
			data.MatchType = s
		}
		s.Resp.Payload.Data = append(s.Resp.Payload.Data, data)
		return true, nil
	}
	if lemmaId > 0 {
		// 词条匹配成功，查库
		indexCode, ok := s.Ctx.Store().GetData("_indexCode")
		if !ok {
			return false, selferrors.RecallInterveneError_InternelError.Detaild(fmt.Sprintf("cannot find %s in Ctx", "indexCode"))
		}
		indexName, ok := s.Ctx.Store().GetData("_indexName")
		if !ok {
			return false, selferrors.RecallInterveneError_InternelError.Detaild(fmt.Sprintf("cannot find %s in Ctx", "indexName"))
		}
		results, err := QueryByLemmaId(opSpan, utils.Interface2S(indexName), utils.Interface2S(indexCode), lemmaId)
		if err != nil {
			return false, selferrors.RecallInterveneError_InternelError.Detaild(err.Error())
		}
		for _, result := range results {
			title, err := PreprocessInst.Preprocess(utils.Interface2S(result["title"]), false)
			if err != nil {
				return false, selferrors.RecallInterveneError_InternelError.Detaild(err.Error())
			}
			if title == term {
				goboot.Logger().DefaultInstance().Debug(fmt.Sprintf("泛化句式匹配成功，召回无好结果，查库命中结果:%s", title))
				// r, err := utils.BsonToMap(result)
				// if err != nil {
				// 	return false, selferrors.RecallInterveneError_InternelError.Detaild(err.Error())
				// }
				data.Docs = []*map[string]any{&result}
				s.Resp.Payload.Data = append(s.Resp.Payload.Data, data)
				if term == query {
					if s, ok := global.StrategyMap[global.StrategyQIsBaike]; ok {
						data.MatchType = s
					}
					return true, nil
				} else {
					if s, ok := global.StrategyMap[global.StrategyGeneral]; ok {
						data.MatchType = s
					}
					return true, nil
				}
			}
		}
	} else {
		goboot.Logger().DefaultInstance().Debug("泛化句式匹配成功，好结果未召回,doc和query词条不匹配")
	}
	goboot.Logger().DefaultInstance().Debug("泛化句式干预失败")
	return false, nil
}

func (s *Service) strategyCommonMatched(span *span.Span, query string, data *proto_intervene.RecallData) (bool, *errtypes.SelfError) {
	opSpan := span.AddSpan("通用匹配")
	defer opSpan.Finish()
	// 通用句式匹配
	commonMatched, term, subTitle, err := CommonSentenceMatch(query, intervene.InterveneInst.CommonSentencePattern)
	if err != nil {
		return false, selferrors.RecallInterveneError_InternelError.Detaild(err.Error())
	}
	var lemmaId int32
	if commonMatched {
		for _, doc := range data.Docs {
			title, err := PreprocessInst.Preprocess(utils.Interface2S((*doc)["title"]), false)
			if err != nil {
				return false, selferrors.RecallInterveneError_InternelError.Detaild(err.Error())
			}
			items := strings.Fields(title)
			//   氯喹盐基 氯喹盐基 简介
			if items[0] == term {
				lemmaId = utils.Interface2I32((*doc)["lemma_id"])
				for _, item := range items {
					if item != term && item == subTitle {
						goboot.Logger().DefaultInstance().Debug(fmt.Sprintf("通用句式匹配成功, 召回有好结果:%s", title))
						data.Docs = []*map[string]any{doc}
						if s, ok := global.StrategyMap[global.StrategyCommon]; ok {
							data.MatchType = s
						}
						s.Resp.Payload.Data = append(s.Resp.Payload.Data, data)
						return true, nil
					}
				}
			}

		}
		if lemmaId > 0 {
			// 通用句式好结果没召回，查库
			goboot.Logger().DefaultInstance().Debug(fmt.Sprintf("通用句式匹配成功，召回无好结果,lemmaId:%d", lemmaId))
			indexCode, ok := s.Ctx.Store().GetData("_indexCode")
			if !ok {
				return false, selferrors.RecallInterveneError_InternelError.Detaild(fmt.Sprintf("cannot find %s in Ctx", "indexCode"))
			}
			indexName, ok := s.Ctx.Store().GetData("_indexName")
			if !ok {
				return false, selferrors.RecallInterveneError_InternelError.Detaild(fmt.Sprintf("cannot find %s in Ctx", "indexName"))
			}
			results, err := QueryByLemmaId(opSpan, utils.Interface2S(indexName), utils.Interface2S(indexCode), lemmaId)
			if err != nil {
				return false, selferrors.RecallInterveneError_InternelError.Detaild(err.Error())
			}
			for _, result := range results {
				title, err := PreprocessInst.Preprocess(utils.Interface2S(result["title"]), false)

				if err != nil {
					return false, selferrors.RecallInterveneError_InternelError.Detaild(err.Error())
				}
				items := strings.Fields(title)
				for _, item := range items {
					if item != term && item == subTitle {
						goboot.Logger().DefaultInstance().Debug(fmt.Sprintf("通用句式匹配成功，好结果未召回,查库命中title:%s", title))
						// r, err := utils.BsonToMap(result)
						// if err != nil {
						// 	return false, selferrors.RecallInterveneError_InternelError.Detaild(err.Error())
						// }
						data.Docs = []*map[string]any{&result}
						if s, ok := global.StrategyMap[global.StrategyCommon]; ok {
							data.MatchType = s
						}
						s.Resp.Payload.Data = append(s.Resp.Payload.Data, data)
						return true, nil
					}
				}
			}
		} else {
			goboot.Logger().DefaultInstance().Debug("通用句式匹配成功，好结果未召回,doc和query词条不匹配")
		}
	}
	goboot.Logger().DefaultInstance().Debug("通用句式干预失败")
	return false, nil
}

func (s *Service) parse(span *span.Span) {
	opSpan := span.AddSpan("策略匹配")
	defer func() {
		opSpan.Finish()
		s.Resp.Header.TraceId = s.Req.Header.TraceId
	}()

	s.Resp.Payload.Data = make([]*proto_intervene.RecallData, 0, len(s.Req.Payload.Data))

	for _, data := range s.Req.Payload.Data {
		if len(data.Docs) > 0 {
			// 只处理第一个doc
			doc := data.Docs[0]
			indexCode := utils.Interface2S((*doc)["_indexCode"])
			indexName := utils.Interface2S((*doc)["_indexName"])
			s.Ctx.Store().SetData("_indexCode", indexCode)
			s.Ctx.Store().SetData("_indexName", indexName)
		}
		if config.G_Config.IConf.OnlyRaw && data.Type != global.Raw {
			// 初版只匹配原始query召回的结果，但可以通过开关控制
			continue
		}
		// query预处理
		query, err := PreprocessInst.Preprocess(data.Query, true)
		if err != nil {
			s.setErrorResponse(err)
			return
		}

		bingo, err := s.strategyFullMatched(span, query, data)
		if err != nil {
			s.setErrorResponse(err)
			return
		}
		if bingo {
			continue
		}

		bingo, err = s.strategyGeneralMatched(span, query, data)
		if err != nil {
			s.setErrorResponse(err)
			return
		}
		if bingo {
			continue
		}

		_, err = s.strategyCommonMatched(span, query, data)
		if err != nil {
			s.setErrorResponse(err)
			return
		}

	}
}

func (s *Service) HttpHandler() {
	parseSpan := s.Ctx.RootSpan().AddSpan("Parser")
	defer func() {
		parseSpan.Finish()
		parseSpan.TraceInfo("requestInfo", s.Req)
		parseSpan.TraceInfo("responseInfo", s.Resp)
	}()

	err := s.checkRequest(parseSpan)
	if err != nil {
		s.setErrorResponse(err)
		return
	}

	s.parse(parseSpan)
}
