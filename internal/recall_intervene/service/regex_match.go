package service

import (
	"fmt"
	"regexp"
	"strings"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/global"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/intervene"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/recall_intervene/utils"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
)

func QueryTitleMatch(query string, term string, docs []*map[string]any) (*map[string]any, int32, int, *errtypes.SelfError) {
	/*
		term是从query中抽出的百科词条，执行逻辑：title中空格分割的词条和query中百科词条相同
	*/
	for _, doc := range docs {
		title, err := PreprocessInst.Preprocess(utils.Interface2S((*doc)["title"]), false)
		if err != nil {
			return nil, -1, global.QTNoMatched, selferrors.RecallInterveneError_InternelError.Detaild(err.Error())
		}

		if len(term) == 0 {
			continue
		}

		items := strings.Split(title, " ")
		lemmaId := utils.Interface2I32((*doc)["lemma_id"])
		// 召回的doc不是description
		if term == title {
			return doc, lemmaId, global.QTFullMatched, nil
		} else if items[0] == term {
			return doc, lemmaId, global.QTXXXMatched, nil
		}
	}
	return nil, -1, global.QTNoMatched, nil
}

func QueryTitleFullMatched(query string, docs []*map[string]any) (*map[string]any, int, *errtypes.SelfError) {
	for _, doc := range docs {
		title, err := PreprocessInst.Preprocess(utils.Interface2S((*doc)["title"]), true)
		if err != nil {
			return nil, global.QTNoMatched, selferrors.RecallInterveneError_InternelError.Detaild(err.Error())
		}
		if query == title {
			// 策略1判断逻辑
			return doc, global.QTFullMatched, nil
		}
	}
	return nil, global.QTNoMatched, nil
}

func matchPatterns(query string, patterns []string, patternType int) (string, [][]string, *errtypes.SelfError) {
	var patternMap map[string]*regexp.Regexp

	// 1. 检查 patternType 有效性
	switch patternType {
	case global.StrategyCommon:
		patternMap = intervene.InterveneInst.CommonPatternMap
	case global.StrategyGeneral:
		patternMap = intervene.InterveneInst.GeneralPatternMap
	default:
		return "", nil, selferrors.RecallInterveneError_InternelError.Detaild("Invalid pattern type")
	}

	// 2. 遍历所有模式进行匹配
	for _, pattern := range patterns {
		compiledRegex, exists := patternMap[pattern]
		if !exists {
			// // 动态编译正则表达式
			return "", nil, selferrors.RecallInterveneError_InternelError.Detaild("pattern未编译")
		}
		// 3. 执行匹配
		matches := compiledRegex.FindAllStringSubmatch(query, -1)
		if len(matches) > 0 {
			return pattern, matches, nil
		}
	}
	// 4. 无匹配结果
	return "", nil, nil
}

func SentenceMatch(query string, patterns []string) (bool, string, *errtypes.SelfError) {
	// 泛化句式匹配
	pattern, matches, err := matchPatterns(query, patterns, global.StrategyGeneral)
	if err != nil {
		return false, "", err
	}

	for _, match := range matches {
		if len(match) > 1 {
			goboot.Logger().DefaultInstance().Debug(fmt.Sprintf("泛化句式匹配成功, 词条:%s，句式：%s", match[1], pattern))
			return true, match[1], nil
		}
	}
	return false, query, nil
}

func CommonSentenceMatch(query string, patterns []string) (bool, string, string, *errtypes.SelfError) {
	// 通用句式匹配
	pattern, matches, err := matchPatterns(query, patterns, global.StrategyCommon)
	if err != nil {
		return false, "", "", err
	}

	if matches != nil {
		res := []string{}
		for _, match := range matches {
			for i := 0; i < len(match); i++ {
				res = append(res, match[i])
			}
		}

		if len(res) > 1 {
			term := res[1]
			subTitle := ""
			if len(res) > 2 {
				subTitle = res[2]
			}
			goboot.Logger().DefaultInstance().Debug(fmt.Sprintf("通用句式匹配成功, 词条:%s，子标题：%s，句式：%s", term, subTitle, pattern))
			return true, term, subTitle, nil
		}
	}
	return false, "", "", nil
}
