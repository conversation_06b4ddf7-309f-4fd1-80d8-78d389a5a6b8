package service

import (
	"regexp"
	"strings"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
)

// 预处理
type Preprocess struct {
}

func NewPreprocess() *Preprocess {
	return &Preprocess{}
}

var reg = regexp.MustCompile(`[。，？！：；“”‘’《》【】（）…\.\?!,:;'"\x60\-–—()\[\]{}<>/]`)

func (s *Preprocess) Preprocess(query string, cleanBlank bool) (string, *errtypes.SelfError) {
	// 大小写转换
	query = strings.ToLower(query)
	// 去除空格
	if cleanBlank {
		query = strings.Replace(query, " ", "", -1)
	}
	// 去重特殊字符
	// query = reg.ReplaceAllString(query, "")

	return query, nil
}

func (s *Preprocess) GetName() string {
	return "preprocess"
}
