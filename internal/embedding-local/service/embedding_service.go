package service

import (
	"fmt"
	"math"

	"golang.org/x/sync/errgroup"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	tokenizer_v2 "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/tokenizer_v2_1"
	"github.com/samber/lo"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/embedding-local/entity"
	proto_embedding "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/embedding"
	proto_onnx "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/onnx-modelserver/proto"
	onnx_runtime "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/onnx-modelserver/runtime"
	pandora_span "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	util "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/data_tool"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	wrapper "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/tokenizer_v2_1"
)

type service struct {
	defaultModel string
	aseMap       map[string]*aseItem
}

type aseItem struct {
	aseConf   *entity.ModelConfig
	ort       *onnx_runtime.OnnxRuntime
	tokenizer *wrapper.AsyncTokenizer
}

// Service 排序服务单例
var Service = new(service)

// 初始化服务
func (s *service) Init() error {
	models := goboot.GetCustomModule(entity.ModelFactory).GetAllConfig()
	onnxModule := goboot.GetCustomModule(entity.OnnxFactory)
	s.aseMap = make(map[string]*aseItem, len(models))
	for _, model := range models {
		item := &aseItem{}
		item.aseConf = model

		// 指定默认模型
		if model.Default {
			s.defaultModel = model.UName
		}

		if item.tokenizer = tokenizer_v2.G_tokenizer.GetInstance(model.UName); item.tokenizer == nil {
			return selferrors.EmbeddingError_TokenizerInitFailed.Detaild(fmt.Sprintf("invalid tokenizer: %s", model.UName))
		}

		if item.ort = onnxModule.GetInstance(model.UName); item.ort == nil {
			return selferrors.EmbeddingError_ModelNotSupport.Detaild(fmt.Sprintf("invalid onnxruntime: %s", model.UName))
		}

		s.aseMap[model.UName] = item
	}

	return nil
}

// 向量计算请求
func (s *service) Process(ctx *pandora_context.PandoraContext, span *pandora_span.Span, req *proto_embedding.ReqPayload) (resp *proto_embedding.RespPayload, err error) {
	embeddingSpan := span.AddSpan("embedding")
	defer func() {
		if err != nil {
			embeddingSpan.TraceInfo("Embedding process failed: ", err.Error())
		}
		embeddingSpan.Finish()
	}()

	// 参数不传，走默认值
	if req.Model == "" {
		req.Model = s.defaultModel
	}

	// 根据模型获取ase配置
	item, ok := s.aseMap[req.Model]
	if !ok {
		return nil, selferrors.EmbeddingError_ModelNotSupport.Detaild(fmt.Sprintf("invalid model: %s", req.Model))
	}

	aseConf := item.aseConf

	batchSpan := span.AddSpan("embedding-batch")
	// 按照指定batch_size重新划分doc
	var g errgroup.Group
	textsBatch := lo.Chunk(req.Texts, aseConf.BatchSize)
	resBatch := make([][][]float32, len(textsBatch))

	for batchID, texts := range textsBatch {
		g.Go(func() error {
			tokenizerSpan := batchSpan.AddSpan("embedding-tokenizer")
			token, shape, err := s.assembleToken(req.Model, texts)
			if err != nil {
				return err
			}
			tokenizerSpan.Finish()

			// 调用模型推理
			inferSpan := batchSpan.AddSpan("embedding-infer")
			resp, err := item.ort.Infer(ctx.GetContext(), &proto_onnx.InferReq{
				Inputs: token,
				Shapes: shape,
			})
			inferSpan.Finish()

			if err != nil {
				return selferrors.EmbeddingError_AseRequestFailed.Detaild(err.Error())
			}

			// 结果处理
			logits := util.BytesToSlice[float32](resp.Outputs[entity.Logits])

			results := lo.Chunk(logits, aseConf.ResDim)
			resBatch[batchID] = results

			return nil
		})
	}
	batchSpan.Finish()

	if err = g.Wait(); err != nil {
		return nil, err
	}

	// batch-merge
	l2Span := span.AddSpan("l2-norm")
	embedding := make([][]float32, 0, len(req.Texts))
	for _, batch := range resBatch {
		embedding = append(embedding, batch...)
	}
	// l2归一化
	for i := range embedding {
		embedding[i] = L2Normalization(embedding[i])
	}
	l2Span.Finish()

	return &proto_embedding.RespPayload{Model: req.Model, Embedding: embedding}, nil
}

func (s *service) assembleToken(model string, queries []string) (map[string][]byte, map[string][]int64, error) {
	tokenMap := make(map[string][]byte)
	shapeMap := make(map[string][]int64)

	encodings, err := wrapper.AsyncEncodeBatch[int64](s.aseMap[model].tokenizer, queries, true)
	if err != nil {
		return nil, nil, err
	}

	for _, encoding := range encodings {
		tokenMap[entity.InputIds] = append(tokenMap[entity.InputIds], util.SliceToBytes(encoding.Ids)...)
		tokenMap[entity.AttentionMask] = append(tokenMap[entity.AttentionMask], util.SliceToBytes(encoding.Masks)...)
	}

	batchSize := int64(len(queries))
	shapeMap[entity.InputIds] = []int64{batchSize, int64(len(encodings[0].Ids))}
	shapeMap[entity.AttentionMask] = []int64{batchSize, int64(len(encodings[0].Masks))}

	return tokenMap, shapeMap, nil
}

// L2Normalization l2归一化
func L2Normalization(data []float32) []float32 {
	var sum float32
	for _, v := range data {
		sum += v * v
	}
	sum = float32(math.Sqrt(float64(sum)))
	for i := range data {
		data[i] /= sum
	}
	return data
}
