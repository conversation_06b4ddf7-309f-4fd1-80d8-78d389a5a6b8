package filter

import (
	"fmt"
	"sync"

	pandora_span "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	"go.mongodb.org/mongo-driver/bson"
)

// Filter 接口定义
type Filter interface {
	Apply(input []bson.M) ([]bson.M, error)
}

// Processor 处理器,可配置多个过滤组件
type Processor struct {
	Filters      map[string]Filter        // 过滤组件
	OutputFormat func(item bson.M) string // 可配置输出格式
	sync.Mutex
}

// SetFilter 设置过滤器
func (p *Processor) SetFilter(name string, filter Filter) {
	p.Lock()
	defer p.Unlock()
	p.Filters[name] = filter

}

// Apply 应用过滤器
func (p *Processor) Apply(span *pandora_span.Span, filterNames []string, input []bson.M) ([]bson.M, error) {
	var err error
	for _, name := range filterNames {
		childSpan := span.AddSpan(name)
		input, err = p.Filters[name].Apply(input)

		if err != nil {
			childSpan.TraceInfo("error", err.Error())
			childSpan.Finish()
			return nil, fmt.Errorf("%s failed, error: %v", name, err)
		}
		childSpan.Finish()
	}
	return input, nil
}
