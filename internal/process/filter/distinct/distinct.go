package distinct

import (
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/filter"
	proto_process "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/process"
	"go.mongodb.org/mongo-driver/bson"
)

// Distinct 根据特定条件去重
type Distinct struct {
	KeySelector    func(item bson.M, fields []string) (string, error)
	ChooseItem     func(item1, item2 bson.M, params []*proto_process.DistinctField) (bson.M, error) // 选择保留的项
	ReserveFields  []*proto_process.DistinctField
	DistinctFields []string
}

// 检查是否实现Filter接口
var _ filter.Filter = (*Distinct)(nil)

func (d *Distinct) Apply(input []bson.M) ([]bson.M, error) {
	seen := make(map[any]bson.M)
	order := make([]string, 0, len(input))
	for _, item := range input {
		// 取不到的字段,默认为空
		key, err := d.KeySelector(item, d.DistinctFields)
		if err != nil {
			return nil, err
		}

		// 保留条件取不到的那一项排除
		if existingItem, found := seen[key]; found {
			choice, err := d.ChooseItem(existingItem, item, d.ReserveFields)
			if err != nil {
				return nil, err
			}
			seen[key] = choice
		} else {
			seen[key] = item
			order = append(order, key)
		}
	}

	result := make([]bson.M, len(order))
	for i, key := range order {
		result[i] = seen[key]
	}
	return result, nil
}
