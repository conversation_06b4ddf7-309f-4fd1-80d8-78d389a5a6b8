package distinct

import (
	"regexp"
)

// 预编译正则表达式以提高性能
var (
	// 标点符号正则
	punctuationRegex = regexp.MustCompile(`[!"#$%&'()*+,\-./:;<=>?@\[\\\]^_` + "`" + `{|}~]|\p{P}`)

	// 空白字符正则,包括空格、制表符、换行符等
	whitespaceRegex = regexp.MustCompile(`[\s\p{Zs}]`)

	// 控制字符正则（排除 \t\n\r）
	controlRegex = regexp.MustCompile(`[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]|\p{Cc}`)

	// 中文标点符号正则
	chinesePuncRegex = regexp.MustCompile(`[–—''""…、。〈〉《》「」『』【】〔〕！（），．：；？]`)
)

// IsPunctuation 检查字符是否为标点符号
func IsPunctuation(c rune) bool {
	return punctuationRegex.MatchString(string(c))
}

// IsWhiteSpace 检查字符是否为空白字符
func IsWhiteSpace(c rune) bool {
	return whitespaceRegex.MatchString(string(c))
}

// IsControl 检查字符是否为控制字符
func IsControl(c rune) bool {
	if c == '\t' || c == '\n' || c == '\r' {
		return false
	}
	return controlRegex.MatchString(string(c))
}

// IsChinesePunctuation 检查字符是否为中文标点符号
func IsChinesePunctuation(c rune) bool {
	return chinesePuncRegex.MatchString(string(c))
}

// Clean 清理文本中的无效字符和空白字符
func Clean(text string) string {
	if text == "" {
		return text
	}

	// 移除标点符号
	cleaned := punctuationRegex.ReplaceAllString(text, "")

	// 移除无效字符
	cleaned = controlRegex.ReplaceAllString(cleaned, "")

	// 将连续空白字符替换为单个空格
	cleaned = whitespaceRegex.ReplaceAllString(cleaned, "")

	// 移除中文标点符号
	cleaned = chinesePuncRegex.ReplaceAllString(cleaned, "")

	return cleaned
}
