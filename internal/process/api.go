package process

import (
	"fmt"
	"time"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/service"
	proto_process "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/process"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
	"github.com/go-playground/validator/v10"
	"go.mongodb.org/mongo-driver/bson"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
)

type processHandler struct {
	filterService *service.FilterService
	validate      *validator.Validate
}

func (p *processHandler) Process(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_process.Payload]) *pandora_proto.PandoraResponseMessage[proto_process.Payload] {
	processSpan := ctx.RootSpan().AddSpan("post-process")
	defer processSpan.Finish()

	now := time.Now()

	resp := proto_process.ProtoAPI.NewPandoraResponseMessage()

	// 请求参数校验
	if err := p.validate.Struct(req); err != nil {
		processSpan.TraceInfo("validate err", err)
		return fillHeader(resp, selferrors.CommonError_InvalidInput.Detaild(err.Error()))
	}

	// 调用process
	if err := p.filterService.Process(processSpan, &req.Payload); err != nil {
		processSpan.TraceInfo("process err", err)
		return fillHeader(resp, selferrors.ProcessError_FilterFailed.Detaild(err.Error()))
	}

	resp.Payload = req.Payload
	resp.Header.Success = fmt.Sprintf("success, cost: %dms", time.Since(now).Milliseconds())
	return resp
}

// Init 服务初始化
func Init() error {
	// 服务初始化
	handler := &processHandler{
		filterService: &service.FilterService{},
		validate:      validator.New(validator.WithRequiredStructEnabled()),
	}

	//注册服务
	goboot.HttpServer().DefaultInstance().Router.POST("/process/api/v2", proto_process.ProtoAPI.GinWrapper().SetHandler(handler.Process).SetRequestUnmarshal(bsonUnmarshal).HandlerFunc())
	return nil
}

func bsonUnmarshal(out []byte, req *pandora_proto.PandoraRequestMessage[proto_process.Payload]) error {
	return bson.UnmarshalExtJSON(out, true, req)
}

func fillHeader(resp *pandora_proto.PandoraResponseMessage[proto_process.Payload], err *errtypes.SelfError) *pandora_proto.PandoraResponseMessage[proto_process.Payload] {
	resp.Header.Code = err.Code()
	resp.Header.Success = err.String()
	return resp
}
