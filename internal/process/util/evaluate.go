package util

import (
	"errors"
	"fmt"
	"strconv"
	"strings"
	"time"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/model"
	"golang.org/x/exp/constraints"
)

func EvaluateOperator(field any, fieldType string, operator string, value any) bool {
	docValue := fmt.Sprintf("%v", field)
	condValue := fmt.Sprintf("%v", value)
	switch fieldType {
	case model.TYPE_INT, model.TYPE_LONG:
		doc, _ := strconv.ParseInt(docValue, 10, 64)
		cond, _ := strconv.ParseInt(condValue, 10, 64)
		return evaluateNum(doc, operator, cond)
	case model.TYPE_FLOAT32, model.TYPE_DOUBLE:
		doc, _ := strconv.ParseFloat(docValue, 64)
		cond, _ := strconv.ParseFloat(condValue, 64)
		return evaluateNum(doc, operator, cond)
	case model.TYPE_STRING:
		return evaluateStringOperator(docValue, operator, condValue)
	case model.TYPE_LENGTH:
		return evaluateStringOperator(docValue, operator, condValue)
	}
	return false
}

func evaluateNum[T constraints.Ordered](field T, operator string, value T) bool {
	switch operator {
	case "==", "!=", ">", "<", ">=", "<=":
		return basicCompare(field, operator, value)
	case "last_n_hours", "last_n_days", "last_n_months":
		return evaluateTimeCondition(field, value, operator)
	}
	return false
}

// 基础比较操作统一处理
func basicCompare[T constraints.Ordered](a T, op string, b T) bool {
	switch op {
	case "==":
		return a == b
	case "!=":
		return a != b
	case ">":
		return a > b
	case "<":
		return a < b
	case ">=":
		return a >= b
	case "<=":
		return a <= b
	}
	return false
}

// 统一处理时间条件判断
func evaluateTimeCondition(field, value any, operator string) bool {
	fieldInt, valueInt, err := convertToInt64(field, value)
	if err != nil {
		return false
	}

	now := time.Now()
	switch operator {
	case "last_n_hours":
		compareTime := now.Add(-time.Duration(valueInt) * time.Hour).Unix()
		return fieldInt >= compareTime
	case "last_n_days":
		compareTime := now.AddDate(0, 0, -int(valueInt)).Unix()
		return fieldInt >= compareTime
	case "last_n_months":
		compareTime := now.AddDate(0, -int(valueInt), 0).Unix()
		return fieldInt >= compareTime
	}
	return false
}

// 重命名并修正错误信息
func convertToInt64(v1, v2 any) (int64, int64, error) {
	v1Int, ok := v1.(int64)
	if !ok {
		return 0, 0, errors.New("first value is not int64")
	}
	v2Int, ok := v2.(int64)
	if !ok {
		return 0, 0, errors.New("second value is not int64")
	}
	return v1Int, v2Int, nil
}

func evaluateStringOperator(field string, operator string, value string) bool {
	// 转化失败num为0
	num, _ := strconv.Atoi(value)
	switch operator {
	case "==":
		return field == value
	case "!=":
		return field != value
	case ">":
		return len([]rune(field)) > num
	case "<":
		return len([]rune(field)) < num
	case ">=":
		return len([]rune(field)) >= num
	case "<=":
		return len([]rune(field)) <= num
	case "in":
		return Contains(strings.Split(value, ","), field) > -1
	}
	return false
}
