package sort

import (
	"fmt"
	"sort"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/model"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/process/util"
	proto_process "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/process"
	pandora_span "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
)

func Sort(span *pandora_span.Span, sortList []*proto_process.SortField, payload *proto_process.Payload) error {
	if len(sortList) == 0 || len(payload.Data) == 0 {
		return nil
	}

	// 对每个 ReqData 中的 Docs 进行排序
	for _, reqData := range payload.Data {
		if len(reqData.Docs) <= 1 {
			continue
		}

		// 使用 sort.Slice 进行排序
		// TODO 这儿耗时几十ms，是否可以优化？
		sort.Slice(reqData.Docs, func(i, j int) bool {
			// 遍历所有排序字段
			for _, sortField := range sortList {
				// 获取两个文档中对应字段的值
				valueI, err := util.GetField(sortField.Field, reqData.Docs[i])
				if err != nil {
					// 取不到值，进行下一个排序字段
					continue
				}
				valueJ, err := util.GetField(sortField.Field, reqData.Docs[j])
				if err != nil {
					// 取不到值，进行下一个排序字段
					continue
				}

				// 如果两个值相等，继续比较下一个排序字段
				if compareValue(valueI, "==", valueJ) {
					continue
				}

				// 根据排序方向返回比较结果
				if sortField.Sort == model.SORT_TYPE_DESC {
					return compareValue(valueI, ">", valueJ)
				} else if sortField.Sort == model.SORT_TYPE_ASC {
					return compareValue(valueI, "<", valueJ)
				}
				return false
			}
			return false // 所有字段都相等时保持原有顺序
		})
	}
	return nil
}

func compareValue(value1 any, operator string, value2 any) bool {

	var fieldType string
	// v := reflect.TypeOf(value1)
	switch v := value1.(type) {
	case int:
		fieldType = model.TYPE_INT
	case int32:
		fieldType = model.TYPE_LONG
	case int64:
		fieldType = model.TYPE_LONG
	case float64:
		fieldType = model.TYPE_DOUBLE
	case float32:
		fieldType = model.TYPE_FLOAT32
	case string:
		fieldType = model.TYPE_STRING
	default:
		fmt.Printf("Unsupported field type: %T\n", v)

	}

	return util.EvaluateOperator(value1, fieldType, operator, value2)
}
