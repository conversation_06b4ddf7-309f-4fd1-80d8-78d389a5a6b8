package bean

import (
	proto_rerank "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/rerank"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

type RespPayLoad = proto_rerank.RerankAPIV2RespPayLoad
type Response = pandora_proto.PandoraResponseMessage[RespPayLoad]
type RequestPayLoad = proto_rerank.RerankAPIV2RequestPayLoad
type RerankReq = pandora_proto.PandoraRequestMessage[RequestPayLoad]
type Request = pandora_proto.PandoraRequestMessage[RequestPayLoad]
type QueryData = proto_rerank.RerankAPIV2QueryData
