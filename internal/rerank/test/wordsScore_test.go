package test

import (
	"bufio"
	"fmt"
	"os"
	"sort"
	"testing"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/service/utils"
	"github.com/yanyiwu/gojieba"
)

func TestWordScore(t *testing.T) {

	query := "多维元素片可以长期服用吗？"
	title := "多维元素片能长期吃吗"
	content := "如果遵医嘱吃多维元素片，一般能长期吃；若不按照医生的指导吃多维元素片，则不能长期吃。具体分析如下： 1.能：多维元素片是一种常见的西药，具有补充维生素的作用，常用于预防和治疗因维生素、矿物质缺乏所引起的疾病。通常情况下，如果遵医嘱吃该药物，一般能适当长期吃，不会对身体造成较大的不良影响。 2.不能：如果不按照医生的指导下吃多维元素片，此时通常不能长期吃。因为是药三分毒，长期服用这种药物也可能会引起一些不良反应，比如烦躁不安、头晕、疲倦等精神异常表现。 所以如果身体中缺乏维生素不是特别的严重，可以通过多吃蔬菜、水果、肉类等食物补充身体所需的维生素。此外，在服用多维元素片时，需谨遵医嘱，不可擅自乱用，以免引起身体不适。"

	queryKeysOrigin := []string{"多维", "元素", "片", "可以", "长期", "服用", "吗"}
	queryValuesOrigin := []float64{0.2162676900625229, 0.2571503818035126, 0.2504954934120178, 0.03400563821196556, 0.11645864695310593, 0.12265162914991379, 0.002970494795590639}
	titleKeys := []string{"多维", "元素", "片", "能", "长期", "吃", "吗"}
	titleValues := []float64{0.22851578891277313, 0.2720273435115814, 0.25590774416923523, 0.03864225372672081, 0.09763773530721664, 0.10537904500961304, 0.0018900162540376186}
	wordsScore, matchDict, unmatchDict, unmatchDict_, err := utils.MatchWordsScore0620(query, title, content, queryKeysOrigin, queryValuesOrigin, titleKeys, titleValues)
	if err != nil {
		fmt.Println(err.Error())
	}
	fmt.Println(matchDict)
	fmt.Println(unmatchDict)
	fmt.Println(unmatchDict_)
	fmt.Println(wordsScore)

}

// jieba 分词
var jieba *gojieba.Jieba // 定义全局的 gojieba 实例
func init() { // 初始化
	jieba = gojieba.NewJieba()
}

// 自定义词典
var Dictionary []string
var dictPath string = "/data/lynxiao/lynxiao-ai-search/resource/rerank/wordDict0620.txt"

func init() { // 初始化
	Dictionary = loadWordDict(dictPath)
	if Dictionary == nil {
		fmt.Println("无法加载自定义词典，程序退出。")
		os.Exit(1)
	}
	// 对 Dictionary 按照长度降序排列
	sort.Slice(Dictionary, func(i, j int) bool {
		return len(Dictionary[i]) > len(Dictionary[j])
	})
}

// 加载自定义词典, Dictionary 存储为列表
func loadWordDict(dictPath string) []string {
	file, err := os.Open(dictPath)
	if err != nil {
		fmt.Printf("无法打开文件: %v\n", err)
		return nil
	}
	defer file.Close()

	var Dictionary []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		Dictionary = append(Dictionary, line)
	}

	if err := scanner.Err(); err != nil {
		fmt.Printf("读取文件时出错: %v\n", err)
	}

	return Dictionary
}

func TestGoJieba(t *testing.T) {
	res := jieba.Cut("多维元素片可以长期服用吗？", false)
	fmt.Println(res)
}
