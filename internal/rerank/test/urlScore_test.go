package test

import (
	"fmt"
	"testing"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/consts"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/service/rank"
)

// 验证wordScore正确的情况下，urlScore得分是否正确
func TestUrlScore(t *testing.T) {
	req := &bean.Request{
		Payload: bean.RequestPayLoad{
			Model:     consts.Medical_V0620,
			RankSites: []string{"m.baidu.com/bh/m/detail/ar"},
		},
	}
	url := "http://m.baidu.com/bh/m/detail/ar_1078793543477347384"
	host := "m.baidu.com"
	keywordsScore := 0.931
	index := 2
	tag := true
	domains := []string{"m.baidu.com"}
	indexThreshold := 15
	score := rank.UrlsScore(req, url, host, keywordsScore, index, tag, domains, indexThreshold)
	fmt.Println(score)
}
