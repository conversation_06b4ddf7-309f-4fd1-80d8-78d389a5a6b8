package test

import (
	"fmt"
	"testing"

	"github.com/go-ego/gse"
)

func TestSegment(t *testing.T) {
	segmentPath := "/data/lynxiao/lynxiao-ai-search/resource/rerank/data/dict/s_1.txt"
	stopWordsPath := "/data/lynxiao/lynxiao-ai-search/resource/rerank/data/dict/stop_word.txt"
	var seg gse.Segmenter

	err := seg.LoadDict(segmentPath)
	if err != nil {
		fmt.Println("Load seg dictionary error: " + err.Error())
		return
	}
	err = seg.LoadStop(stopWordsPath)
	if err != nil {
		fmt.Println("Load stop dictionary error: " + err.Error())
		return
	}
	res := seg.Cut("刘德华的老婆是谁")
	fmt.Println("分词结果:", res)

}
