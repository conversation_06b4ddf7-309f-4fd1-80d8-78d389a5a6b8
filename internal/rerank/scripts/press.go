package main

import (
	"bufio"
	"bytes"
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"os/signal"
	"sort"
	"sync"
	"sync/atomic"
	"syscall"
	"time"
)

var (
	filePath    = flag.String("file", "", "Path to JSON file containing request data")
	apiURL      = flag.String("url", "", "API endpoint URL")
	concurrency = flag.Int("concurrency", 10, "Number of concurrent workers")
	duration    = flag.Int("duration", 0, "Test duration in seconds (0 for unlimited)")
	total       = flag.Int("total", 0, "Total requests to send (0 for unlimited)")
)

type Result struct {
	latencies []time.Duration
	mu        sync.Mutex
}

func (r *Result) Add(latency time.Duration) {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.latencies = append(r.latencies, latency)
}

func (r *Result) Metrics() (qps float64, avg, p90, p95, p99 time.Duration) {
	r.mu.<PERSON>()
	defer r.mu.Unlock()

	if len(r.latencies) == 0 {
		return 0, 0, 0, 0, 0
	}

	// Calculate total duration from first to last request
	sort.Slice(r.latencies, func(i, j int) bool {
		return r.latencies[i] < r.latencies[j]
	})

	// Calculate percentiles
	totalReq := len(r.latencies)
	p90 = r.latencies[totalReq*90/100]
	p95 = r.latencies[totalReq*95/100]
	p99 = r.latencies[totalReq*99/100]

	// Calculate average
	var totalLatency time.Duration
	for _, l := range r.latencies {
		totalLatency += l
	}
	avg = totalLatency / time.Duration(totalReq)

	return
}

func main() {
	flag.Parse()

	if *filePath == "" || *apiURL == "" {
		log.Fatal("Both file and url parameters are required")
	}

	// Read requests from file
	requests, err := readRequests(*filePath)
	if err != nil {
		log.Fatalf("Error reading requests: %v", err)
	}
	if len(requests) == 0 {
		log.Fatal("No requests found in the file")
	}

	// Setup context for cancellation
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Handle interrupt signals
	sigCh := make(chan os.Signal, 1)
	signal.Notify(sigCh, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		<-sigCh
		cancel()
	}()

	var wg sync.WaitGroup
	result := &Result{}
	requestCh := make(chan json.RawMessage, *concurrency*2)
	reqCounter := int64(0)

	// Start workers
	for i := 0; i < *concurrency; i++ {
		wg.Add(1)
		go worker(ctx, &wg, *apiURL, requestCh, result, &reqCounter)
	}

	// Setup timeout if duration is set
	if *duration > 0 {
		go func() {
			time.Sleep(time.Duration(*duration) * time.Second)
			cancel()
		}()
	}

	// Start feeding requests
	startTime := time.Now()
	go feedRequests(ctx, requests, requestCh, *total)

	// Wait for all workers to finish
	wg.Wait()
	close(requestCh)
	totalTime := time.Since(startTime)

	// Calculate metrics
	_, avg, p90, p95, p99 := result.Metrics()
	totalRequests := len(result.latencies)

	fmt.Println("\n--- Test Results ---")
	fmt.Printf("Total requests: %d\n", totalRequests)
	fmt.Printf("Total duration: %s\n", totalTime.Round(time.Millisecond))
	fmt.Printf("QPS: %.2f\n", float64(totalRequests)/totalTime.Seconds())
	fmt.Printf("Avg latency: %s\n", avg.Round(time.Millisecond))
	fmt.Printf("90th percentile: %s\n", p90.Round(time.Millisecond))
	fmt.Printf("95th percentile: %s\n", p95.Round(time.Millisecond))
	fmt.Printf("99th percentile: %s\n", p99.Round(time.Millisecond))
}

func readRequests(filePath string) ([]json.RawMessage, error) {
	file, err := os.Open(filePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var requests []json.RawMessage
	scanner := bufio.NewScanner(file)

	// 设置更大的缓冲区（最大10MB）
	maxCapacity := 10 * 1024 * 1024 // 10MB
	buf := make([]byte, maxCapacity)
	scanner.Buffer(buf, maxCapacity)

	for scanner.Scan() {
		line := scanner.Bytes()
		if len(line) > 0 {
			// 复制数据以避免被覆盖
			lineCopy := make([]byte, len(line))
			copy(lineCopy, line)
			requests = append(requests, json.RawMessage(lineCopy))
		}
	}
	return requests, scanner.Err()
}

func feedRequests(ctx context.Context, requests []json.RawMessage, ch chan<- json.RawMessage, total int) {
	sent := 0
	for {
		for _, req := range requests {
			select {
			case <-ctx.Done():
				return
			default:
				if total > 0 && sent >= total {
					return
				}
				ch <- req
				sent++
			}
		}
	}
}

func worker(ctx context.Context, wg *sync.WaitGroup, url string, ch <-chan json.RawMessage, result *Result, counter *int64) {
	defer wg.Done()
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	for {
		select {
		case <-ctx.Done():
			return
		case req, ok := <-ch:
			if !ok {
				return
			}

			// Check total request limit
			if *total > 0 && atomic.LoadInt64(counter) >= int64(*total) {
				return
			}
			atomic.AddInt64(counter, 1)

			start := time.Now()
			resp, err := client.Post(url, "application/json", bytes.NewReader(req))
			latency := time.Since(start)

			if err == nil {
				io.Copy(io.Discard, resp.Body)
				resp.Body.Close()
			}

			result.Add(latency)
		}
	}
}
