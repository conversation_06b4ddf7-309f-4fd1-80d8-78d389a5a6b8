"""
你现在是一个资深的python开发工程师，现在需要你写一个通用的接口跑测脚本，需求如下：
1、按行读取json文件，json文件中每行就是一个完整的请求体
2、调用指定api接口，并记录返回结果到json，每行一个结果
3、代码需要可复用，适应不同的接口，输入文件路径和api接口地址可以指定，而不改变代码逻辑
你需要不断的调试运行，纠错，不能中途停止，使得输出的产物能够准确无误的完成需求为止

pip install requests tqdm

python api_test_tool.py \
  --input requests.json \
  --output results.json \
  --api http://your-api-endpoint.com/path \
  --timeout 15
"""
import argparse
import json
import requests
import time
import logging
from tqdm import tqdm
from requests.exceptions import RequestException, Timeout, ConnectionError, HTTPError

def send_request(api_url, payload, timeout=10):
    """
    发送HTTP POST请求到指定API
    :param api_url: API地址
    :param payload: 请求体数据
    :param timeout: 请求超时时间
    :return: 包含响应状态码和内容的字典
    """
    try:
        headers = {'Content-Type': 'application/json'}
        response = requests.post(api_url, json=payload, headers=headers, timeout=timeout)
        response.raise_for_status()
        
        try:
            # 尝试解析JSON响应
            return {
                'status_code': response.status_code,
                'response': response.json(),
                'error': None
            }
        except json.JSONDecodeError:
            # 如果响应不是JSON，返回原始文本
            return {
                'status_code': response.status_code,
                'response': response.text,
                'error': 'Non-JSON response'
            }
    
    except (Timeout, ConnectionError) as e:
        return {
            'status_code': 0,
            'response': None,
            'error': f'Connection error: {str(e)}'
        }
    except HTTPError as e:
        return {
            'status_code': e.response.status_code,
            'response': None,
            'error': f'HTTP error: {str(e)}'
        }
    except RequestException as e:
        return {
            'status_code': 0,
            'response': None,
            'error': f'Request failed: {str(e)}'
        }

def run_api_tests(input_file, output_file, api_url, timeout=10):
    """
    执行API测试
    :param input_file: 输入文件路径
    :param output_file: 输出文件路径
    :param api_url: API地址
    :param timeout: 请求超时时间
    """
    processed = 0
    errors = 0
    
    try:
        # 打开输入输出文件
        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(output_file, 'w', encoding='utf-8') as outfile:
            
            # 获取总行数用于进度条
            total_lines = sum(1 for _ in infile)
            infile.seek(0)  # 重置文件指针
            
            # 创建进度条
            progress_bar = tqdm(total=total_lines, desc="Processing requests", unit="req")
            
            for line in infile:
                try:
                    # 解析JSON行
                    payload = json.loads(line.strip())
                    
                    # 发送请求
                    start_time = time.time()
                    result = send_request(api_url, payload, timeout)
                    elapsed = time.time() - start_time
                    
                    # 添加元数据
                    result['timestamp'] = time.strftime('%Y-%m-%d %H:%M:%S')
                    result['elapsed_time'] = round(elapsed, 4)
                    result['request'] = payload
                    
                    # 写入结果
                    outfile.write(json.dumps(result, ensure_ascii=False) + '\n')
                    outfile.flush()  # 确保每次写入后立即保存
                    
                    processed += 1
                    
                    # 错误统计
                    if result['error'] or result['status_code'] >= 400:
                        errors += 1
                        logging.warning(f"Request failed: {result.get('error')} (Status: {result['status_code']})")
                
                except json.JSONDecodeError as e:
                    errors += 1
                    logging.error(f"Invalid JSON in input: {line.strip()} - Error: {str(e)}")
                    # 写入错误记录
                    error_result = {
                        'error': f'Invalid input JSON: {str(e)}',
                        'original_line': line.strip(),
                        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
                    }
                    outfile.write(json.dumps(error_result) + '\n')
                
                progress_bar.update(1)
                time.sleep(0.01)  # 避免请求过载
            
            progress_bar.close()
    
    except IOError as e:
        logging.critical(f"File operation error: {str(e)}")
        raise

    # 输出摘要
    success_rate = (processed - errors) / processed * 100 if processed > 0 else 0
    print(f"\nTest completed: {processed} requests processed, {errors} errors")
    print(f"Success rate: {success_rate:.2f}%")
    print(f"Results saved to: {output_file}")

if __name__ == "__main__":
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[logging.StreamHandler()]
    )
    
    # 命令行参数解析
    parser = argparse.ArgumentParser(description='API Load Testing Tool')
    parser.add_argument('--input', type=str, required=True, 
                        help='Input JSON file path (one JSON object per line)')
    parser.add_argument('--output', type=str, required=True,
                        help='Output JSON file path for results')
    parser.add_argument('--api', type=str, required=True,
                        help='API endpoint URL')
    parser.add_argument('--timeout', type=float, default=10.0,
                        help='Request timeout in seconds (default: 10)')
    
    args = parser.parse_args()
    
    # 验证输入文件
    if not args.input.endswith('.json'):
        logging.warning("Input file does not have .json extension")
    
    # 执行测试
    try:
        print(f"Starting API tests\nInput: {args.input}\nAPI: {args.api}")
        run_api_tests(args.input, args.output, args.api, args.timeout)
    except Exception as e:
        logging.exception("Unexpected error occurred during testing")
        exit(1)