import os
import json
import uuid


sample = {
    "header": {
        "traceId": str(uuid.uuid4()),
    },
    "payload": {
        "topk": 10,
        "scoreThreshold": 0.85,
        "step": 0.05,
        "model": "5001_V20250620",
        "rankCollections": [
            "ifly_search.kb_health",
            "ifly_search.kb_health_2"
        ],
        "rankSites": [
            "m.baidu.com/bh/m/detail/ar",
            "health.baidu.com/m/detail/ar",
            "dxy.com"
        ],
        "intent": "hot",
        "data": []
    }
}


lines = open("/data/bak/wfliu3/lynxiao/lynxiao-ai-search/internal/rerank/scripts/data/medical_0620/test_all.json","r", encoding="utf-8").readlines()
fp_w = open("/data/bak/wfliu3/lynxiao/lynxiao-ai-search/internal/rerank/scripts/data/medical_0620/test_all_trans.json", "w", encoding="utf-8")
for line in lines:
    data = json.loads(line)
    sample["header"]['traceId'] = str(uuid.uuid4())
    sample['payload']['data']=data['data']
    fp_w.write(json.dumps(sample,ensure_ascii=False)+"\n")


