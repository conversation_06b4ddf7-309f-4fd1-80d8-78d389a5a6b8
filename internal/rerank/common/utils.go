package common

import (
	"crypto/md5"
	"crypto/rand"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"math"
	"math/big"
	"net"
	"regexp"
	"runtime"
	"strconv"
	"strings"
	"unicode/utf8"

	"github.com/spf13/cast"
	"golang.org/x/exp/constraints"
)

var allpuncReg = regexp.MustCompile("[!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~ ·，《。》、？；￥…（）]+")

//"[，_《。》、？；：‘’＂“”【「】」！@￥…（）—,<.>\/\?\;\:\'\"\[\]\{\}\~\`\!\@\#\$\%\^\&\*\(\)\-\=\+ ·]"

func GenerateUUID() string {
	b := make([]byte, 16)
	_, err := rand.Read(b)
	if err != nil {
		return ""
	}
	return hex.EncodeToString(b)
}

// 取余运算的基数
var remBase *big.Int = new(big.Int).SetUint64(uint64(math.Pow(2, 63)))

func GenerateStrId(text string) string {
	md5Bytes := md5.Sum([]byte(text))      //md5哈希的结果是128bit
	return hex.EncodeToString(md5Bytes[:]) //十六进制编码之后是128/4=32个字符
}

// 通过计算text的MD5Hash值 生成int64类型的id
func GenerateLongId(text string) int64 {
	md5Bytes := md5.Sum([]byte(text))
	idInt := new(big.Int).SetBytes(md5Bytes[:])
	idInt.Rem(idInt, remBase)

	return idInt.Int64()
}

// 返回两个数的最小值
func Min[T constraints.Ordered](a, b T) T {
	if a < b {
		return a
	}
	return b
}

// 定义数值类型的泛型约束
type Number interface {
	int | int8 | int16 | int32 | int64 |
		uint | uint8 | uint16 | uint32 | uint64 | uintptr |
		float32 | float64
}

// 泛型求和函数
func Sum[T Number](src []T) T {
	var total T
	for _, v := range src {
		total += v
	}
	return total
}

func ToInt64Slice(data, sep string) []int64 {
	slice := strings.Split(data, sep)
	var result []int64
	for _, v := range slice {
		result = append(result, cast.ToInt64(v))
	}
	return result
}

func IntContain(src int, dst []int) bool {
	for _, eachItem := range dst {
		if eachItem == src {
			return true
		}
	}
	return false
}

func Filter[T any](slice []T, predicate func(index int, item T) bool) []T {
	filtered := make([]T, 0)
	for index, item := range slice {
		if predicate(index, item) {
			filtered = append(filtered, item)
		}
	}
	return filtered
}

func Map[T any, C any](slice []T, mapper func(index int, item T) C) []C {
	mapped := make([]C, len(slice))
	for index, item := range slice {
		mapped[index] = mapper(index, item)
	}
	return mapped
}

func MaxScore[T any](slice []T, score func(i int, a T) float64) T {
	max := slice[0]
	maIndex := 0
	for i, item := range slice {
		if score(i, item) > score(maIndex, max) {
			max = item
			maIndex = i
		}
	}
	return max
}

func MapAndFilter[T any, C any](slice []T, mapper func(item T) C, predicate func(item T) bool) []C {
	mapped := make([]C, 0)
	for _, item := range slice {
		if predicate(item) {
			mapped = append(mapped, mapper(item))
		}
	}
	return mapped
}

func If[T any](condition bool, trueVal, falseVal T) T {
	if condition {
		return trueVal
	}
	return falseVal
}

// 去除特殊字符 转小写
func UniformText(text string) string {
	// 使用正则表达式替换字符串
	result := allpuncReg.ReplaceAllString(text, "")

	return strings.ToLower(result)
}

func CheckPort(address string, port int) bool {
	conn, err := net.Dial("tcp", address+":"+strconv.Itoa(port))
	if err != nil {
		fmt.Printf("Port %s is available\n", port)
		return true
	}
	conn.Close()
	fmt.Printf("Port %s is already in use\n", port)
	return false
}

// Contains checks if an element is in a collection using generics.
func Contains[T comparable](collection []T, element T) bool {
	for _, item := range collection {
		if item == element {
			return true
		}
	}
	return false
}

func Interface2S(value any) (result string) {
	if value == nil {
		return ""
	}

	switch value := value.(type) {

	case []byte: // 取缔 []uint8 --> string
		result = string(value)

	case int64:
		result = strconv.FormatInt(value, 10)

	case float64:
		result = strconv.FormatFloat(value, 'f', -1, 64)

	case json.Number:
		result = value.String()

	case string:
		result = value

	default:
		newValue, _ := json.Marshal(value)
		result = string(newValue)
	}

	return
}

func Interface2I64(value any) int64 {
	if value == nil {
		return 0
	}
	var result int64
	var err error
	switch value := value.(type) {

	case int64:
		result = value
	case int32:
		result = int64(value)

	case float64:
		result = int64(value)

	case string:
		result, err = strconv.ParseInt(value, 10, 64)
		if err != nil {
			fmt.Errorf(err.Error())
		}
	case int:
		result = int64(value)
	case json.Number:
		parsedValue, err := value.Int64()
		if err != nil {
			fmt.Println("Error parsing json.Number to int64:", err)
			return 0
		}
		result = parsedValue

	default:
	}

	return result
}

func Interface2F64(value any) float64 {
	if value == nil {
		return 0.0
	}
	var result float64
	switch value := value.(type) {
	case int:
		result = float64(value)
	case int8:
		result = float64(value)
	case int16:
		result = float64(value)
	case int32:
		result = float64(value)
	case int64:
		result = float64(value)
	case uint:
		result = float64(value)
	case uint8:
		result = float64(value)
	case uint16:
		result = float64(value)
	case uint32:
		result = float64(value)
	case uint64:
		result = float64(value)
	case float32:
		result = float64(value)
	case float64:
		result = value
	case string:
		parsedValue, err := strconv.ParseFloat(value, 64)
		if err != nil {
			fmt.Println("Error parsing string to float64:", err)
			return 0.0
		}
		result = parsedValue
	case json.Number:
		parsedValue, err := value.Float64()
		if err != nil {
			fmt.Println("Error parsing json.Number to float64:", err)
			return 0.0
		}
		result = parsedValue
	default:
		fmt.Println("Unsupported type")
	}
	return result
}

func Interface2Bool(value any) bool {
	if value == nil {
		return false
	}

	switch v := value.(type) {
	case bool:
		return v
	case int:
		return v != 0
	case int8:
		return v != 0
	case int16:
		return v != 0
	case int32:
		return v != 0
	case int64:
		return v != 0
	case uint:
		return v != 0
	case uint8:
		return v != 0
	case uint16:
		return v != 0
	case uint32:
		return v != 0
	case uint64:
		return v != 0
	case float32:
		return v != 0.0
	case float64:
		return v != 0.0
	case string:
		// 尝试解析为布尔（兼容 "true", "1", "false", "0" 等）
		if b, err := strconv.ParseBool(v); err == nil {
			return b
		}
		// 尝试解析为数字（非零值返回 true）
		if num, err := strconv.ParseFloat(v, 64); err == nil {
			return num != 0
		}
		// 无法解析的字符串统一返回 false
		return false
	default:
		fmt.Printf("Unsupported type for bool conversion: %T\n", v)
		return false
	}
}

func RemovePunctuation(text string) string {
	punctuation := "!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~，。？！"
	punctuationMap := make(map[rune]bool)
	for _, char := range punctuation {
		punctuationMap[char] = true
	}

	var result strings.Builder
	for _, char := range text {
		if !punctuationMap[char] {
			result.WriteRune(char)
		}
	}

	return result.String()
}

func TruncateString(s string, maxLength int) string {
	if utf8.RuneCountInString(s) <= maxLength {
		return s
	}
	runes := []rune(s)
	return string(runes[:maxLength])
}

func Normalize(weights []float32) ([]float32, error) {
	sum := float32(0)
	for i, weight := range weights {
		if math.IsNaN(float64(weight)) {
			weights[i] = 0
			weight = 0
		}
		sum += weight
	}

	if sum == 0 {
		return weights, nil
	}

	// if math.IsNaN(float64(sum)) {
	// 	return weights, fmt.Errorf("sum is NaN, 存在无法识别的特殊字符")
	// }

	normalizedWeights := make([]float32, len(weights))
	for i, weight := range weights {
		if math.IsNaN(float64(weight)) {
			normalizedWeights[i] = 0
			continue
		}
		normalizedWeights[i] = weight / sum
	}

	return normalizedWeights, nil
}

func DeepCopyMap(src *map[string]any) *map[string]any {
	if src == nil {
		return nil
	}
	dst := make(map[string]any)
	for k, v := range *src {
		// 递归处理嵌套的 map[string]any
		if innerMap, ok := v.(map[string]any); ok {
			// 创建嵌套 map 的副本
			nestedCopy := DeepCopyMap(&innerMap)
			dst[k] = *nestedCopy
		} else {
			// 直接赋值非 map 类型
			dst[k] = v
		}
	}
	return &dst
}

// memoryUsageTracker 用于跟踪函数的内存使用情况
func MemoryUsageTracker(funcName string) func() {
	var mBefore runtime.MemStats
	runtime.ReadMemStats(&mBefore)

	return func() {
		var mAfter runtime.MemStats
		runtime.ReadMemStats(&mAfter)
		allocDiff := mAfter.Alloc - mBefore.Alloc
		fmt.Printf("%s 函数执行期间内存申请量: %v KB\n", funcName, allocDiff/1024)
	}
}

func Mem(f func()) int64 {
	var m1 runtime.MemStats
	runtime.ReadMemStats(&m1) // 获取内存统计信息

	if f == nil {
		return int64(m1.Alloc)
	}

	f()

	var m2 runtime.MemStats
	runtime.ReadMemStats(&m2)
	return int64(m2.Alloc - m1.Alloc)
}

func Objects(f func()) int64 {
	var m1 runtime.MemStats
	runtime.ReadMemStats(&m1) // 获取内存统计信息

	pre := m1.Mallocs - m1.Frees
	if f == nil {
		return int64(pre)
	}

	f()

	var m2 runtime.MemStats
	runtime.ReadMemStats(&m2)
	posts := m2.Mallocs - m2.Frees
	return int64(posts - pre)
}

// MaxValues 泛型函数，用于找出切片中的最大值
func MaxValues[T constraints.Ordered](values []T) (T, error) {
	var zero T
	if len(values) > 0 {
		maxVal := values[0]
		for _, value := range values[1:] {
			if value > maxVal {
				maxVal = value
			}
		}
		return maxVal, nil
	}
	return zero, fmt.Errorf("slice is empty")
}
