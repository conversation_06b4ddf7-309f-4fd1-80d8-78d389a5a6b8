package common

import (
	// "fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// extractYearsList 提取文本中的年份列表
func ExtractYearsList(text string) []int {
	var result []int
	i := 0
	for i < len(text) {
		if text[i] >= '0' && text[i] <= '9' {
			start := i
			for i < len(text) && text[i] >= '0' && text[i] <= '9' {
				i++
			}
			numStr := text[start:i]
			num, err := strconv.Atoi(numStr)
			if err == nil && num >= 2000 && num <= 2025 {
				result = append(result, num)
			}
		} else {
			i++
		}
	}
	return result
}

// isValidDate 函数用于验证日期的有效性
func isValidDate(year, month, day string) bool {
	if year != "" {
		y, err := strconv.Atoi(strings.TrimRight(year, "年")) // 1970年 - 2099年
		if err != nil || y < 1970 || y > 2099 {
			return false
		}
	}
	if month != "" {
		m, err := strconv.Atoi(strings.TrimRight(month, "月")) // 1月 - 12月
		if err != nil || m < 1 || m > 12 {
			return false
		}
	}
	if day != "" {
		d, err := strconv.Atoi(strings.TrimRight(day, "日")) // 1日 - 31日
		if err != nil || d < 1 || d > 31 {
			return false
		}
	}
	return true
}

// extractDateRanges 函数用于从输入字符串中提取日期范围, 格式:2025年04月18日-2025年04月20日, 允许年月日部分字段缺失
func extractDateRangeStr(s string) []string {
	// 定义正则表达式
	re := regexp.MustCompile(`(\d*年)?(\d*月)?(\d*日)?[-_———](\d*年)?(\d*月)?(\d*日)?`) // 支持分隔符为"-","_","—",——"四种
	// 查找所有匹配项
	matches := re.FindAllString(s, -1)
	// 过滤掉不符合条件的匹配项，即符号前后至少有一个年、月或日字段
	var validMatches []string
	for _, match := range matches {
		parts := re.FindStringSubmatch(match)
		if (parts[1] != "" || parts[2] != "" || parts[3] != "") && (parts[4] != "" || parts[5] != "" || parts[6] != "") {
			if isValidDate(parts[1], parts[2], parts[3]) && isValidDate(parts[4], parts[5], parts[6]) {
				newMatch := strings.NewReplacer("_", "-", "——", "-", "—", "-").Replace(match) // 替换统一符号
				validMatches = append(validMatches, newMatch)
			}
		}
	}
	return validMatches
}

// parseDate 解析日期字符串, 处理缺失部分信息的情况
func parseDate(dateStr string, defaultYear, defaultMonth, defaultDay int) (int, int, int) {
	year := defaultYear
	month := defaultMonth
	day := defaultDay

	if strings.Contains(dateStr, "年") {
		parts := strings.Split(dateStr, "年")
		year, _ = strconv.Atoi(parts[0])
		dateStr = parts[1]
	}
	if strings.Contains(dateStr, "月") {
		parts := strings.Split(dateStr, "月")
		month, _ = strconv.Atoi(parts[0])
		dateStr = parts[1]
	}
	if strings.Contains(dateStr, "日") {
		day, _ = strconv.Atoi(strings.TrimSuffix(dateStr, "日"))
	}
	return year, month, day
}

// removeDuplicates 对整数切片去重
func removeDuplicates(slice []int) []int {
	unique := make(map[int]bool)
	result := []int{}
	for _, num := range slice {
		if !unique[num] {
			result = append(result, num)
			unique[num] = true
		}
	}
	return result
}

// extractDateRange 提取日期范围信息, 格式:2025年04月18日-2025年04月20日, 允许年月日部分字段缺失
func extractDateRangeList(dateStr string) (years []int, months []int, days []int) {
	if dateStr == "-" {
		return []int{}, []int{}, []int{}
	}

	parts := strings.Split(dateStr, "-")
	if len(parts) != 2 {
		return
	}
	startStr := parts[0]
	endStr := parts[1]

	now := time.Now()
	currentYear := now.Year()
	currentMonth := int(now.Month())

	startYear, startMonth, startDay := parseDate(startStr, currentYear, currentMonth, 1)
	endYear, endMonth, endDay := parseDate(endStr, startYear, startMonth, 1)

	// 处理缺失年的情况
	if startYear == 0 && endYear == 0 {
		startYear = currentYear
		endYear = currentYear
	}
	// 处理缺失年月的情况
	if startYear == currentYear && endYear == currentYear && startMonth == currentMonth && endMonth == currentMonth && startDay == 1 && endDay == 1 {
		startDay = 1
		endDay = time.Date(currentYear, time.Month(currentMonth+1), 0, 0, 0, 0, 0, time.UTC).Day()
	}
	// 处理缺失月日的情况
	if startMonth == 1 && endMonth == 1 && startDay == 1 && endDay == 1 {
		startMonth = 1
		endMonth = 12
		startDay = 1
		for y := startYear; y <= endYear; y++ {
			years = append(years, y)
			for m := 1; m <= 12; m++ {
				months = append(months, m)
				daysInMonth := time.Date(y, time.Month(m+1), 0, 0, 0, 0, 0, time.UTC).Day()
				for d := 1; d <= daysInMonth; d++ {
					days = append(days, d)
				}
			}
		}
	} else {
		// 处理缺失月的情况
		if startMonth == endMonth && startDay == 1 && endDay == 1 {
			startMonth = 1
			endMonth = 12
		}
		// 处理缺失日的情况
		if startDay == 1 && endDay == 1 {
			startDay = 1
			endDay = time.Date(endYear, time.Month(endMonth+1), 0, 0, 0, 0, 0, time.UTC).Day()
		}

		start := time.Date(startYear, time.Month(startMonth), startDay, 0, 0, 0, 0, time.UTC)
		end := time.Date(endYear, time.Month(endMonth), endDay, 0, 0, 0, 0, time.UTC)

		current := start
		for !current.After(end) {
			years = append(years, current.Year())
			months = append(months, int(current.Month()))
			days = append(days, current.Day())
			current = current.AddDate(0, 0, 1)
		}
	}

	// 对结果去重
	years = removeDuplicates(years)
	months = removeDuplicates(months)
	days = removeDuplicates(days)

	return
}

// extractDates 函数用于从输入字符串中提取日期, 格式:2025年04月18日, 允许年月日部分字段缺失
func extractDateStr(s string) []string {
	// 定义正则表达式
	re := regexp.MustCompile(`(\d*年)?(\d*月)?(\d*日)?`)
	// 查找所有匹配项
	matches := re.FindAllString(s, -1)
	var validMatches []string
	for _, match := range matches {
		parts := re.FindStringSubmatch(match)
		if isValidDate(parts[1], parts[2], parts[3]) && match != "" {
			validMatches = append(validMatches, match)
		}
	}
	return validMatches
}

// extractDate 提取日期信息, 格式:2025年04月18日, 允许年月日部分字段缺失
func extractDateList(s string) ([]int, []int, []int) {
	re := regexp.MustCompile(`(?:(\d{4})年)?(?:(\d{1,2})月)?(?:(\d{1,2})日)?`)
	matches := re.FindStringSubmatch(s)

	yearList := []int{}
	monthList := []int{}
	dayList := []int{}

	// 获取当前年份
	currentYear := time.Now().Year()

	if matches[1] != "" {
		year, _ := strconv.Atoi(matches[1])
		yearList = append(yearList, year)
	} else {
		yearList = append(yearList, currentYear)
	}

	if matches[2] != "" {
		month, _ := strconv.Atoi(matches[2])
		monthList = append(monthList, month)
	} else {
		for i := 1; i <= 12; i++ {
			monthList = append(monthList, i)
		}
	}

	if matches[3] != "" {
		day, _ := strconv.Atoi(matches[3])
		dayList = append(dayList, day)
	} else {
		for i := 1; i <= 31; i++ {
			dayList = append(dayList, i)
		}
	}

	return yearList, monthList, dayList
}

// extractTime 函数
func ExtractRangeTime(text string) ([]int, []int, []int) {
	timeStrs := extractDateRangeStr(text)
	allYears := []int{}
	allMonths := []int{}
	allDays := []int{}

	for _, timeStr := range timeStrs {
		yearList, monthList, dayList := extractDateRangeList(timeStr)
		allYears = append(allYears, yearList...)
		allMonths = append(allMonths, monthList...)
		allDays = append(allDays, dayList...)
	}

	// 去重
	uniqueYears := removeDuplicates(allYears)
	uniqueMonths := removeDuplicates(allMonths)
	uniqueDays := removeDuplicates(allDays)

	return uniqueYears, uniqueMonths, uniqueDays
}

// extractTime 函数
func ExtractTime(text string) ([]int, []int, []int) {
	timeStrs := extractDateStr(text)
	allYears := []int{}
	allMonths := []int{}
	allDays := []int{}

	for _, timeStr := range timeStrs {
		yearList, monthList, dayList := extractDateList(timeStr)
		allYears = append(allYears, yearList...)
		allMonths = append(allMonths, monthList...)
		allDays = append(allDays, dayList...)
	}

	// 去重
	uniqueYears := removeDuplicates(allYears)
	uniqueMonths := removeDuplicates(allMonths)
	uniqueDays := removeDuplicates(allDays)

	return uniqueYears, uniqueMonths, uniqueDays
}
