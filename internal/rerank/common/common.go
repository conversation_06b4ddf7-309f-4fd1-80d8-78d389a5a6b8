package common

import (
	"math"
	"sort"
	"strings"
	"sync"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/consts"
)

func LCS(x string, y string) int {
	m := len(x)
	n := len(y)
	c := make([][]int, m+1)
	for i := 0; i <= m; i++ {
		c[i] = make([]int, n+1)
	}

	for i := 1; i <= m; i++ {
		for j := 1; j <= n; j++ {
			if x[i-1] == y[j-1] {
				c[i][j] = c[i-1][j-1] + 1
			} else if c[i-1][j] > c[i][j-1] {
				c[i][j] = c[i-1][j]
			} else {
				c[i][j] = c[i][j-1]
			}
		}
	}

	return c[m][n]
}

func JaccardDistance(set1, set2 string) float64 {
	// 将字符串转换为字符切片
	slice1 := strings.Split(set1, "")
	slice2 := strings.Split(set2, "")

	// 创建一个map来记录元素出现的次数
	m1 := make(map[string]int)
	m2 := make(map[string]int)

	// 统计每个字符出现的次数
	for _, v := range slice1 {
		m1[v]++
	}
	for _, v := range slice2 {
		m2[v]++
	}

	// 计算交集的大小
	intersection := 0
	for k, v := range m1 {
		if m2[k] > 0 {
			intersection += Min(v, m2[k])
		}
	}

	// 计算并集的大小
	union := len(slice1) + len(slice2) - intersection

	// 计算Jaccard系数
	jaccard := float64(intersection) / float64(union)

	return jaccard
}

func JaccardDistance2(inputX, inputY string) float64 {
	setX := make(map[rune]struct{})
	setY := make(map[rune]struct{})

	for _, char := range inputX {
		setX[char] = struct{}{}
	}
	for _, char := range inputY {
		setY[char] = struct{}{}
	}

	intersectionSize := 0
	unionSize := len(setX) + len(setY)

	for char := range setX {
		if _, exists := setY[char]; exists {
			intersectionSize++
		}
	}

	unionSize -= intersectionSize
	if unionSize == 0 {
		return 0
	}

	return float64(intersectionSize) / float64(unionSize)
}

func Round(v float64, prec int) float64 {
	powerOfTen := math.Pow(10, float64(prec))
	return math.Round(v*powerOfTen) / powerOfTen
}

func TextProcess(texts []string) []string {
	var newSentences []string
	var wg sync.WaitGroup
	retChan := make(chan string, len(texts))

	for _, v := range texts {
		wg.Add(1)
		str := v

		go func(wg *sync.WaitGroup, text string) {
			defer wg.Done()

			var sb strings.Builder

			for _, ch := range text {
				if consts.Biaodians[ch] {
					sb.WriteRune(' ')
				} else if consts.SpecialChars[ch] {
					continue
				} else {
					sb.WriteRune(ch)
				}
			}

			retChan <- sb.String()

		}(&wg, str)
	}

	wg.Wait()
	close(retChan)

	for v := range retChan {
		newSentences = append(newSentences, v)
	}

	return newSentences

}

func TextFilter(text string) string {
	var sb strings.Builder

	for _, ch := range text {
		if consts.Biaodians[ch] {
			sb.WriteRune(' ')
		} else if consts.SpecialChars[ch] {
			continue
		} else {
			sb.WriteRune(ch)
		}
	}

	return sb.String()
}

func RemoveDuplicates[T comparable](arr []T) []T {
	seen := make(map[T]bool)
	result := []T{}

	for _, value := range arr {
		if !seen[value] {
			seen[value] = true
			result = append(result, value)
		}
	}

	return result
}

// SetDifference 计算两个集合的差集
func SetDifference[T comparable](setA, setB []T) []T {
	diff := []T{}
	setBMap := make(map[T]struct{})

	// 将 setB 的元素存入 map 中
	for _, item := range setB {
		setBMap[item] = struct{}{}
	}

	// 遍历 setA，找出不在 setB 中的元素
	for _, item := range setA {
		if _, found := setBMap[item]; !found {
			diff = append(diff, item)
		}
	}

	return diff
}

func KeywordFeat(queryKeywords, contentKeywords []string, content string) (float64, float64, float64, float64) {
	var keywordsLength int
	qkLen := len(queryKeywords)
	ckLen := len(contentKeywords)
	qkSet := make(map[string]float64, qkLen)
	ckSet := make(map[string]float64, ckLen)

	for _, v := range queryKeywords {
		qkSet[v] = 1 / float64(qkLen)
	}

	for _, v := range contentKeywords {
		ckSet[v] = 1 / float64(ckLen)
	}

	queryFeat := make([]float64, 10)
	contentFeat := make([]float64, 10)

	// 找出交集
	var intersection []string
	for k := range qkSet {
		if _, exists := ckSet[k]; exists {
			intersection = append(intersection, k)
		}
	}

	// 取前10个元素
	if len(intersection) > 10 {
		intersection = intersection[:10]
	}

	var keywordProp float64
	if len(intersection) == 0 {
		keywordProp = 0
	} else {
		keywordProp = float64(len(intersection)) / float64(qkLen)
	}

	for _, v := range queryKeywords {
		if strings.Contains(content, v) {
			keywordsLength++
		}
	}

	intersectionMap := make([]*bean.Keyword, 0)
	if len(intersection) > 0 {
		for _, v := range intersection {
			intersectionMap = append(intersectionMap, &bean.Keyword{
				Key:    v,
				Weight: qkSet[v],
			})
		}

		sort.Slice(intersectionMap, func(i, j int) bool {
			return intersectionMap[i].Weight > intersectionMap[j].Weight
		})

		intersection = Map(intersectionMap, func(index int, item *bean.Keyword) string {
			return item.Key
		})

		for idx, val := range intersection {
			queryFeat[idx] = qkSet[val]
			contentFeat[idx] = ckSet[val]
		}
	}

	var keywordLen float64
	if qkLen == 0 {
		keywordLen = 0
	} else {
		keywordLen = float64(keywordsLength / qkLen)
	}

	return Sum(queryFeat), Sum(contentFeat), keywordProp, keywordLen
}

func contains(content, keyword string) bool {
	return len(keyword) > 0 && len(content) > 0 && len(content) >= len(keyword) && content[:len(keyword)] == keyword
}

func sum(slice []float64) float64 {
	total := 0.0
	for _, value := range slice {
		total += value
	}
	return total
}

func KeywordFeat2(queryKeywords, contentKeywords []string, content string) (float64, float64, float64, float64) {
	// Initialize weights for query and content keywords
	queryWeights := make(map[string]float64)
	contentWeights := make(map[string]float64)
	for _, keyword := range queryKeywords {
		queryWeights[keyword] = 1 / float64(len(queryKeywords))
	}
	for _, keyword := range contentKeywords {
		contentWeights[keyword] = 1 / float64(len(contentKeywords))
	}

	// Initialize feature slices
	queryFeat := make([]float64, 10)
	contentFeat := make([]float64, 10)

	// Find intersection of query and content keywords
	intersection := make(map[string]bool)
	for _, keyword := range queryKeywords {
		if _, exists := contentWeights[keyword]; exists {
			intersection[keyword] = true
		}
	}

	// Convert map keys to slice and sort by weight
	var sortedIntersection []string
	for keyword := range intersection {
		sortedIntersection = append(sortedIntersection, keyword)
	}
	sort.Slice(sortedIntersection, func(i, j int) bool {
		return queryWeights[sortedIntersection[i]] > queryWeights[sortedIntersection[j]]
	})

	// Calculate keyword proportion and length in content
	keywordProp := 0.0
	if len(queryKeywords) == 0 {
		keywordProp = 0
	} else {
		keywordProp = float64(len(sortedIntersection)) / float64(len(queryKeywords))
	}
	keywordLength := 0.0
	for _, keyword := range queryKeywords {
		if contains(content, keyword) {
			keywordLength++
		}
	}
	if len(queryKeywords) == 0 {
		keywordLength = 0
	} else {
		keywordLength /= float64(len(queryKeywords))
	}

	// Populate features based on sorted intersection
	for i, keyword := range sortedIntersection {
		if i >= 10 {
			break
		}
		queryFeat[i] = queryWeights[keyword]
		contentFeat[i] = contentWeights[keyword]
	}

	// Return the sum of features and calculated values
	return sum(queryFeat), sum(contentFeat), keywordProp, keywordLength
}
