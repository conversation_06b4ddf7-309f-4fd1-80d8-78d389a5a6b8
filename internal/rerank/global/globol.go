package global

import (
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/async/antspool"
	tokenizerv2_1 "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/tokenizer_v2_1"
)

var (
	IFLYS_SAMPLE_Tokenizer *tokenizerv2_1.TokenizerWrapper
	IFLYS_TokenModelServer *tokenizerv2_1.AsyncTokenizer
	Inst                   *antspool.AsyncAntsPool
)

var TermWeightVersion string = "termweight_v20231221"

func Init() {
	// 初始化资源和公共实例
	IFLYS_TokenModelServer = tokenizerv2_1.G_tokenizer.DefaultInstance()
	IFLYS_SAMPLE_Tokenizer = tokenizerv2_1.NewSimpleTokenizer(tokenizerv2_1.G_tokenizer.DefaultConfig().Path, tokenizerv2_1.G_tokenizer.DefaultConfig().UseLocal)

	Inst = goboot.AntsPool().DefaultInstance()
}
