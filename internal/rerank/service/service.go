package service

import (
	"context"
	"fmt"
	"sync"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/consts"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/service/rank"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	concurrent_map "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/concurrent/map"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
	"golang.org/x/sync/errgroup"
)

type RerankService struct {
	Ctx           *pandora_context.PandoraContext
	medicalRerank *rank.MedicalRerank
	commonRerank  *rank.CommonRerank
	newsRerank    *rank.NewsRerank
	sportsRerank  *rank.SportsRerank
	Resp          *pandora_proto.PandoraResponseMessage[bean.RespPayLoad]
	Req           *pandora_proto.PandoraRequestMessage[bean.RequestPayLoad]
}

var rerankOnce sync.Once

// 初始化Rerank服务
func (s *RerankService) Init() {
	rerankOnce.Do(func() {
		s.medicalRerank = &rank.MedicalRerank{Ctx: s.Ctx}
		s.commonRerank = &rank.CommonRerank{Ctx: s.Ctx}
		s.newsRerank = &rank.NewsRerank{Ctx: s.Ctx}
		s.sportsRerank = &rank.SportsRerank{Ctx: s.Ctx}
	})
}

// checkRequest 检查请求的有效性
func (s *RerankService) checkRequest(span *span.Span) *errtypes.SelfError {
	checkSpan := span.AddSpan("请求检查")
	defer checkSpan.Finish()

	// 检查请求数据是否为空
	if len(s.Req.Payload.Data) == 0 {
		return selferrors.RerankError_ReqDataEmptyError
	}

	// 检查第一个查询是否为空
	if len(s.Req.Payload.Data[0].Query) == 0 {
		return selferrors.RerankError_ReqQueryEmptyError
	}

	// 检查文档ID是否重复
	for _, d := range s.Req.Payload.Data {
		idsMap := make(map[int64]struct{})
		for _, doc := range d.Docs {
			id := common.Interface2I64((*doc)["id"])
			if _, ok := idsMap[id]; ok {
				return selferrors.RerankError_ReqIDDuplicateError
			}
			idsMap[id] = struct{}{}
		}
	}

	return nil
}

// rank 执行重排服务
func (s *RerankService) rank() {
	span := s.Ctx.RootSpan().AddSpan("执行重排服务")
	defer func() {
		span.TraceInfo("requestInfo", s.Req)
		span.TraceInfo("responseInfo", s.Resp)
		span.Finish()
	}()

	// 检查请求
	if err := s.checkRequest(span); err != nil {
		s.handleError(err)
		return
	}

	// 处理热点搜索
	if s.Req.Payload.Intent == consts.HOTSEARCH && s.Req.Payload.Model == consts.SPORTS {
		s.handleHotSearch()
		return
	}

	// 执行重排
	results, err := s.processRanking(span)
	if err != nil {
		s.handleError(selferrors.RerankError_UnknowError)
		return
	}

	s.Resp.Payload.Result = results
}

// handleHotSearch 处理热点搜索，直接返回请求数据
func (s *RerankService) handleHotSearch() {
	results := make([]*bean.QueryData, len(s.Req.Payload.Data))
	for i, d := range s.Req.Payload.Data {
		results[i] = &bean.QueryData{
			Query: d.Query,
			Type:  d.Type,
			Extra: d.Extra,
			Docs:  d.Docs,
		}
	}
	s.Resp.Header.Success = "hot_search不走重排，直接返回请求数据"
	s.Resp.Payload.Result = results
}

func (s *RerankService) processRanking(span *span.Span) ([]*bean.QueryData, error) {
	domainID := s.Req.Payload.Model
	resultMap := concurrent_map.New[int, []*map[string]any]()
	goboot.Logger().DefaultInstance().Info(fmt.Sprintf("traceId:%s, 重排领域id：%s\n", s.Req.Header.TraceId, domainID))

	ctx, cancel := context.WithCancel(s.Ctx.GetContext())
	defer cancel()

	var eg errgroup.Group
	for i, data := range s.Req.Payload.Data {
		i, data := i, data // 创建局部变量以避免并发问题
		eg.Go(func() error {
			select {
			case <-ctx.Done():
				return nil // 如果上下文被取消，直接返回
			default:
				rerankRsts, err := s.performRanking(span, domainID, data)
				if err != nil {
					cancel() // 取消其他任务
					return fmt.Errorf("重排任务失败 (index=%d, query=%s): %s", i, data.Query, err.Error())
				}
				resultMap.Set(i, rerankRsts)
				return nil
			}
		})
	}

	if err := eg.Wait(); err != nil {
		goboot.Logger().DefaultInstance().Error(fmt.Sprintf("重排过程中发生错误: %v", err))
		return nil, err
	}

	return s.collectResults(resultMap), nil
}

func (s *RerankService) performRanking(span *span.Span, domainID string, data *bean.QueryData) ([]*map[string]any, error) {
	switch domainID {
	case consts.MEDICAL, consts.V1120, consts.Medical_V1120, consts.HEALTH:
		return s.medicalRerank.Rank1120(span, s.Req, data)
	case consts.V1218, consts.Medical_V1218:
		return s.medicalRerank.Rank1218(span, s.Req, data)
	case consts.Medical_V0310, consts.Medical_V0620:
		return s.medicalRerank.Rank0310(span, s.Req, data)
	case consts.SPORTS:
		return s.sportsRerank.Rank(span, s.Req, data)
	case consts.News_V20250421, consts.News_V20250522:
		return s.newsRerank.Rank20250421(span, s.Req, data)
	case consts.Medical_V0516:
		return s.medicalRerank.Rank0516(span, s.Req, data)
	default:
		return s.commonRerank.Rank(span, s.Req, data)
	}
}

func (s *RerankService) collectResults(resultMap *concurrent_map.ConcurrentMap[int, []*map[string]any]) []*bean.QueryData {
	results := make([]*bean.QueryData, 0, len(s.Req.Payload.Data))
	for i, data := range s.Req.Payload.Data {
		if value, ok := resultMap.Get(i); ok {
			results = append(results, &bean.QueryData{
				Query: data.Query,
				Type:  common.If(data.Type == "", "raw", data.Type),
				Extra: common.If(len(data.Extra) == 0, []*map[string]any{}, data.Extra),
				Docs:  value,
			})
		}
	}
	return results
}

// HttpHandler HTTP请求处理入口
func (s *RerankService) HttpHandler() {
	span := s.Ctx.RootSpan().AddSpan(s.Req.Header.AppId)
	defer span.Finish()

	s.rank()
}

// handleError 统一处理错误
func (s *RerankService) handleError(err *errtypes.SelfError) {
	s.Resp.Header.Code = err.Code()
	s.Resp.Header.Success = err.String()
}
