package rank

import (
	"fmt"
	"math"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/consts"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	"github.com/dexyk/stringosim"
)

type CommonRerank struct {
	Ctx *pandora_context.PandoraContext
}

func (r *CommonRerank) answerStage(data []*map[string]interface{}, step float64, rankScoreThreshold float64) [][]*map[string]interface{} {
	// 得分分桶

	scoreHist := arange(rankScoreThreshold, 1.05, step)
	shLen := len(scoreHist)

	recordsBox := make([][]*map[string]interface{}, 0)
	if step == 0 {
		var recordBox []*map[string]interface{}
		if step == 0 || step == 1 {
			for i := 0; i < len(data); i++ {
				score := common.Interface2F64((*data[i])["_rank_score"])
				if score >= rankScoreThreshold {
					(*data[i])["_rerank_score"] = rankScoreThreshold
					recordBox = append(recordBox, data[i])
				}
			}
		}
		if len(recordBox) > 0 {
			recordsBox = append(recordsBox, recordBox)
		}
		return recordsBox
	}

	// if len(scoreHist) > 0 {
	// 	scoreHist[0] = scoreHist[0] - 0.01
	// }

	for idx := range scoreHist {
		var ec float64
		sc := scoreHist[idx]
		if idx >= shLen-1 {
			ec = 1 + step
		} else {
			ec = scoreHist[idx+1]
		}

		var recordBox []*map[string]interface{}
		for idx1 := 0; idx1 < len(data); idx1++ {
			score := common.Interface2F64((*data[idx1])["_rank_score"])
			if score == rankScoreThreshold && score >= sc && score <= ec {
				(*data[idx1])["_rerank_score"] = score
				recordBox = append(recordBox, data[idx1])
			} else {
				if score > sc && score <= ec {
					(*data[idx1])["_rerank_score"] = sc
					recordBox = append(recordBox, data[idx1])
				}
			}

		}

		if len(recordBox) > 0 {
			recordsBox = append(recordsBox, recordBox)
		}
	}

	return recordsBox
}

func (r *CommonRerank) calcRerankScore(id int64, query string, timeDiff int64, summaryLen int, score, lcs, queryScore, summaryScore, keywordProp, keywordsLen, summaryKeywords,
	qualityScore, jaccardDist, levenshteinDist, suppressWeight float64, authority, pr, freq, top, strongTimeliness, domainFreq, realmFreq int32) (float64, map[string]any) {
	summaryScore = math.Tanh(float64(summaryLen)/10000) * 1.5
	lcsScore := float64(lcs) * 0.01 * suppressWeight
	jaccardScore := jaccardDist * 0.01 * suppressWeight
	keywordPropScore := keywordProp * 0.01
	keywordsLenScore := keywordsLen * 0.01
	// keywordsSummaryScore := summaryKeywords * 0.01
	// authorityScore := math.Tanh(float64(authority)) * 0.01
	levenshteinDistScore := common.Min(levenshteinDist, float64(utf8.RuneCountInString(query))) * -0.003 / suppressWeight
	// domainFreqScore := math.Tanh(0.00000001*float64(domainFreq)) * 0.1

	var rankScore float64 = 0
	var rerankScore float64 = 0
	scoreDetail := make(map[string]any, 0)
	scoreDetail["summary_score"] = summaryScore // 0.014538602311465032
	rankScore += summaryScore
	scoreDetail["lcs_score"] = lcsScore // 0.01
	rankScore += lcsScore

	scoreDetail["jaccard_score"] = jaccardScore // 0.002
	rankScore += jaccardScore

	scoreDetail["keyword_prop_score"] = keywordPropScore //0.005
	rankScore += keywordPropScore

	scoreDetail["keywords_length_score"] = keywordsLenScore //0.005
	rankScore += keywordsLenScore

	// scoreDetail["keywords_in_summary_score"] = keywordsSummaryScore //0.01
	// rankScore += keywordsSummaryScore

	// scoreDetail["authority_score"] = authorityScore //0
	// rankScore += authorityScore

	scoreDetail["levenshtein_dist_score"] = levenshteinDistScore //-0.009000000000000001
	rankScore += levenshteinDistScore

	// scoreDetail["domain_freq_score"] = domainFreqScore //0
	// rankScore += domainFreqScore

	rerankScore = rankScore*0.3 + score*18
	// fmt.Printf("id:%v, 分数1:%v, 分数2:%v, 分数3:%v\n", id, rankScore, score, rerankScore)

	scoreDetail["levenshtein_dist"] = levenshteinDist
	scoreDetail["summary_length"] = summaryLen
	scoreDetail["lcs"] = lcs
	scoreDetail["jaccard_dist"] = jaccardDist
	scoreDetail["keyword_prop"] = keywordProp
	scoreDetail["keyword_length"] = keywordsLen
	scoreDetail["score_factors"] = rerankScore
	scoreDetail["rank_score_"] = score
	scoreDetail["rank_score*18"] = score * 18

	return rerankScore, scoreDetail
}

func (r *CommonRerank) rerankProcess(req *bean.Request, data *map[string]interface{}, domainId string, query, category string, currentTime int64, score float64) (float64, map[string]any) {
	var Summary = common.Interface2S((*data)["summary"])
	var Content = common.Interface2S((*data)["content"])
	if Summary == "" {
		Summary = Content
	}

	summary := common.If(Summary == "", "", strings.ToLower(strings.TrimSpace(Summary)))

	ts := common.Interface2I64((*data)["post_ts"])

	timeDiff := currentTime - ts
	title := common.Interface2S((*data)["title"])
	title = strings.ToLower(title)

	summaryLen := utf8.RuneCountInString(summary)

	// 使用下划线分割标题字符串
	titleSegs := strings.Split(title, "_")

	// 根据下划线的数量进行不同的处理
	if len(titleSegs) >= 3 {
		// 保留前部分的段，去掉最后两个段
		title = strings.Join(titleSegs[:len(titleSegs)-2], "_")
	} else if len(titleSegs) == 2 {
		// 只保留第一个段
		title = titleSegs[0]
	}

	queryKeywords := common.SetDifference(config.Seg.CutStop(query), config.StopWords)
	titleKeywords := common.SetDifference(config.Seg.CutStop(title), config.StopWords)
	rerankKeywords := make([]string, 0)

	for _, item := range titleKeywords {
		rerankKeywords = append(rerankKeywords, item)
	}
	// fmt.Println(rerankKeywords)
	(*data)["_rerank_keywords"] = strings.Join(rerankKeywords, " ")
	strongTimeliness := 1

	// var wg sync.WaitGroup
	var levenshteinDist int
	var lcs, jaccardDist float64

	// wg.Add(3)
	// go func() {
	// 	defer wg.Done()

	lcs = float64(stringosim.LCS([]rune(query), []rune(title))) / float64(utf8.RuneCountInString(query))
	// data.Lcs = lcs
	// }()

	// go func() {
	// 	defer wg.Done()

	//jaccardDist = stringosim.Jaccard([]rune(query), []rune(title), []int{1})
	jaccardDist = common.JaccardDistance2(query, title)
	// slog.DebugF("id:%v,query:%s,title:%s,jaccard:%v\n", common.Interface2I64((*data)["id"]), query, title, jaccardDist)
	// }()

	// go func() {
	// 	defer wg.Done()

	levenshteinDist = stringosim.Levenshtein([]rune(query), []rune(title))
	// }()

	// wg.Wait()

	queryScore, summaryScore, keywordProp, keywordsLen := common.KeywordFeat2(queryKeywords, titleKeywords, title)

	summaryKeywords := 0
	for _, item := range queryKeywords {
		if strings.Contains(summary, item) {
			summaryKeywords++
		}
	}
	if len(queryKeywords) == 0 {
		summaryKeywords = 0
	} else {
		summaryKeywords = summaryKeywords / len(queryKeywords)
	}

	suppressWeight := 1.0
	if float64(len(title)) < float64(len(strings.Join(queryKeywords, "")))*0.4 {
		suppressWeight = 0.6
	}

	qualityScore := 0

	var domainFreq int32 = 0
	var authority int32 = 0
	var pr int32 = 0
	var top int32 = 0
	realmFreq := 0
	freq := 1
	rerankScore, scoreDetail := r.calcRerankScore(common.Interface2I64((*data)["id"]), query, timeDiff, summaryLen, float64(score), lcs, float64(queryScore), float64(summaryScore), float64(keywordProp), float64(keywordsLen), float64(summaryKeywords),
		float64(qualityScore), jaccardDist, float64(levenshteinDist), suppressWeight, authority, pr, int32(freq), top, int32(strongTimeliness), int32(domainFreq), int32(realmFreq))

	// 去除视频类新闻
	minusVideo := true

	for key := range consts.VideoKeywords {
		if strings.Contains(query, key) {
			minusVideo = false
			break
		}
	}

	if minusVideo {
		for key := range consts.VideoKeywords {
			if strings.Contains(summary, key) {
				rerankScore -= 2
			}
		}
	}
	scoreDetail["minusVideo"] = minusVideo

	// 领域分类
	// var realm string = ""
	// if len(category) > 0 && category == realm {
	// 	rerankScore = rerankScore * 1.5
	// }
	scoreDetail["score_without_level_score"] = rerankScore

	var docLevel int = 1

	// 质量等级
	docLevel = int(common.Interface2I64((*data)["q_user"]))
	if docLevel == 0 {
		docLevel = int(common.Interface2I64((*data)["q_level"]))
	}
	if docLevel != 4 && docLevel != 3 && docLevel != 2 && docLevel != 1 {
		docLevel = 1
	}

	var authorScore int32 = 0
	levelScore := CalcScore(float64(authorScore), docLevel)
	scoreDetail["level_socre"] = levelScore
	rerankScore += levelScore

	// 历史上的今天和百科权重
	var baikeWeight float64 = 0.0
	url := common.Interface2S((*data)["url"])
	if value, ok := config.G_ParamModule.DefaultInstance().Domain2BaikeWeight[consts.HistoryToday]; ok {
		baikeWeight = value
	}
	if domainId == consts.HistoryToday && common.Contains(consts.Date_baikeids, common.Interface2I64((*data)["id"])) {
		rerankScore += float64(baikeWeight)
	} else if strings.Contains(url, "baike.baidu.com") {
		rerankScore += float64(baikeWeight)
	}

	return rerankScore, scoreDetail
}

func (r *CommonRerank) Rank(span *span.Span, req *bean.Request, data *bean.QueryData) ([]*map[string]interface{}, error) {
	rankSpan := span.AddSpan("重排排序")
	defer rankSpan.Finish()
	currentTime := time.Now().Unix() * 1000
	var rerankScore = make([]*map[string]interface{}, 0)
	defer func() {
		if r := recover(); r != nil {
			fmt.Println("Recovered from panic:", r)
		}
	}()

	id2ScoreDetail := make(map[int64]*map[string]any, 0)
	defer rankSpan.TraceInfo("scoreDetail", id2ScoreDetail)

	// 将精排结果进行分段
	docs := data.Docs
	var tmpData [][]*map[string]interface{}
	tmpData = r.answerStage(docs, req.Payload.Step, req.Payload.ScoreThreshold)

	// 转换为小写并去除首尾空格
	query := strings.ToLower(strings.TrimSpace(data.Query))
	var category string
	timeStrength := 1
	addScore := 4
	for i := len(tmpData) - 1; i >= 0; i-- {
		datas := tmpData[i]
		var tsList []int
		dataCount := len(datas)
		// 将时间倒排给权重
		for _, v := range datas {
			postTs := common.Interface2I64((*v)["post_ts"])
			tsList = append(tsList, -int(postTs))
		}

		// 统计时间重复率
		sort.Slice(tsList, func(i, j int) bool {
			return tsList[i] < tsList[j]
		})

		tsUnique := common.RemoveDuplicates(tsList)

		timeScore := common.Map(tsUnique, func(index int, item int) float64 {
			return 1 - math.Tanh(0.2*float64(index))
		})

		timeScoreDict := make(map[int]float64, dataCount)

		for i := 0; i < len(tsUnique); i++ {
			timeScoreDict[tsUnique[i]] = timeScore[i]
		}

		for _, d := range datas {
			_score := common.Interface2F64((*d)["_rerank_score"])
			score, scoreDetail := r.rerankProcess(req, d, "", query, category, currentTime, _score)
			scoreDetail["score_without_ts"] = score

			postTs := common.Interface2I64((*d)["post_ts"])

			tsScore := timeScoreDict[-int(postTs)] * 1.5 / 5 * float64(timeStrength)
			scoreDetail["ts"] = tsScore
			scoreDetail["add_score"] = addScore
			scoreDetail["time_score_dict"] = timeScoreDict

			score += (tsScore + float64(addScore))

			scoreDetail["rerank_score"] = score
			id2ScoreDetail[common.Interface2I64((*d)["id"])] = &scoreDetail

			(*d)["_rerank_score"] = score
			rerankScore = append(rerankScore, d)

		}
		addScore -= 1
	}

	sort.Slice(rerankScore, func(i, j int) bool {
		score1 := (*rerankScore[i])["_rerank_score"].(float64)
		score2 := (*rerankScore[j])["_rerank_score"].(float64)
		if score1 == score2 {
			rank_index1 := common.Interface2I64((*rerankScore[i])["_rank_index"])
			rank_index2 := common.Interface2I64((*rerankScore[j])["_rank_index"])
			return rank_index1 < rank_index2
		}
		return score1 > score2
	})

	index := 1
	for _, r := range rerankScore {
		(*r)["_rerank_index"] = index
		index += 1
	}

	if req.Payload.TopK >= 0 && req.Payload.TopK < len(rerankScore) {
		// 如果limit-1, limit, limit+1重排分数相同，均保留
		limit := req.Payload.TopK

		return rerankScore[:limit], nil
	} else {

		return rerankScore, nil
	}
}
