package rank

import (
	"strings"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/consts"
)

// 站点或等级得分

func CalcScore(authorScore float64, level int) float64 {
	var levelScore float64
	if level != 1 && level != 2 && level != 3 && level != 4 {
		levelScore = consts.QualityScore[level]
	} else {
		authorLevel := getAuthorLevel(authorScore)
		levelScore = articleScore(level, authorLevel) / 4
	}

	return levelScore
}

func getAuthorLevel(score float64) int {
	// 作者level映射
	level := 1
	if score <= 1 && score >= 0.9 {
		level = 4
	} else if score >= 0.7 && score < 0.9 {
		level = 3
	} else if score >= 0.4 && score < 0.7 {
		level = 2
	}

	return level
}

func articleScore(docLevel int, authorLevel int) float64 {
	var docScore, levelScore float64

	score, ok := consts.LevelScoreDict[docLevel]
	if ok {
		docScore = score
	} else {
		docScore = 0
	}

	switch authorLevel {
	case 4:
		levelScore = docScore + 2
	case 3:
		levelScore = docScore + 1.5
	case 2:
		levelScore = docScore + 0.8
	default:
		levelScore = docScore
	}

	return levelScore

}

func CalMedicalScore(authorScore float64, level int) float64 {
	if level == 1 && authorScore > 0 {
		authorLevel := getAuthorLevel(authorScore)
		levelScore := articleScore(level, authorLevel) / 4
		return levelScore * 1
	}
	return consts.QualityScore[level] * 0.9 * 1
}

// 站点策略得分
func UrlsScore(req *bean.Request, url, host string, keywordsScore float64, index int, tag bool, domains []string, indexThreshold int) float64 {
	var score float64
	isMedical := false
	urlTag := false

	// 精品站点判断
	if len(req.Payload.RankSites) != 0 {
		for _, site := range req.Payload.RankSites {
			if strings.Contains(url, site) {
				urlTag = true
				break
			}
		}
	}

	if urlTag && indexThreshold > 0 {
		isMedical = true
		if index <= indexThreshold {
			if tag {
				if keywordsScore >= 0.8 && index <= 10 {
					score = keywordsScore * 0.3
				} else if keywordsScore > 0.2 {
					score = keywordsScore * 0.2
				} else {
					score = 0.010
				}
			} else {
				if keywordsScore > 0.2 {
					score = 0.008
				} else {
					score = 0.004
				}
			}
		} else {
			score = 0.004
		}
	} else {
		parts := strings.Split(url, "//")
		if len(parts) > 1 {
			domain := strings.Split(parts[1], "/")[0]
			for _, medicalDomain := range domains {
				if strings.Contains(domain, medicalDomain) {
					isMedical = true
					break
				}
			}
		}
	}

	if !isMedical && indexThreshold < 0 {
		score = -1
	}

	// 讯飞医典
	if host == "mkb.iflyhealth.com" && indexThreshold > 0 {
		if index <= indexThreshold {
			if tag {
				if keywordsScore >= 0.8 && index <= 10 {
					score = 1.0
				} else if keywordsScore > 0.2 {
					score = keywordsScore * 0.3
				} else {
					score = 0.020
				}
			} else {
				if keywordsScore > 0.2 {
					score = 0.010
				} else {
					score = 0.008
				}
			}
		} else {
			score = 0.008
		}
	}

	return score
}

// levelsScore 站点 level 权重得分
func levelsScore(level int) float64 {
	if weight, ok := consts.QualityScore[level]; ok {
		return weight * 0.02
	}
	return 0
}

func levelsScore0620(level int) float64 {

	return float64(level) * 0.004
}
