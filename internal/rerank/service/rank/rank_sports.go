package rank

import (
	"fmt"
	"math"
	"sort"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/consts"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	"github.com/dexyk/stringosim"
	"go.mongodb.org/mongo-driver/bson/primitive"
)

type SportsRerank struct {
	Ctx *pandora_context.PandoraContext
}

var fineLabelScore = map[int]float64{
	4: 14.0,
	3: 7.0,
	2: 0.0,
	1: -7.0,
}

func timeSplit(ts int64) int {
	currentTime := time.Now().UnixNano() / int64(time.Millisecond)
	// 一月内为A
	if currentTime-ts <= int64(31*24*60*60*1000) {
		return 3
	} else if currentTime-ts > int64(31*24*60*60*1000) && currentTime-ts <= int64(366*24*60*60*1000) {
		return 2
	}
	return 1
}

func qualityLevel(qualityLevel int) int {
	var levelScore float64 = float64(qualityLevel) / 4.0
	if levelScore >= 0.75 {
		return 3
	} else if levelScore >= 0.3 && levelScore < 0.75 {
		return 2
	} else {
		return 1
	}
}

// 资讯重排
func newsRerank(data []*map[string]any) ([]*map[string]any, map[int64]*map[string]any) {
	// scoreResults := make([]*map[string]any, 0)
	id2ScoreDetail := make(map[int64]*map[string]any, 0)

	// 将精排结果按照打分进行分段
	filterResults := make([]*map[string]any, 0)
	tsList := make([]int, 0)
	for _, d := range data {
		if common.Interface2F64((*d)["_rank_score"]) >= 0.9 {
			filterResults = append(filterResults, d)
		}
		tsList = append(tsList, -int(common.Interface2I64((*d)["post_ts"])))
	}

	// 统计时间重复率
	sort.Slice(tsList, func(i, j int) bool {
		return tsList[i] < tsList[j]
	})

	tsUnique := common.RemoveDuplicates(tsList)

	timeScore := common.Map(tsUnique, func(index int, item int) float64 {
		return 1 - math.Tanh(0.2*float64(index))
	})

	timeScoreDict := make(map[int]float64, 0)

	for i := 0; i < len(tsUnique); i++ {
		timeScoreDict[tsUnique[i]] = timeScore[i]
	}

	// 将时间打分作为最终的重排打分
	for _, item := range filterResults {
		(*item)["_rerank_score"] = timeScoreDict[-int(common.Interface2I64((*item)["post_ts"]))]
		// score := timeScoreDict[-int(common.Interface2I64((*item)["post_ts"]))]
	}

	sort.Slice(filterResults, func(i, j int) bool {
		rerankScore1 := common.Interface2F64((*filterResults[i])["_rerank_score"])
		rerankScore2 := common.Interface2F64((*filterResults[i])["_rerank_score"])
		return rerankScore1 > rerankScore2
	})

	return filterResults, id2ScoreDetail

}

func answerStage(data []*map[string]any, step float64, rankScoreThreshold float64) [][]*map[string]any {
	// 统计精排打分列表
	// scoreMap := map[int64]float64
	scoreList := make([]float64, 0)
	for _, d := range data {
		scoreList = append(scoreList, common.Interface2F64((*d)["_rank_score"]))
	}
	// 得分分桶
	scoreHist := arange(rankScoreThreshold, 1.05, step)
	shLen := len(scoreHist)

	recordsBox := make([][]*map[string]any, 0)
	if step == 0 {
		var recordBox []*map[string]any
		if step == 0 || step == 1 {
			for i := 0; i < len(data); i++ {
				score := common.Interface2F64((*data[i])["_rank_score"])
				if score >= rankScoreThreshold {
					(*data[i])["_rerank_score"] = rankScoreThreshold
					recordBox = append(recordBox, data[i])
				}
			}
		}
		// if len(recordBox) > 0 {
		// 	recordsBox = append(recordsBox, recordBox)
		// }
		recordsBox = append(recordsBox, recordBox)
		return recordsBox
	}
	if len(scoreHist) > 0 {
		scoreHist[0] = scoreHist[0] - 0.01
	}

	for idx := range scoreHist {
		var ec float64
		sc := scoreHist[idx]
		if idx >= shLen-1 {
			ec = 1 + step
		} else {
			ec = scoreHist[idx+1]
		}

		var recordBox []*map[string]any
		for idx1 := 0; idx1 < len(data); idx1++ {
			score := common.Interface2F64((*data[idx1])["_rank_score"])
			if score > sc && score <= ec {
				// 分桶后排序前的桶区间得分暂存在_rerank_score中

				(*data[idx1])["_rerank_score"] = sc
				recordBox = append(recordBox, data[idx1])

			}
		}

		// if len(recordBox) > 0 {
		// 	recordsBox = append(recordsBox, recordBox)
		// }
		recordsBox = append(recordsBox, recordBox)
	}

	return recordsBox
}

func scoreRerank(id int64, query string, summaryLen int, score, lcs, queryScore, summaryScore, keywordProp, keywordsLen,
	jaccardDist, levenshteinDist, suppressWeight float64) (float64, map[string]any) {
	summaryScore = math.Tanh(float64(summaryLen)/10000) * 1.5
	lcsScore := float64(lcs) * 0.01 * suppressWeight
	jaccardScore := jaccardDist * 0.01 * suppressWeight
	keywordPropScore := keywordProp * 0.01
	keywordsLenScore := keywordsLen * 0.01
	// keywordsSummaryScore := summaryKeywords * 0.01
	// authorityScore := math.Tanh(float64(authority)) * 0.01
	levenshteinDistScore := common.Min(levenshteinDist, float64(utf8.RuneCountInString(query))) * -0.003 / suppressWeight
	// domainFreqScore := math.Tanh(0.00000001*float64(domainFreq)) * 0.1

	var rankScore float64 = 0
	var rerankScore float64 = 0
	scoreDetail := make(map[string]any, 0)
	scoreDetail["summary_score"] = summaryScore // 0.014538602311465032
	rankScore += summaryScore
	scoreDetail["lcs_score"] = lcsScore // 0.01
	rankScore += lcsScore

	scoreDetail["jaccard_score"] = jaccardScore // 0.002
	rankScore += jaccardScore

	scoreDetail["keyword_prop_score"] = keywordPropScore //0.005
	rankScore += keywordPropScore

	scoreDetail["keywords_length_score"] = keywordsLenScore //0.005
	rankScore += keywordsLenScore

	// scoreDetail["keywords_in_summary_score"] = keywordsSummaryScore //0.01
	// rankScore += keywordsSummaryScore

	// scoreDetail["authority_score"] = authorityScore //0
	// rankScore += authorityScore

	scoreDetail["levenshtein_dist_score"] = levenshteinDistScore //-0.009000000000000001
	rankScore += levenshteinDistScore

	// scoreDetail["domain_freq_score"] = domainFreqScore //0
	// rankScore += domainFreqScore

	rerankScore = rankScore*0.3 + score*18
	// fmt.Printf("id:%v, 分数1:%v, 分数2:%v, 分数3:%v\n", id, rankScore, score, rerankScore)

	scoreDetail["levenshtein_dist"] = levenshteinDist
	scoreDetail["summary_length"] = summaryLen
	scoreDetail["lcs"] = lcs
	scoreDetail["jaccard_dist"] = jaccardDist
	scoreDetail["keyword_prop"] = keywordProp
	scoreDetail["keyword_length"] = keywordsLen

	return rerankScore, scoreDetail
}

// 对重排特征进行解析
func rerankProcess(data *map[string]any, query string, category string) (rerankScore float64, scoreDetail map[string]any) {
	var Summary = common.Interface2S((*data)["summary"])
	var Content = common.Interface2S((*data)["content"])
	if Summary == "" {
		Summary = common.TruncateString(Content, 512)
	}
	Summary = strings.ToLower(Summary)

	title := common.Interface2S((*data)["title"])
	title = strings.ToLower(title)

	if strings.Contains(query, "令人瞠目结舌") {
		fmt.Println("attention please")
	}
	summaryLen := utf8.RuneCountInString(Summary)

	queryKeywords := common.SetDifference(config.Seg.CutStop(query), config.StopWords)
	titleKeywords := common.SetDifference(config.Seg.CutStop(title), config.StopWords)

	// strongTimeliness := 1
	var wg sync.WaitGroup
	var levenshteinDist int
	var lcs, jaccardDist float64

	wg.Add(3)
	go func() {
		defer wg.Done()

		lcs = float64(stringosim.LCS([]rune(query), []rune(title))) / float64(utf8.RuneCountInString(query))
		// data.Lcs = lcs
	}()

	go func() {
		defer wg.Done()

		//jaccardDist = stringosim.Jaccard([]rune(query), []rune(title), []int{1})
		jaccardDist = common.JaccardDistance2(query, title)
		// slog.DebugF("id:%v,query:%s,title:%s,jaccard:%v\n", common.Interface2I64((*data)["id"]), query, title, jaccardDist)
	}()

	go func() {
		defer wg.Done()

		levenshteinDist = stringosim.Levenshtein([]rune(query), []rune(title))
	}()

	wg.Wait()

	queryScore, summaryScore, keywordProp, keywordsLen := common.KeywordFeat2(queryKeywords, titleKeywords, title)

	suppressWeight := 1.0
	if float64(len(title)) < float64(len(strings.Join(queryKeywords, "")))*0.4 {
		suppressWeight = 0.6
	}
	rankScore := common.Interface2F64((*data)["_rerank_score"])
	rerankScore, scoreDetail = scoreRerank(common.Interface2I64((*data)["id"]), query, summaryLen, rankScore, lcs, float64(queryScore), float64(summaryScore), float64(keywordProp), float64(keywordsLen), jaccardDist, float64(levenshteinDist), suppressWeight)

	// 去除视频类新闻
	minusVideo := true

	for key := range consts.VideoKeywords {
		if strings.Contains(query, key) {
			minusVideo = false
			break
		}
	}

	if minusVideo {
		for key := range consts.VideoKeywords {
			if strings.Contains(Summary, key) {
				rerankScore -= 2
			}
		}
	}
	scoreDetail["minusVideo"] = minusVideo

	// 添加质量特征,有q_user用q_user,无q_user用q_level
	// qUser := common.Interface2I64((*data)["q_user"])
	// qLevel := common.Interface2I64((*data)["q_level"])

	// 获取质量等级
	// var docLevel int = 1
	// var levelScore float64 = 0.0
	// var qualityScore = map[int]float64{4: 1, 3: 0.7, 2: 0.3, 1: 0}
	// if qUser == 0 {
	// 	if qLevel != 0 {
	// 		docLevel = int(qLevel)
	// 	}
	// } else {
	// 	docLevel = int(qUser)
	// }

	return rerankScore, scoreDetail
}

// 非主要特征打分
func secondaryScore(query string, data *map[string]any, category string) (rerankScore float64, scoreDetail map[string]any) {
	var Summary = common.Interface2S((*data)["summary"])
	var Content = common.Interface2S((*data)["content"])
	if Summary == "" {
		Summary = common.TruncateString(Content, 512)
	}
	// fmt.Printf("query:%v\n", query)

	title := common.Interface2S((*data)["title"])
	title = strings.ToLower(title)

	summaryLen := utf8.RuneCountInString(Summary)

	// 分桶后排序前的桶区间得分暂存在_rerank_score中
	rankScore := common.Interface2F64((*data)["_rerank_score"])

	queryKeywords := common.SetDifference(config.Seg.CutStop(query), config.StopWords)
	titleKeywords := common.SetDifference(config.Seg.CutStop(title), config.StopWords)

	var wg sync.WaitGroup
	var levenshteinDist int
	var lcs, jaccardDist float64

	wg.Add(3)
	go func() {
		defer wg.Done()

		lcs = float64(stringosim.LCS([]rune(query), []rune(title))) / float64(utf8.RuneCountInString(query))
		// data.Lcs = lcs
	}()

	go func() {
		defer wg.Done()

		//jaccardDist = stringosim.Jaccard([]rune(query), []rune(title), []int{1})
		jaccardDist = common.JaccardDistance2(query, title)
		// slog.DebugF("id:%v,query:%s,title:%s,jaccard:%v\n", common.Interface2I64((*data)["id"]), query, title, jaccardDist)
	}()

	go func() {
		defer wg.Done()

		levenshteinDist = stringosim.Levenshtein([]rune(query), []rune(title))
	}()

	wg.Wait()

	// domainFreq := 0

	queryScore, summaryScore, keywordProp, keywordsLen := common.KeywordFeat2(queryKeywords, titleKeywords, title)

	suppressWeight := 1.0
	if float64(len(title)) < float64(len(strings.Join(queryKeywords, "")))*0.4 {
		suppressWeight = 0.6
	}

	rerankScore, scoreDetail = scoreRerank(common.Interface2I64((*data)["id"]), query, summaryLen, rankScore, lcs, float64(queryScore), float64(summaryScore), float64(keywordProp), float64(keywordsLen), jaccardDist, float64(levenshteinDist), suppressWeight)

	// 去除视频类新闻
	minusVideo := true

	for key := range consts.VideoKeywords {
		if strings.Contains(query, key) {
			minusVideo = false
			break
		}
	}

	if minusVideo {
		for key := range consts.VideoKeywords {
			if strings.Contains(Summary, key) {
				rerankScore -= 2
			}
		}
	}
	scoreDetail["minusVideo"] = minusVideo

	return rerankScore, scoreDetail
}

func mediumScore(data []*map[string]any, resultsHist [][]*map[string]any, query string) ([]*map[string]any, map[int64]*map[string]any) {
	// scoreResults := make([]*map[string]any, 0)
	id2ScoreDetail := make(map[int64]*map[string]any, 0)

	var category string

	if query == "电子竞技世界杯 沙特 举办日期" {
		fmt.Println("attention please!")
	}
	// 最终排序结果
	rerankScore := make([]*map[string]any, 0)
	// 以防分档之后的打分排序出现越级的情况，增加等级加分
	addScore := 42

	for i := len(resultsHist) - 1; i >= 0; i-- {
		results := resultsHist[i]
		rerankList := make([]*map[string]any, 0)
		for i := 0; i < len(results); i++ {
			result := results[i]
			rerankScore, scoreDetail := secondaryScore(query, result, category)

			// 时间与质量结合打分
			ts := common.Interface2I64((*result)["post_ts"])
			scoreDetail["ts"] = ts
			timeLabel := timeSplit(ts)
			scoreDetail["timeLabel"] = timeLabel
			var docLevel int = 1

			// docLevel = int(common.Interface2I64((*data)["q_level"]))

			// 质量等级
			docLevel = int(common.Interface2I64((*result)["q_user"]))
			if docLevel == 0 {
				docLevel = int(common.Interface2I64((*result)["q_level"]))
			}
			if docLevel != 3 && docLevel != 2 && docLevel != 1 {
				docLevel = 1
			}

			levelLabel := qualityLevel(docLevel)
			tsQualLevel := map[int]float64{3: 4.0, 2: 3.0, 1: 1.0}
			timeLabelScore := 0.0
			levelLabelScore := 0.0
			if value, ok := tsQualLevel[timeLabel]; ok {
				timeLabelScore = value
			}
			if value, ok := tsQualLevel[levelLabel]; ok {
				levelLabelScore = value
			}
			scoreDetail["levelLabel"] = levelLabel
			timeQualityScore := timeLabelScore + levelLabelScore
			scoreDetail["timeQualityScore"] = timeQualityScore
			rerankScore += float64(timeQualityScore)

			// 精品质量等级
			fineLevel := 1
			// docLevel = int(common.Interface2I64((*data)["q_level"]))
			if t, ok := (*result)["levels"].(map[string]any); ok {
				// fmt.Println(t)
				for key, value := range t {
					if common.Interface2S(key) == config.G_ParamModule.DefaultInstance().SportsLevel {
						fineLevel = int(common.Interface2I64(value))
					}

				}
			} else {
				fineLevel = 1
			}

			if value, ok := fineLabelScore[fineLevel]; ok {
				rerankScore += value
				scoreDetail["fineLevelScore"] = value
			} else {
				rerankScore += -7.0
				scoreDetail["fineLevelScore"] = -7.0
			}
			scoreDetail["fineLevel"] = fineLevel

			rerankScore += float64(addScore)
			(*result)["_rerank_score"] = rerankScore
			rerankList = append(rerankList, result)
			scoreDetail["rerankScore"] = rerankScore
			id2ScoreDetail[common.Interface2I64((*result)["id"])] = &scoreDetail
		}
		addScore -= 22
		rerankScore = append(rerankScore, rerankList...)
	}

	return rerankScore, id2ScoreDetail
}

func weakScore(data []*map[string]any, resultsHist [][]*map[string]any, query string) ([]*map[string]any, map[int64]*map[string]any) {

	// 最终排序结果
	// rerank := make([]*map[string]any, 0)

	rerankTotal, id2Scoid2ScoreDetail := commonScore(data, resultsHist, query, 1)

	for _, item := range rerankTotal {
		var fineLevel int = 1
		rerankScore := common.Interface2F64((*item)["_rerank_score"])
		id := common.Interface2I64((*item)["id"])
		scoreDetail := id2Scoid2ScoreDetail[id]

		// docLevel = int(common.Interface2I64((*data)["q_level"]))
		if t, ok := (*item)["levels"].(map[string]any); ok {
			// fmt.Println(t)
			for key, value := range t {
				if common.Interface2S(key) == config.G_ParamModule.DefaultInstance().SportsLevel {
					fineLevel = int(common.Interface2I64(value))
				}

			}
		} else {
			fineLevel = 1
		}

		if value, ok := fineLabelScore[fineLevel]; ok {
			rerankScore += value
			(*scoreDetail)["fileLevelScore"] = value
		} else {
			rerankScore += -7.0
			(*scoreDetail)["fileLevelScore"] = -7.0
		}

		(*scoreDetail)["rerankScore"] = rerankScore
		(*item)["_rerank_score"] = rerankScore

	}
	return rerankTotal, id2Scoid2ScoreDetail

}

func commonScore(data []*map[string]any, resultsHist [][]*map[string]any, query string, timeStrength int) ([]*map[string]any, map[int64]*map[string]any) {
	// resultHist为精排分档后的二维数组
	id2ScoreDetail := make(map[int64]*map[string]any, 0)
	var category string

	// 最终排序结果
	rerankScore := make([]*map[string]any, 0)
	// 以防分档之后的打分排序出现越级的情况，增加等级加分
	addScore := 42
	for i := len(resultsHist) - 1; i >= 0; i-- {
		results := resultsHist[i]
		rerankList := make([]*map[string]any, 0)
		var tsList []int
		dataCount := len(results)
		// 将时间倒排给权重
		for _, v := range results {
			postTs := common.Interface2I64((*v)["post_ts"])
			tsList = append(tsList, -int(postTs))
		}

		// 统计时间重复率
		sort.Slice(tsList, func(i, j int) bool {
			return tsList[i] < tsList[j]
		})

		tsUnique := common.RemoveDuplicates(tsList)

		timeScore := common.Map(tsUnique, func(index int, item int) float64 {
			return 1 - math.Tanh(0.2*float64(index))
		})

		timeScoreDict := make(map[int]float64, dataCount)

		for i := 0; i < len(tsUnique); i++ {
			timeScoreDict[tsUnique[i]] = timeScore[i]
		}

		for i := 0; i < len(results); i++ {
			result := results[i]

			rerankScore, scoreDetail := rerankProcess(result, query, category)

			// 时间与质量结合打分
			ts := common.Interface2I64((*result)["post_ts"])
			// timeLabel := timeSplit(ts)
			scoreDetail["ts"] = ts
			rerankScore += timeScoreDict[-int(ts)] * 1.0 * float64(timeStrength)
			var docLevel int = 1

			// docLevel = int(common.Interface2I64((*data)["q_level"]))
			if t, ok := (*result)["levels"].(primitive.M); ok {
				// fmt.Println(t)
				for key, value := range t {
					if common.Interface2S(key) == config.G_ParamModule.DefaultConfig().SportsLevel {
						docLevel = int(common.Interface2I64(value))
					}

				}
			} else {
				docLevel = 1
			}
			levelLabel := qualityLevel(docLevel)
			// timeQualityScore := levelLabel
			// rerankScore += float64(timeQualityScore)
			scoreDetail["levelLabelScore"] = levelLabel
			// scoreDetail["timeQualityScore"] = timeLabel
			scoreDetail["timeScoreDict"] = timeScoreDict

			// fineLevel := docLevel
			// if value, ok := fineLabelScore[fineLevel]; ok {
			// 	rerankScore += value
			// } else {
			// 	rerankScore += -7.0
			// }
			rerankScore += float64(addScore)
			scoreDetail["addScore"] = addScore
			(*result)["_rerank_score"] = rerankScore
			scoreDetail["_rerank_score_without_fineLevel"] = rerankScore
			rerankList = append(rerankList, result)
			id2ScoreDetail[common.Interface2I64((*result)["id"])] = &scoreDetail

		}
		addScore -= 22

		rerankScore = append(rerankScore, rerankList...)
	}

	return rerankScore, id2ScoreDetail

}

// 体育重排
func sportsRerank(req *bean.Request, timeliness string, query string, data []*map[string]any) ([]*map[string]any, map[int64]*map[string]any) {
	scoreResults := make([]*map[string]any, 0)
	id2ScoreDetail := make(map[int64]*map[string]any, 0)
	tmpData := answerStage(data, req.Payload.Step, req.Payload.ScoreThreshold)
	if timeliness == consts.STRONG_TIMELINESS {
		// 强时效和热搜新闻一个路子
		scoreResults, id2ScoreDetail = newsRerank(data)
	} else if timeliness == consts.MEDIUM_TIMELINESS {
		// 中时效
		scoreResults, id2ScoreDetail = mediumScore(data, tmpData, query)
	} else {
		// 弱时效
		scoreResults, id2ScoreDetail = weakScore(data, tmpData, query)
	}
	return scoreResults, id2ScoreDetail
}
func (r *SportsRerank) Rank(span *span.Span, req *bean.Request, data *bean.QueryData) ([]*map[string]any, error) {
	rankSpan := span.AddSpan("重排排序")
	defer rankSpan.Finish()
	// 重排打分结果
	var rerankScore = make([]*map[string]any, 0)
	// 各项特征得分详情
	id2ScoreDetail := make(map[int64]*map[string]any, 0)
	docs := data.Docs
	defer rankSpan.TraceInfo("scoreDetail", id2ScoreDetail)

	if req.Payload.Intent == consts.HOTTOPIC {
		rerankScore, id2ScoreDetail = newsRerank(docs)
	} else if (req.Payload.Intent == consts.NEWS && common.Contains(consts.TimelinessLabel, req.Payload.TimeLiness)) ||
		!common.Contains(consts.IntentLabel, req.Payload.Intent) {
		rerankScore, id2ScoreDetail = sportsRerank(req, req.Payload.TimeLiness, data.Query, docs)
	}

	sort.Slice(rerankScore, func(i, j int) bool {
		score1 := (*rerankScore[i])["_rerank_score"].(float64)
		score2 := (*rerankScore[j])["_rerank_score"].(float64)
		if score1 == score2 {
			rank_index1 := common.Interface2I64((*rerankScore[i])["_rank_index"])
			rank_index2 := common.Interface2I64((*rerankScore[j])["_rank_index"])
			return rank_index1 < rank_index2
		}
		return score1 > score2
	})

	index := 1
	for _, r := range rerankScore {
		(*r)["_rerank_index"] = index
		index += 1
	}

	if req.Payload.TopK >= 0 && req.Payload.TopK < len(rerankScore) {
		// 如果limit-1, limit, limit+1重排分数相同，均保留
		limit := req.Payload.TopK
		return rerankScore[:limit], nil
	} else {
		return rerankScore, nil
	}
}
