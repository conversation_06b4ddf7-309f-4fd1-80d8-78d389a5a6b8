package rank

import (
	"fmt"
	"math"
	"sort"
	"strconv"
	"time"
	"unicode"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/service/utils"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	concurrent_map "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/concurrent/map"
)

// splitArray 将浮点数数组按相邻元素最大差值不断分割, 直到满足以下条件：
// 1. 每个子数组的首尾差值 < 该子数组与前一个子数组的差值
// 2. 每个子数组的首尾差值 < 该子数组与后一个子数组的差值
// 3. 每个子数组的首尾差值 < 0.015
func splitArray(arr []float64) [][]float64 {

	// 如果数组元素全部一致, 无需分割
	if len(arr) > 0 {
		first := arr[0]
		allSame := true
		for _, v := range arr[1:] {
			if v != first {
				allSame = false
				break
			}
		}
		if allSame {
			return [][]float64{arr}
		}
	}

	// 如果数组长度小于等于1，无需分割
	if len(arr) <= 1 {
		return [][]float64{arr}
	}

	// 存储所有分割点的位置，初始只有数组末尾位置
	splitPoints := []int{len(arr)}
	// 待处理的区间队列，初始为整个数组
	splitQueue := []struct{ start, end int }{{0, len(arr)}}

	// 处理所有待分割的区间
	for len(splitQueue) > 0 {
		// 取出队列头部的区间进行处理
		current := splitQueue[0]
		splitQueue = splitQueue[1:]
		start, end := current.start, current.end

		// 如果区间长度小于等于1，无需分割
		if end-start <= 1 {
			continue
		}

		// 找到区间内相邻元素差值最大的位置
		maxDiff := -math.MaxFloat64
		splitIdx := -1
		for i := start; i < end-1; i++ {
			diff := math.Abs(arr[i+1] - arr[i])
			if diff > maxDiff {
				maxDiff = diff
				splitIdx = i + 1
			}
		}

		// 将新的分割点加入列表并排序
		splitPoints = append(splitPoints, splitIdx)
		splitPoints = sortSplitPoints(splitPoints)

		// 检查是否需要继续分割
		// 获取当前分割点在splitPoints中的索引
		splitPointIndex := findIndex(splitPoints, splitIdx)
		// 获取前一个分割点位置（默认为0）
		prevSplit := 0
		if splitPointIndex > 0 {
			prevSplit = splitPoints[splitPointIndex-1]
		}
		// 获取下一个分割点位置（默认为数组长度）
		nextSplit := len(arr)
		if splitPointIndex < len(splitPoints)-1 {
			nextSplit = splitPoints[splitPointIndex+1]
		}

		// 检查分割后的两个新区间是否满足条件
		// 区间1: [prevSplit, splitIdx]
		if splitIdx-prevSplit > 1 {
			// 计算当前区间首尾差值
			rangeDiff := math.Abs(arr[splitIdx-1] - arr[prevSplit])
			// 计算与前一区间的差值（如果有）
			leftDiff := math.MaxFloat64
			if splitPointIndex > 1 {
				leftDiff = math.Abs(arr[prevSplit] - arr[splitPoints[splitPointIndex-2]-1])
			}
			// 计算与后一区间的差值
			rightDiff := math.Abs(arr[splitIdx-1] - arr[splitIdx])

			// 如果不满足条件，加入队列继续分割
			if rangeDiff >= leftDiff || rangeDiff >= rightDiff || rangeDiff >= 0.015 {
				splitQueue = append(splitQueue, struct{ start, end int }{prevSplit, splitIdx})
			}
		}

		// 区间2: [splitIdx, nextSplit]
		if nextSplit-splitIdx > 1 {
			// 计算当前区间首尾差值
			rangeDiff := math.Abs(arr[nextSplit-1] - arr[splitIdx])
			// 计算与前一区间的差值
			leftDiff := math.Abs(arr[splitIdx] - arr[splitIdx-1])
			// 计算与后一区间的差值（如果有）
			rightDiff := math.MaxFloat64
			if splitPointIndex < len(splitPoints)-2 {
				rightDiff = math.Abs(arr[nextSplit-1] - arr[splitPoints[splitPointIndex+1]])
			}

			// 如果不满足条件，加入队列继续分割
			if rangeDiff >= leftDiff || rangeDiff >= rightDiff || rangeDiff >= 0.015 {
				splitQueue = append(splitQueue, struct{ start, end int }{splitIdx, nextSplit})
			}
		}
	}

	// 生成最终的分割结果
	result := make([][]float64, 0, len(splitPoints))
	// result, _ := arena_api.MakeSliceWithDefault[[]float64](global.IFLYS_ARENA, 0, len(splitPoints))
	prev := 0
	for _, split := range splitPoints {
		if split > prev {
			result = append(result, arr[prev:split])
			prev = split
		}
	}

	return result
}

// 辅助函数1: 对分割点进行排序并去重
func sortSplitPoints(points []int) []int {
	// 简单的冒泡排序实现
	for i := 0; i < len(points)-1; i++ {
		for j := i + 1; j < len(points); j++ {
			if points[i] > points[j] {
				points[i], points[j] = points[j], points[i]
			}
		}
	}

	// 去重处理
	result := []int{points[0]}
	for i := 1; i < len(points); i++ {
		if points[i] != points[i-1] {
			result = append(result, points[i])
		}
	}

	return result
}

// 辅助函数2: 查找元素在数组中的索引
func findIndex(arr []int, val int) int {
	for i, v := range arr {
		if v == val {
			return i
		}
	}
	return -1
}

// 从Title中提取年份信息, 医疗权威知识库多数数据无时间戳信息, 而部分医疗指南数据标题中携带年份信息, 作为补充
// 提取规则：从字符串中提取所有连续的数字, 仅保留大小在2000到今年年份之间的数字，并返回最大值的Unix时间戳（秒）
func extractYear(str string) int64 {
	currentYear := time.Now().Year()
	var result []string      // 存储符合条件的年份字符串
	var currentDigits []rune // 当前正在处理的连续数字序列
	inNumber := false        // 标记是否正在处理数字序列

	// 遍历字符串中的每个字符, 提取所有连续的数字
	for _, r := range str {
		if unicode.IsDigit(r) {
			if !inNumber {
				// 开始新的数字序列
				inNumber = true
				currentDigits = []rune{r}
			} else {
				// 继续当前数字序列
				currentDigits = append(currentDigits, r)
			}
		} else {
			if inNumber {
				// 数字序列结束, 检查数值范围
				if numStr := string(currentDigits); len(numStr) >= 4 {
					if num, err := strconv.Atoi(numStr); err == nil {
						if num >= 2000 && num <= (currentYear+1) { // 当前年份 + 1
							result = append(result, numStr)
						}
					}
				}
				inNumber = false
				currentDigits = nil
			}
			// 非数字字符, 继续处理下一个字符
		}
	}

	// 处理字符串末尾的数字序列(如果有)
	if inNumber {
		if numStr := string(currentDigits); len(numStr) >= 4 {
			if num, err := strconv.Atoi(numStr); err == nil {
				if num >= 2000 && num <= (currentYear+1) { // 当前年份 + 1
					result = append(result, numStr)
				}
			}
		}
	}

	// 查找最大值作为时间(多个年份时取最新的）
	if len(result) == 0 {
		return 0
	}
	max := result[0]
	for _, num := range result[1:] {
		if num > max {
			max = num
		}
	}

	// 将字符串转换为整数
	year, _ := strconv.Atoi(max)

	// 创建该年1月1日的时间对象, 时区设为UTC
	date := time.Date(year, time.January, 1, 0, 0, 0, 0, time.UTC)
	// 获取Unix时间戳（秒）
	timestamp := date.Unix()

	return timestamp
}

// 对精排分数按相同语义分数水平分组
func (r *MedicalRerank) groupResults(results []*map[string]any) [][]*map[string]any {
	// 所有 doc 的精排分数
	var rankScoreList []float64
	// rankScoreList, _ := arena_api.MakeSliceWithDefault[float64](global.IFLYS_ARENA, 0, len(results))
	for _, result := range results {
		rankScore := common.Interface2F64((*result)["_rank_score"])
		rankScoreList = append(rankScoreList, rankScore)
	}
	// 分割精排分数数组
	splitResult := splitArray(rankScoreList)

	// 创建与 splitResult 结构对应的二维结果列表
	var groupedResults [][]*map[string]any

	// 当前处理的结果索引
	currentIndex := 0

	// 遍历 splitResult 的每个分组
	for _, group := range splitResult {
		groupLength := len(group)
		var currentGroup []*map[string]any

		// 从原始结果中提取对应数量的元素
		for i := 0; i < groupLength; i++ {
			if currentIndex < len(results) {
				currentGroup = append(currentGroup, results[currentIndex])
				currentIndex++
			} else {
				break
			}
		}

		groupedResults = append(groupedResults, currentGroup)
	}

	return groupedResults
}

// 以下函数应产品要求单独作为可配置项, 例如库名可能会更改/增加
func (r *MedicalRerank) SortResults(results []*map[string]any, IndexCoedPriority *concurrent_map.ConcurrentMap[string, int]) {
	sort.Slice(results, func(i, j int) bool {
		data1, data2 := results[i], results[j]

		// 提取库名并获取优先级
		getPriority := func(lib string) int {

			if p, ok := IndexCoedPriority.Get(lib); ok {
				return p
			} else {
				return 0
			}
		}

		// 比较 library 优先级
		indexCode1 := common.Interface2S((*data1)["_indexCode"])
		indexCode2 := common.Interface2S((*data2)["_indexCode"])
		postTs1 := common.Interface2I64((*data1)["post_ts"])
		postTs2 := common.Interface2I64((*data2)["post_ts"])
		if priority1, priority2 := getPriority(indexCode1), getPriority(indexCode2); priority1 != priority2 {
			return priority1 > priority2
		}

		// library 相同时, post_ts 降序排列
		if postTs1 != postTs2 {
			return postTs1 > postTs2
		}

		// post_ts 相同时, rankScore 降序排列
		return common.Interface2F64((*data1)["_rank_score"]) > common.Interface2F64((*data2)["_rank_score"])
	})
}

// Content 中文及数字字符数较少的数据位置调整
func (r *MedicalRerank) SortResultsByChar(results []*map[string]any) {

	// 用于记录需要调整位置的元素及其新位置
	var adjustments []struct {
		index    int
		newIndex int
	}

	// 第一遍扫描: 找出所有需要调整的元素及其目标位置
	for i := 0; i < len(results); i++ {
		result := results[i]

		// 计算中文及数字符数
		charCount := 0
		for _, r := range common.Interface2S((*result)["content"]) {
			if unicode.Is(unicode.Han, r) {
				charCount++
			}
			if unicode.IsDigit(r) {
				charCount++
			}
		}
		for _, r := range common.Interface2S(((*result)["sub_title"])) {
			if unicode.Is(unicode.Han, r) {
				charCount-- // Content 内容由 sub_title 和 content 拼接而成, 因此实际统计 content 中文及数字字符数需去除 sub_title
			}
			if unicode.IsDigit(r) {
				charCount--
			}
		}
		// 如果中文及数字字符数少于60, 进行位置调整
		if charCount < 60 {
			// 查找rankScore相差在0.04范围内的最后一个元素位置
			j := i
			for j+1 < len(results) &&
				common.Interface2F64((*results[i])["_rank_score"])-common.Interface2F64((*results[j+1])["_rank_score"]) <= 0.04 {
				j++
			}

			var newPos int
			// 计算新位置
			if common.Interface2F64((*results[j])["_rank_score"]) >= 0.98 {
				newPos = i + 2 // 向后移动2位或到范围内末尾
			} else {
				newPos = i + 4 // 向后移动4位或到范围内末尾
			}
			if newPos > j {
				newPos = j
			}

			// 记录调整
			adjustments = append(adjustments, struct {
				index    int
				newIndex int
			}{i, newPos})
			// 如果中文及数字字符数少于100, 进行位置调整
		} else if charCount < 100 {
			// 查找rankScore相差在0.03范围内的最后一个元素位置
			j := i
			for j+1 < len(results) && common.Interface2F64((*results[i])["_rank_score"])-common.Interface2F64((*results[j+1])["_rank_score"]) <= 0.03 {
				j++
			}

			var newPos int
			// 计算新位置
			if common.Interface2F64((*results[i])["_rank_score"]) >= 0.98 {
				newPos = i + 2 // 向后移动2位或到范围内末尾
			} else {
				newPos = i + 4 // 向后移动4位或到范围内末尾
			}
			if newPos > j {
				newPos = j
			}

			// 记录调整
			adjustments = append(adjustments, struct {
				index    int
				newIndex int
			}{i, newPos})
		}
	}

	// 第二遍处理：按从前往后的顺序调整元素位置
	for i := 0; i < len(adjustments); i++ {
		adj := adjustments[i]
		if adj.index >= adj.newIndex {
			continue // 如果不需要移动或向后移动，跳过
		}

		// 检查后续调整是否受到当前移动的影响
		// 如果受到影响，更新后续调整的索引
		for j := i + 1; j < len(adjustments); j++ {
			// 如果后续调整的元素在当前移动的范围内
			if adjustments[j].index > adj.index && adjustments[j].index <= adj.newIndex {
				adjustments[j].index-- // 更新索引，因为前面的元素被移走了一个位置
			}
		}

		// 移动元素到新位置
		result := results[adj.index]
		// 将中间元素前移
		copy(results[adj.index:adj.newIndex], results[adj.index+1:adj.newIndex+1])
		results[adj.newIndex] = result
	}
}

func (r *MedicalRerank) Rank0516(span *span.Span, req *bean.Request, data *bean.QueryData) ([]*map[string]any, error) {
	rankSpan := span.AddSpan("重排排序")
	defer rankSpan.Finish()

	// rerankScore为最终结果
	var rerankScore = make([]*map[string]any, 0)
	// rerankScore, _ := arena_api.MakeSliceWithDefsault[*map[string]any](global.IFLYS_ARENA, 0, len(data.Docs))
	// rerankList为中间结果

	// 构建权重map
	weight := len(req.Payload.RankCollections)
	// IndexCoedPriority := make(map[string]int)
	IndexCoedPriority := concurrent_map.New[string, int]()
	for _, indexCode := range req.Payload.RankCollections {
		IndexCoedPriority.Set(indexCode, weight)
		weight--
	}

	id2ScoreDetail := make(map[int64]*map[string]any)
	rankSpan.TraceInfo("indexCodePriority", IndexCoedPriority)

	defer rankSpan.TraceInfo("scoreDetail", id2ScoreDetail)

	// 判断是否是强时间性query
	// 转换为小写并去除首尾空格
	// query := strings.ToUpper(data.Query)

	defer func() {
		if r := recover(); r != nil {
			fmt.Println("Recovered from panic:", r)
		}
	}()
	docs := data.Docs

	var tmpData [][]*map[string]any

	tmpData = r.groupResults(docs)

	for _, datas := range tmpData {
		// rerankList为中间结果
		var rerankList = make([]*map[string]any, 0)
		// rerankList, _ := arena_api.MakeSliceWithDefault[*map[string]any](global.IFLYS_ARENA, 0, len(datas))
		for _, data := range datas {
			// id := common.Interface2I64((*data)["id"])
			title := common.Interface2S((*data)["title"])
			// content := common.Interface2S((*data)["content"])
			// rankIndex := common.Interface2I64((*data)["_rank_index"])
			rankScore := common.Interface2F64((*data)["_rank_score"])
			// indexCode := common.Interface2S((*data)["_indexCode"])
			postTs := common.Interface2I64((*data)["post_ts"])

			if postTs != 0 {
				// 线上时间戳为精确到毫秒的13位, 去除3位毫秒级, 保留前10位进行正常的Unix时间戳转换
				tsStr := strconv.FormatInt(postTs, 10)
				if len(string(tsStr)) > 10 {
					tsStr = tsStr[:10]
					postTs, _ = strconv.ParseInt(tsStr, 10, 64)
				}
			} else {
				postTs = extractYear(title) // 时间戳为0, 则从标题抽取时间信息
			}

			(*data)["_rerank_score"] = rankScore
			rerankList = append(rerankList, data)

		}
		// 产品策略排序
		r.SortResults(rerankList, IndexCoedPriority)

		rerankScore = append(rerankScore, rerankList...)
	}
	// 最终排序(处理超短文本)
	r.SortResultsByChar(rerankScore)

	return utils.FinalResultsProcess(rankSpan, req, rerankScore, false)
}
