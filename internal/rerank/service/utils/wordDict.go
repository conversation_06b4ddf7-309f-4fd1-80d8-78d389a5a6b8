// package main
package utils

import (
	// "fmt"
	"strings"
)

// 定义列表存储屏蔽词
var BlockList = [][]string{
	{"支", "患者", "病人", "病患", "患有", "推荐"},
}

// 定义列表存储后缀屏蔽词
var SuffixList = [][]string{
	{"症", "期", "时期", "部", "部位", "类", "症", "征", "证"},
}

// 定义二维列表存储同义词：前缀
var SynonymsPrefix = [][]string{
	{"不稳定", "不稳定性", "不稳定型"}, {"中心", "中心性", "中心型"}, {"中毒", "中毒性", "中毒型"}, {"交感", "交感性", "交感型"}, {"伤寒", "伤寒性", "伤寒型"}, {"假肉瘤", "假肉瘤性", "假肉瘤型"}, {"偏执", "偏执性", "偏执型"},
	{"充血", "充血性", "充血型"}, {"先天", "先天性", "先天型"}, {"内开放", "内开放性", "内开放型"}, {"减少", "减少性", "减少型"}, {"凹陷", "凹陷性", "凹陷型"}, {"出血", "出血性", "出血型"}, {"创伤", "创伤性", "创伤型"},
	{"功能失调", "功能失调性", "功能失调型"}, {"功能", "功能性", "功能型"}, {"化学", "化学性", "化学型"}, {"化脓", "化脓性", "化脓型"}, {"单纯", "单纯性", "单纯型"}, {"占位", "占位性", "占位型"}, {"压缩", "压缩性", "压缩型"},
	{"压迫", "压迫性", "压迫型"}, {"原发", "原发性", "原发型"}, {"反流", "反流性", "反流型"}, {"变异", "变异性", "变异型"}, {"变性", "变性性", "变性型"}, {"周期", "周期性", "周期型"}, {"喘息", "喘息性", "喘息型"},
	{"地方", "地方性", "地方型"}, {"坏死", "坏死性", "坏死型"}, {"坠积", "坠积性", "坠积型"}, {"增生", "增生性", "增生型"}, {"复杂", "复杂性", "复杂型"}, {"外伤", "外伤性", "外伤型"}, {"多发", "多发性", "多发型"},
	{"多形", "多形性", "多形型"}, {"大叶", "大叶性", "大叶型"}, {"失血", "失血性", "失血型"}, {"局灶", "局灶性", "局灶型"}, {"巨幼细胞", "巨幼细胞性", "巨幼细胞型"}, {"开放", "开放性", "开放型"}, {"弥漫", "弥漫性", "弥漫型"},
	{"强直", "强直性", "强直型"}, {"心因", "心因性", "心因型"}, {"心源", "心源性", "心源型"}, {"急性", "急型"}, {"急进", "急进性", "急进型"}, {"恶性", "恶型"}, {"感染", "感染性", "感染型"}, {"慢性", "慢型"}, {"扩张", "扩张性", "扩张型"},
	{"损伤", "损伤性", "损伤型"}, {"栓塞", "栓塞性", "栓塞型"}, {"梗塞", "梗塞性", "梗塞型"}, {"梗阻", "梗阻性", "梗阻型"}, {"水肿", "水肿性", "水肿型"}, {"流型", "流型性", "流型型"}, {"浆液", "浆液性", "浆液型"},
	{"浸润", "浸润性", "浸润型"}, {"消化", "消化性", "消化型"}, {"溃疡", "溃疡性", "溃疡型"}, {"溶血", "溶血性", "溶血型"}, {"滤泡", "滤泡性", "滤泡型"}, {"特发", "特发性", "特发型"}, {"狂躁", "狂躁性", "狂躁型"},
	{"甲亢", "甲亢性", "甲亢型"}, {"病毒", "病毒性", "病毒型"}, {"病理", "病理性", "病理型"}, {"瘢痕", "瘢痕性", "瘢痕型"}, {"癫痫", "癫痫性", "癫痫型"}, {"短暂", "短暂性", "短暂型"}, {"神经", "神经性", "神经型"},
	{"积性", "积型"}, {"空洞", "空洞性", "空洞型"}, {"穿透", "穿透性", "穿透型"}, {"突发", "突发性", "突发型"}, {"类风湿", "类风湿性", "类风湿型"}, {"粉碎", "粉碎性", "粉碎型"}, {"粘连", "粘连性", "粘连型"}, {"粟粒", "粟粒性", "粟粒型"},
	{"糖尿病", "糖尿病性", "糖尿病型"}, {"糜烂", "糜烂性", "糜烂型"}, {"系统", "系统性", "系统型"}, {"细菌", "细菌性", "细菌型"}, {"结核", "结核性", "结核型"}, {"结石", "结石性", "结石型"}, {"结节", "结节性", "结节型"},
	{"继发", "继发性", "继发型"}, {"缩窄", "缩窄性", "缩窄型"}, {"缺血", "缺血性", "缺血型"}, {"缺铁", "缺铁性", "缺铁型"}, {"老年", "老年性", "老年型"}, {"肝硬化", "肝硬化性", "肝硬化型"}, {"肥厚", "肥厚性", "肥厚型"},
	{"肺性", "肺型"}, {"肺源", "肺源性", "肺源型"}, {"肾小管", "肾小管性", "肾小管型"}, {"肾血管", "肾血管性", "肾血管型"}, {"胆汁反流", "胆汁反流性", "胆汁反流型"}, {"胰腺假", "胰腺假性", "胰腺假型"}, {"胶原", "胶原性", "胶原型"},
	{"膜增生", "膜增生性", "膜增生型"}, {"膜性", "膜型"}, {"自发", "自发性", "自发型"}, {"自身免疫", "自身免疫性", "自身免疫型"}, {"良性", "良型"}, {"药物", "药物性", "药物型"}, {"营养不良", "营养不良性", "营养不良型"},
	{"血栓", "血栓性", "血栓型"}, {"贫血", "贫血性", "贫血型"}, {"过敏", "过敏性", "过敏型"}, {"过缓", "过缓性", "过缓型"}, {"酒精", "酒精性", "酒精型"}, {"钝挫", "钝挫性", "钝挫型"}, {"间质", "间质性", "间质型"},
	{"阴塞", "阴塞性", "阴塞型"}, {"阵发", "阵发性", "阵发型"}, {"阻塞", "阻塞性", "阻塞型"}, {"阿米巴", "阿米巴性", "阿米巴型"}, {"隐匿", "隐匿性", "隐匿型"}, {"顽固", "顽固性", "顽固型"}, {"风湿", "风湿性", "风湿型"},
	{"骨性", "骨型"}, {"骨髓病", "骨髓病性", "骨髓病型"}, {"高血压", "高血压性", "高血压型"}, {"黄疸", "黄疸性", "黄疸型"}, {"外源", "外源性", "外源型"}, {"反应", "反应性", "反应型"}, {"炎性", "炎型"}, {"疱疹", "疱疹性", "疱疹型"},
	{"生理", "生理性", "生理型"}, {"流行", "流行性", "流行型"}, {"危险", "危险性", "危险型"},
}

// 定义二维列表存储同义词：方位
var SynonymsOrientation = [][]string{
	{"前", "前部", "前侧", "前方", "前端", "前段", "前面", "前壁"}, {"后", "后部", "后侧", "后方", "前端", "后段", "后面", "后壁"}, {"前外侧"}, {"后外侧"},
	{"上", "上面", "上部", "上侧", "上方", "上端", "上段", "上壁"}, {"下", "下面", "下部", "下侧", "下方", "下端", "下段", "下壁"}, {"中", "中部", "中侧", "中间", "之间", "中段"},
	{"内", "内部", "内侧", "里面", "里", "内壁", "内侧壁"}, {"外", "外部", "外侧", "外围", "外周", "外面", "表面"}, {"内外侧"}, {"内上部"}, {"内下部"},
	{"左", "左部", "左侧"}, {"右", "右部", "右侧"},
	{"中上段"}, {"中下段"},
	{"近端", "近段", "近侧端"}, {"远端", "远段", "远侧端"}, {"远近侧端"},
	{"单侧", "一侧", "单侧面"}, {"两端", "两侧", "双侧"},
	{"侧方", "侧壁"},
	{"伸侧", "伸侧面"}, {"屈侧"}, {"尺侧"},
	{"周围", "旁", "旁边", "附近", "旁隙"}, {"间隙"},
	{"末端", "末段", "后缘", "边缘"}, {"深部"}, {"后极部"}, {"基底部"},
}

// 定义二维列表存储同义词：人
var SynonymsPeople = [][]string{
	{"儿童", "孩子", "小孩", "小儿", "婴儿", "宝宝", "宝"}, {"男童", "男孩"}, {"女童", "女孩"},
	{"男", "男性", "男生", "男士", "男人"}, {"女", "女性", "女生", "女士", "女人"},
	{"母亲", "母", "妈妈", "妈"}, {"父亲", "父", "爸爸", "爸"},
	{"老人", "老年人"},
}

// 定义二维列表存储同义词：身体部位
var SynonymsBody = [][]string{
	{"人体", "身体"},
	{"头", "头部"}, {"脑", "脑部", "大脑", "脑袋", "脑子"}, {"鼻", "鼻腔", "鼻部", "鼻子"}, {"口", "口腔", "唇", "唇部", "嘴", "嘴唇", "嘴巴"}, {"耳", "耳朵"}, {"牙齿", "牙"},
	{"眼", "眼部", "眼睛", "眼细胞", "视力"}, {"咽", "咽部", "喉", "喉部", "咽喉", "喉咙", "嗓子", "声音"},
	{"颈", "颈部", "脖", "脖子"}, {"肩", "肩部", "肩膀"}, {"背", "背部", "后背"}, {"手", "手部", "手掌"}, {"脚", "脚部", "脚掌"}, {"骨", "骨骼", "骨头"},
	{"胸", "胸部", "胸腔"}, {"肺", "肺部"}, {"胃", "胃部"}, {"肠", "肠道", "肠子"}, {"腹", "腹部", "肚", "肚子"},
	{"心", "心脏"}, {"左心房", "左心室", "左房", "左室"}, {"右心房", "右心室", "右房", "右室"},
	{"阴", "阴部", "阴道", "阴茎", "外阴"},
	{"黏膜", "粘膜"}, {"冠状动脉", "冠脉"}, {"泌尿系统", "泌尿系"},
}

// 定义二维列表存储同义词：生化&英文
var SynonymsBiochemical = [][]string{
	{"低密度脂蛋白胆固醇", "低密度脂蛋白", "低密度胆固醇", "LDL-C", "LDLC"}, {"高密度脂蛋白胆固醇", "高密度脂蛋白", "高密度胆固醇", "HDL-C", "HDLC"}, {"丙氨酸氨基转移酶", "谷丙转氨酶", "ALT"}, {"天冬氨酸氨基转移酶", "谷草转氨酶", "AST"},
	{"甘油三酯", "三酰甘油", "甘油三脂"}, {"胆固醇", "总胆固醇"},
	{"核磁共振成像", "核磁共振", "磁共振", "MRI"}, {"计算机断层扫描", "CT"}, {"X线", "X光", "X射线"},
	{"维生素A", "维A"}, {"维生素A1", "维A1"}, {"维生素A2", "维A2"}, {"维生素B", "维B"}, {"维生素B1", "维B1"}, {"维生素B2", "维B2"}, {"维生素C", "维C"}, {"维生素D", "维D"}, {"维生素D1", "维D1"}, {"维生素D2", "维D2"}, {"维生素D3", "维D3"}, {"维生素E", "维E"},
	{"艾滋病", "HIV"}, {"人乳头瘤病毒", "HPV"}, {"幽门螺旋杆菌", "幽门螺杆菌", "幽螺杆菌", "HP"},
}

// 定义二维列表存储同义词：疾病&症状
var SynonymsDisease = [][]string{
	{"气血"}, {"血压"}, {"后遗症"},
	{"疼", "痛", "疼痛", "酸痛", "胀痛", "绞痛", "挛痛", "压痛", "按压痛", "有点痛", "很痛", "作痛", "会痛"},
	{"麻", "麻木", "发麻"}, {"止痛", "止疼"}, {"止痛药", "止疼药"},
	{"免疫系统", "免疫"}, {"呼吸系统", "呼吸"}, {"心脑血管系统", "心脑血管"}, {"心血管系统", "心血管"},
	{"泌尿生殖系统", "泌尿生殖"}, {"泌尿系统", "泌尿"}, {"消化系统", "消化"}, {"生殖系统", "生殖"},
	{"神经系统", "神经"}, {"肌肉系统", "肌肉"}, {"血液系统", "血液"}, {"造血系统", "造血"}, {"骨骼系统", "骨骼"},
	{"子宫出血", "宫血"},
	{"月经", "月经期", "月经期间", "经", "经期", "姨妈", "姨妈期", "姨妈期间", "大姨妈", "大姨妈期间"},
	{"妊娠", "怀孕", "怀", "怀上", "孕", "受孕", "孕妇"}, {"妊娠反应", "怀孕反应", "孕反", "孕吐"},
	{"不孕", "怀不上", "不能怀孕"}, {"异位妊娠", "宫外孕"},
	{"生", "生育", "生产", "生孩子"}, {"流产", "人流", "堕胎"},
	{"心力衰竭", "心脏衰竭", "心衰", "心衰竭"}, {"心肌梗塞", "心梗"}, {"脑梗塞", "脑梗"}, {"脑梗死", "脑梗"},
	{"腔隙性脑梗塞", "腔梗"}, {"腔隙性脑梗死", "腔梗"}, {"甲状腺功能减退", "甲减"},
	{"支气管扩张症", "支气管扩张", "支扩"}, {"精神分裂", "精分"}, {"先天性心脏病", "先心"},
	{"再生障碍性贫血", "再障"}, {"上呼吸道感染", "上感"}, {"呼吸衰竭", "呼衰"},
	{"甲型肝炎", "甲肝"}, {"乙型肝炎", "乙肝"}, {"丙型肝炎", "丙肝"},
	{"泌尿结石", "泌尿系结石", "泌尿系统结石", "尿结石", "尿路结石"},
	{"阿尔兹海默病", "阿尔兹海默症", "老年痴呆症", "老年痴呆病", "老年痴呆"},
	{"高血压", "血压高", "血压偏高"}, {"低血压", "血压低", "血压偏低"}, {"血栓", "栓塞", "堵塞"},
	{"升高血压", "升血压", "升压"}, {"降低血压", "降血压", "降压"}, {"升高血糖", "升血糖", "升糖"}, {"降低血糖", "降血糖", "降糖"},
	{"脑溢血", "脑出血"}, {"足藓", "脚藓"}, {"脑卒中", "卒中", "脑中风", "中风"}, {"鼻窦炎", "鼻渊"}, {"矽肺", "硅肺", "尘肺", "石棉肺"},
	{"腹泻", "拉肚子"}, {"呕吐", "呕", "吐"}, {"口臭", "嘴臭"}, {"打鼾", "打呼", "打呼噜"},
	{"手淫", "自慰", "打飞机"}, {"流鼻血", "鼻出血", "鼻流血", "鼻子流血", "鼻子出血"},
	{"瘙痒", "痒", "发痒"},
	{"急性发作", "急发"}, {"功能失调性", "功能性"},
	{"抽搐", "搐搦"}, {"疝", "疝气"},
}

// 定义二维列表存储同义词：药品&食品
var SynonymsMedicine = [][]string{
	{"中医药"},
	{"药", "药品", "药物", "药剂", "药方", "药性", "用药", "药材"}, {"中药", "中药方", "中药方剂", "药材"},
	{"膏", "膏剂", "乳膏", "软膏"}, {"片", "片剂", "分散片"},
	{"青霉素", "青霉素类", "青霉素类型", "青霉素系列"}, {"头孢", "头孢类", "头孢系列"},
	{"眼药水", "滴眼液", "滴眼药"},
	{"孕酮", "黄体酮"},
	{"豆腐干", "豆干"},
}

// 定义二维列表存储同义词：时间词
var SynonymsTime = [][]string{
	{"春季", "春天", "春"}, {"夏季", "夏天", "夏"}, {"秋季", "秋天", "秋"}, {"冬季", "冬天", "冬"},
	{"早", "晨", "早晨", "早上", "清早", "清晨"}, {"午", "中午", "午间"}, {"晚", "夜", "夜晚", "夜间", "夜里", "半夜", "晚上"}, {"白天", "白日"},
	{"午休", "午睡", "睡觉"},
	{"早餐", "早饭"}, {"午餐", "午饭"}, {"晚餐", "晚饭"}, {"餐后", "饭后", "吃完饭", "吃完饭后", "吃完饭之后", "吃完饭以后", "餐", "饭"}, {"餐前", "饭前", "吃饭前", "吃饭之前", "吃饭以前", "餐", "饭"},
	{"每日", "每天", "天天"},
	{"术前", "手术前", "手术之前", "手术以前", "手术"}, {"术后", "手术后", "手术之后", "手术以后", "手术过后", "手术"},
	{"产后", "生产后", "生产之后", "生产过后", "生产过后", "生完", "生", "产", "生产"},
}

// 定义二维列表存储同义词：一般词
var SynonymsCommon = [][]string{
	{"空腹"}, {"备孕"},
	{"能力", "力"}, {"时期", "期"},
	{"母乳", "奶"}, {"蛋白质", "蛋白"},
	{"尿", "尿液", "尿尿", "排尿", "小便", "小解"}, {"屎", "拉屎", "便便", "排便", "大便", "大解", "拉大便", "拉"},
	{"血", "血液", "抽血", "血检", "验血", "血常规"}, {"血糖", "糖"}, {"血", "出血", "流血"},
	{"发热", "发烧", "高烧", "发高烧"},
	{"吸烟", "抽烟"}, {"戒烟", "戒掉"}, {"切", "切除", "摘除", "术", "手术"},
	{"生命", "性命", "寿命"}, {"死", "亡", "死亡", "致死"}, {"危险", "危害", "风险"},
	{"传播", "传染", "感染"}, {"咬伤", "咬"}, {"抓伤", "抓"},
	{"引流管", "导管"},
	{"衣", "衣物", "衣服"}, {"茶", "泡茶", "泡水", "泡茶喝", "泡水喝"}, {"记性", "记忆", "记忆力"},
	{"配方", "组方", "组成", "构成"},
	{"频率", "节律"},
	{"保养", "养护", "保护"}, {"失调", "失衡", "不调"},
	{"疲倦", "疲惫", "疲劳", "倦怠", "乏力", "没劲", "没精神", "没精力", "累"},
	{"持续", "维持"}, {"推迟", "延迟", "推后", "延后", "延长", "延缓", "迟缓"},
	{"好", "好处", "益处", "优点"}, {"坏", "坏处", "缺点", "副作用", "不良反应", "不好"}, {"不好", "不舒服"},
	{"失去", "没有"}, {"去除", "除去", "消除"},
	{"补充", "补"}, {"患", "得"},
	{"价", "钱", "价格", "收费", "费用", "价钱", "花费", "多少钱"},
}

// 定义二维列表存储同义词：非扣分词
var SynonymsNone = [][]string{
	{"血清"},
	{"吃", "吃饭", "饮食", "进食", "食疗", "食补", "补", "补充", "食用", "服用", "服法", "吃法", "食物", "喂", "嚼", "咀嚼"}, {"吃", "喝", "饮"},
	{"打", "打完", "注射", "接种", "疫苗", "针"},
	{"病", "疾病", "病种"}, {"症状", "表现", "情况", "状况", "适应症"},
	{"岁", "周岁"}, {"老", "变老", "衰老", "衰退"},
	{"部", "部位", "位置", "地方"}, {"种", "类", "种类", "类型", "类别", "一类", "哪一类", "哪一种类", "哪一类型", "哪一类别"}, {"项", "一项", "项目", "哪项", "哪一项", "哪一项目"},
	{"关系", "关联"}, {"成份", "成分"},
	{"诊断", "确诊", "明确诊断", "判断", "判定", "鉴别", "鉴定", "诊治"}, {"检查", "查", "复查"},
	{"调理", "治疗", "诊治", "治好", "诊疗", "诊治", "专治", "主治"}, {"调理", "调护", "护理"}, {"恢复", "治愈", "治疗", "治好", "医治", "康复", "自愈"},
	{"导致", "引起", "造成", "引发", "诱发", "致使", "原因", "缘由", "因素"}, {"原因", "原由", "成因", "起因", "诱因", "病因", "因素", "根源", "来源", "为什么", "什么情况", "什么原因", "什么问题", "什么意思", "怎么回事", "咋回事"}, {"因为", "因为什么", "因为什么原因", "因为什么原因导致", "因为什么原因引起", "因为什么原因造成"},
	{"作用", "效果", "功能", "功效", "起效", "有效", "有效果", "效用", "用途", "作用机制", "药理作用", "药效"}, {"机制", "机理"},
	{"方法", "方案", "方式", "办法", "做法", "策略", "措施", "手段"},
	{"处理", "怎么", "怎么办"},
	{"反映", "说明", "代表", "含义", "意思", "意义", "意味"},
	{"指标", "指数", "范围", "水平", "标准", "多少"}, {"正常", "标准"},
	{"量", "剂量", "用量", "含量", "单位"},
	{"促进", "帮助", "利于", "有助", "助于", "有利于", "有助于", "好"},
	{"预防", "防止"},
	{"感觉", "感到", "发觉", "发现"}, {"想", "老想"}, {"放", "放置"},
	{"总是", "常常", "时常", "经常", "通常", "常", "老是", "频繁", "不停", "一直"}, {"几率", "概率", "可能性", "多大"},
	{"相同", "一样", "同样", "一致", "相等", "等同"}, {"不同", "不一样", "不相同", "不一致", "区别", "区分", "比较"},
	{"同时", "一起", "同"}, {"何时", "时间", "时机"}, {"多久", "多长时间"},
	{"平常", "平时", "平日"},
	{"最好", "最佳", "第一"},
	{"适宜", "适合", "合适", "宜", "好"}, {"不宜", "不适宜", "不适合", "不合适", "不好"},
	{"快", "较快", "很快", "变快", "快速", "加快", "越来越快"}, {"慢", "较慢", "很慢", "变慢", "慢速", "减慢", "越来越慢"},
	{"好", "较好", "很好", "变好", "越来越好"}, {"差", "较差", "很差", "变差", "越来越差", "坏", "变坏", "越来越坏"},
	{"大", "较大", "很大", "变大", "越来越大"}, {"小", "较小", "很小", "变小", "越来越小"},
	{"多", "较多", "很多", "变多", "越来越多", "增多", "增加"}, {"少", "较少", "很少", "变少", "越来越少", "减少", "缩小", "降低"},
	{"高", "较高", "很高", "过高", "变高", "越来越高", "升高", "增高", "偏高", "偏高些", "略高", "提高", "有一点高"}, {"低", "较低", "很低", "过低", "变低", "越来越低", "降低", "偏低", "偏低些", "略低", "有一点低"},
	{"升", "上升", "升高", "提高", "提升", "增强", "加强", "增加"}, {"降", "下降", "降低", "减弱", "减退", "衰退", "差", "较差", "很差", "变差", "越来越差"}, {"降", "下降", "降低", "减弱", "减缓", "缓解"},
	{"高", "高不高", "不高"}, {"低", "低不低", "不低"}, {"多", "不多", "多不多"}, {"少", "不少", "少不少"},
	{"该", "不该", "该不该", "应", "应该", "不应该", "应不应该", "应该不应该", "应当", "不应当", "应不应当", "应当不应当",
		"要", "不要", "要不要", "需要", "不需要", "需不需要", "需要不需要",
		"会", "不会", "会不会", "能", "不能", "能不能", "可以", "不可以", "可不可以", "可以不可以",
		"行", "不行", "行不行", "是", "不是", "是不是", "还是",
		"有", "没有", "有没有"},
	{"有用", "没用", "有没有用"}, {"好", "不好", "好不好"},
	{"有", "伴", "伴有", "伴随", "并发"},
	{"含", "包含", "包括", "含有"}, {"属", "属于", "所属", "是"},
	{"是否", "能否"},
	{"又", "再", "再次"}, {"只", "仅", "仅仅"},
	{"和", "与", "跟", "及", "合并"}, {"需", "要", "需要"}, {"用", "用于", "用来", "使用", "用法", "应用", "适用", "适用于", "常用", "常用于"}, {"点", "一点", "一点点"},
	{"后", "过后", "以后"},
	{"为何", "为啥", "为什么"}, {"如何", "怎样", "怎么样"}, {"哪些", "哪个", "哪种", "哪里"}, {"一般", "一般来说"},
	{"没事", "没问题", "没关系"}, {"注意", "注意什么", "注意事项"},
}

// 定义列表存储非扣分词字典
var NoneList = [][]string{
	{
		"长", "发", "指", "按", "完", "时", "刚", "专", "查", "叫", "算", "值",
		"被", "把", "其", "其他", "几", "几个", "才", "做", "也", "这", "都", "的", "很", "还", "就", "最", "何", "了",
		"越来越", "超过",
		"医生", "经典", "典型", "专业", "专家", "技术", "说明书", "方面",
		"细胞", "问题", "结果", "分析", "解析", "影响", "建议", "步骤", "自然", "科学", "小时", "时间", "东西", "事项", "现象", "原理", "质量", "系列", "后果", "途径", "临床", "发展", "状态", "价值", "提示", "存在",
		"发生", "出现", "解决", "安排", "进行", "操作", "注意", "改善", "形成", "开始", "知道", "反应", "了解", "相关", "有关",
		"主要", "具体", "全部", "准确", "常见", "正确", "明显", "容易",
		"严重", "彻底", "感染", "阳性", "阴性",
		"基于", "背后",
		"前后", "之间",
		"马上", "起来",
		"不管", "什么", "什么样", "啊", "呀", "吗", "吧", "啥",
		"一", "一天", "一些", "一次", "一个", "一种", "一会", "一下", "一定",
	},
}

// 定义列表存储扣分词字典
var ContrastList = [][]string{
	{"规格", "价格"},
	{"慢性", "急性"}, {"早期", "晚期", "中期"},
	{"药", "中药", "中成药"}, {"中药", "中医"},
	{"肠癌", "直肠癌", "结肠癌"},
	{"结石", "泌尿结石", "输尿管结石", "膀胱结石", "前列腺结石", "附睾结石", "肾结石", "肾盂结石", "肾小管结石", "胆结石", "胰腺结石", "大肠结石", "肠道结石", "胃肠道结石"},
	{"鼻炎", "鼻窦炎", "气管炎", "细支气管炎", "胆囊炎", "胆管炎", "肺炎", "肺泡炎", "肠炎", "结肠炎", "直肠炎", "十二指肠炎", "大肠炎", "小肠炎", "肠胃炎", "胃炎", "关节炎", "髋关节炎", "脑炎", "脑膜炎"},
	{"呼吸道", "上呼吸道", "下呼吸道"}, {"咽", "喉"}, {"鼻", "鼻窦"},
	{"豆腐干", "干豆腐"},
}

// removeWords 移除屏蔽词
func RemoveWords(wordList []string, blockList []string) []string {
	var result []string
	for _, str := range wordList {
		for _, word := range blockList {
			str = strings.ReplaceAll(str, word, "")
		}
		if strings.TrimSpace(str) != "" {
			result = append(result, str)
		}
	}
	return result
}

// removePrefix 移除前缀
func RemovePrefix(wordList []string, prefixDict []string) []string {
	var result []string
	for _, word := range wordList {
		for _, prefix := range prefixDict {
			if strings.HasPrefix(word, prefix) {
				word = strings.TrimPrefix(word, prefix)
				break
			}
		}
		result = append(result, word)
	}
	return result
}

// removeSuffix 移除后缀
func RemoveSuffix(wordList []string, suffixDict []string) []string {
	var result []string
	for _, word := range wordList {
		for _, suffix := range suffixDict {
			if strings.HasSuffix(word, suffix) {
				word = strings.TrimSuffix(word, suffix)
				break
			}
		}
		result = append(result, word)
	}
	return result
}

// removeAffix 移除前后缀
func RemoveAffix(wordList []string, prefixDict []string, suffixDict []string) []string {
	wordList = RemovePrefix(wordList, prefixDict)
	wordList = RemoveSuffix(wordList, suffixDict)
	return wordList
}

// synonymMatch 同义词匹配
func SynonymMatch(word string, title string, synonymDict [][]string) bool {
	for _, synGroup := range synonymDict {
		isWordInGroup := false
		for _, syn := range synGroup {
			if syn == word {
				isWordInGroup = true
				break
			}
		}
		if isWordInGroup {
			for _, syn := range synGroup {
				if syn == word {
					continue
				}
				if strings.Contains(title, syn) {
					return true
				}
			}
		}
	}
	return false
}

// checkRemove 结合 removeWords 和 removeSuffix 函数
func CheckRemove(wordList []string, blockList []string, suffixDict []string) []string {
	wordList = RemoveWords(wordList, blockList)
	wordList = RemoveSuffix(wordList, suffixDict)
	return wordList
}

// checkSynonyms 检查两个单词是否为同义词
func CheckSynonyms(word1, word2 string) bool {
	synonymsList := append(
		append(
			append(
				append(
					append(
						append(
							append(
								append(
									append(SynonymsPrefix, SynonymsOrientation...),
									SynonymsPeople...),
								SynonymsBody...),
							SynonymsBiochemical...),
						SynonymsDisease...),
					SynonymsMedicine...),
				SynonymsTime...),
			SynonymsCommon...),
		SynonymsNone...)
	for _, synonymGroup := range synonymsList {
		foundWord1 := false
		foundWord2 := false
		for _, word := range synonymGroup {
			if word == word1 {
				foundWord1 = true
			}
			if word == word2 {
				foundWord2 = true
			}
		}
		if foundWord1 && foundWord2 {
			return true
		}
	}
	return false
}

// checkCommon 检查一个单词是否在常见同义词组中
func CheckCommon(word string) bool {
	commonList := append(append(NoneList, SynonymsNone...), SynonymsOrientation...)
	for _, commonGroup := range commonList {
		for _, w := range commonGroup {
			if w == word {
				return true
			}
		}
	}
	return false
}

// checkContrast 检查两个单词是否为对比词
func CheckContrast(word1, word2 string) bool {
	wordList := ContrastList
	for _, sublist := range wordList {
		word1Found := false
		word2Found := false
		for _, word := range sublist {
			if word == word1 {
				word1Found = true
			}
			if word == word2 {
				word2Found = true
			}
		}
		if word1Found && word2Found && word1 != word2 {
			return true
		}
	}
	return false
}
