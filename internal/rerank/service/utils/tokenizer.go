package utils

import (
	"context"
	"fmt"
	"math"
	"strings"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/consts"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/global"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	util "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/data_tool"
	aseclient "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/ase_sdk/client"
	tokenizer_wrapper "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/tokenizer_v2_1"
)

func AssembleToken(terms [][]string) (map[string][]byte, map[string][]int64, error) {
	tokenMap := make(map[string][]byte)
	shapeMap := make(map[string][]int64)
	lengthMax := 0
	batchSize := len(terms)
	queries := make([]string, batchSize)
	termsLength := make([][]int64, batchSize)
	var encodings3 = make([]*tokenizer_wrapper.Encoding[int64], 0, len(terms))

	for i, term := range terms {

		lengthMax = max(lengthMax, len(term))
		termsLength[i] = make([]int64, len(term))
		queries[i] = strings.Join(term, "")

		tokenModel := tokenizer_wrapper.NewTokenizerEncodingWithAsync[int64](global.IFLYS_TokenModelServer)
		tokenSample := tokenizer_wrapper.NewTokenizerEncoding[int64](global.IFLYS_SAMPLE_Tokenizer)
		encodings, _ := tokenSample.EncodeBatch(term, false)
		encodings2, _ := tokenModel.AsyncEncodeBatch(term, false)

		for j, encoding := range encodings {
			termsLength[i][j] = int64(len(encoding.Ids))
		}
		en := &tokenizer_wrapper.Encoding[int64]{}
		en.Ids = append(en.Ids, consts.CLS)
		en.Types = append(en.Types, consts.TYPES)
		en.Masks = append(en.Masks, consts.MASK)
		for _, encoding := range encodings2 {
			for i, _ := range encoding.Ids {
				if encoding.Ids[i] > 0 {
					en.Ids = append(en.Ids, encoding.Ids[i])
					en.Masks = append(en.Masks, encoding.Masks[i])
					en.Types = append(en.Types, encoding.Types[i])
				}
			}
		}
		en.Ids = append(en.Ids, consts.SEP)
		en.Types = append(en.Types, consts.TYPES)
		en.Masks = append(en.Masks, consts.MASK)
		encodings3 = append(encodings3, en)
	}

	// 构造term_t_lens
	termsTLens := make([][]int64, batchSize)
	for i := 0; i < batchSize; i++ {
		termsTLens[i] = make([]int64, lengthMax)
		for j := 0; j < lengthMax; j++ {
			termsTLens[i][j] = 1
		}
	}

	for i, lens := range termsLength {
		copy(termsTLens[i][:len(lens)], lens)
	}

	ids := make([][]int64, len(encodings3))
	for i, encoding := range encodings3 {
		ids[i] = encoding.Ids
	}
	seqLength := len(ids[0])

	// 构造terms_mask
	termsMask := make([][][]float32, batchSize)
	for i := 0; i < batchSize; i++ {
		termsMask[i] = make([][]float32, lengthMax)
		for j := 0; j < lengthMax; j++ {
			termsMask[i][j] = make([]float32, seqLength)
		}
	}

	for i := 0; i < batchSize; i++ {
		lens := termsLength[i]
		var start int64 = 1
		for j, l := range lens {
			for k := start; k < start+l; k++ {
				termsMask[i][j][k] = 1
			}
			start += l
		}
	}

	for i, encoding := range encodings3 {
		termsMaskFlat := make([]float32, 0)
		for _, row := range termsMask[i] {
			termsMaskFlat = append(termsMaskFlat, row...)
		}
		tokenMap["input_ids"] = append(tokenMap["input_ids"], util.SliceToBytes(encoding.Ids)...)
		tokenMap["input_mask"] = append(tokenMap["attention_mask"], util.SliceToBytes(encoding.Masks)...)
		tokenMap["token_type_ids"] = append(tokenMap["token_type_ids"], util.SliceToBytes(encoding.Types)...)
		tokenMap["terms_t_lens"] = append(tokenMap["terms_t_lens"], util.SliceToBytes(termsTLens[i])...)
		tokenMap["terms_mask"] = append(tokenMap["terms_mask"], util.SliceToBytes(termsMaskFlat)...)
	}

	_batchSize := common.Interface2I64(len(queries))
	shapeMap["input_ids"] = []int64{_batchSize, int64(len(encodings3[0].Ids))}
	shapeMap["input_mask"] = []int64{_batchSize, int64(len(encodings3[0].Masks))}
	shapeMap["token_type_ids"] = []int64{_batchSize, int64(len(encodings3[0].Types))}
	shapeMap["terms_t_lens"] = []int64{_batchSize, int64(len(termsTLens[0]))}
	shapeMap["terms_mask"] = []int64{_batchSize, int64(lengthMax), int64(len(encodings3[0].Ids))}
	return tokenMap, shapeMap, nil
}

func TermWeight(req *bean.Request, span *span.Span, terms []string) ([]string, []float64, *errtypes.SelfError) {

	var values = make([]float64, len(terms))

	// tokenizer
	tokenMap, shapeMap, err := AssembleToken([][]string{terms})
	if err != nil {
		return nil, nil, selferrors.SegError_TokenizerError.Detaild(err.Error())
	}
	// 调用模型推理
	// TODO: 不用常量
	client := goboot.AseSdk().GetInstance(global.TermWeightVersion).Request().
		SetTraceId(req.Header.TraceId).
		SetHeaderTag(req.Header.Tag)
	resp, err := client.Send(context.TODO(), &aseclient.InferReq{
		Inputs: tokenMap,
		Shapes: shapeMap,
	})

	if err != nil {
		goboot.Logger().DefaultInstance().Error(err.Error())
		fmt.Sprintf("推理错误:%v\n", err.Error())
		return nil, nil, selferrors.SegError_InferError.Detaild(err.Error())
	}
	if resp != nil && resp.Header.Code != 0 {
		return nil, nil, selferrors.SegError_InferError.Detaild(fmt.Sprintf("code:%d, message:%v\n", resp.Header.Success))
	}
	outputs := util.BytesToSlice[float32](resp.Payload.Outputs["term_weights"])
	termWeights, _ := common.Normalize(outputs)

	for i := 0; i < len(terms); i++ {
		if math.IsNaN(float64(termWeights[i])) {
			return terms, values, selferrors.SegError_InferError.Detaild("termWeights中存在NaN值")
		}
		values[i] = common.Interface2F64(termWeights[i])
	}

	return terms, values, nil
}
