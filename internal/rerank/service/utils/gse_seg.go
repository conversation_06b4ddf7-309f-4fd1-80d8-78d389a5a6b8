package utils

import (
	"regexp"
	"strings"
	"unicode"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/config"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
)

// 罗马数字映射表
var romanToArabic = map[rune]rune{
	'Ⅰ': '1',
	'Ⅱ': '2',
	'Ⅲ': '3',
	'Ⅳ': '4',
	'Ⅴ': '5',
}

// 分词前处理一: 同义词及特殊符号处理
func ReplacePain(input string) string {
	var b strings.Builder
	b.Grow(len(input)) // 预先分配空间，减少内存重新分配

	for i, r := range input {
		switch {
		case i < len(input)-1 && r == '疼' && rune(input[i+1]) == '痛':
			// 遇到“疼痛”，替换为“痛”
			b.WriteRune('痛')
			i++ // 跳过下一个字符
		case r == '疼':
			// 遇到单个“疼”，替换为“痛”
			b.WriteRune('痛')
		case r == '\n' || r == ' ':
			// 遇到换行符或空格，替换为逗号
			b.WriteRune(',')
		default:
			// 其他情况直接添加当前字符
			b.WriteRune(r)
		}
	}
	return b.String()
}

// 分词后处理一: 叠词（重复数字不能处理去重）
func MergeRepeatedChars(strList []string) []string {
	result := make([]string, 0, len(strList))
	for _, s := range strList {
		result = append(result, mergeRepeatedCharsInString(s))
	}
	return result
}

// mergeRepeatedCharsInString 处理单个字符串中的叠词
func mergeRepeatedCharsInString(s string) string {
	var b strings.Builder
	b.Grow(len(s)) // 预先分配空间，减少内存重新分配

	runes := []rune(s)
	for i := 0; i < len(runes); {
		if ShouldMerge(runes, i) {
			b.WriteRune(runes[i])
			i += 2
		} else {
			b.WriteRune(runes[i])
			i++
		}
	}
	return b.String()
}

func ShouldMerge(r []rune, i int) bool {
	return i+1 < len(r) &&
		r[i] == r[i+1] &&
		!unicode.IsDigit(r[i])
}

// 通用分词处理函数
func commonSegProcess(req *bean.Request, span *span.Span, text string, processTitle bool) (bean.SegRes, error) {
	// 字母大写
	text = strings.ToUpper(text)
	// 调用 ReplacePain 函数处理查询
	text = ReplacePain(text)
	// 分词
	keywords := config.SegBioMedical.Cut(text, false)
	// 调用 MergeRepeatedChars 函数处理叠词
	keywords = MergeRepeatedChars(keywords)
	// term weight 权重
	keys, values, err := TermWeight(req, span, keywords)
	if err != nil {
		return bean.SegRes{}, err
	}

	return bean.SegRes{Keys: keys, Value: values}, nil
}

// query 分词
func QuerySeg(req *bean.Request, span *span.Span, query string) (bean.SegRes, error) {
	return commonSegProcess(req, span, query, false)
}

// title 分词
func TitleSeg(req *bean.Request, span *span.Span, title string) (bean.SegRes, error) {
	// 处理标题后缀
	title = processTitleSuffix(title)
	return commonSegProcess(req, span, title, true)
}

// processTitleSuffix 处理标题后缀信息
func processTitleSuffix(title string) string {
	titleSegs := strings.Split(title, "_")
	switch {
	case len(titleSegs) >= 3:
		// Join all segments except the last two
		return strings.Join(titleSegs[:len(titleSegs)-2], "_")
	case len(titleSegs) == 2:
		// Only keep the first segment
		return titleSegs[0]
	default:
		return title
	}
}

// replaceSpecialChars 特殊字符处理, 例如: 肾前性 病因\n肾前性 症状（转义字符\n出现在 baike 结果）
func replaceSpecialChars(str string) string {
	var result []rune
	for _, char := range str {

		if unicode.IsLower(char) || unicode.IsUpper(char) || unicode.IsDigit(char) ||
			(char >= '\u4e00' && char <= '\u9fff') || (char >= '\u0370' && char <= '\u03ff') {
			result = append(result, char) // 保留字符
		} else if arabic, ok := romanToArabic[char]; ok {
			result = append(result, arabic) // 替换罗马数字为阿拉伯数字
		} else {
			result = append(result, ' ') // 替换成空格
		}
	}
	return string(result)
}

var spaceRegex = regexp.MustCompile(`\s+`)

// removeSpaceChars 空格处理, term_weight 无法处理含空格的输入
func removeSpaceChars(stringList []string) []string {
	result := make([]string, 0, len(stringList))
	for _, str := range stringList {
		s := spaceRegex.ReplaceAllString(str, "")
		if s != "" {
			result = append(result, s)
		}
	}
	return result
}

// removeDuplicateChars 连续重复字符处理
func removeDuplicateChars(strings []string) []string {
	result := make([]string, 0, len(strings))
	for _, s := range strings {
		result = append(result, removeDuplicateCharsInString(s))
	}
	return result
}

// removeDuplicateCharsInString 处理单个字符串中的连续重复字符
func removeDuplicateCharsInString(s string) string {
	var newString []rune
	var prevChar rune
	for _, char := range s {
		if unicode.IsDigit(char) { // 如果是数字, 加入新字符串
			newString = append(newString, char)
			prevChar = char
		} else if char != prevChar { // 如果非数字且与前一个字符不同, 加入新字符串
			newString = append(newString, char)
			prevChar = char
		}
	}
	return string(newString)
}

func segmentation(context string, Dictionary []string) []string { // Dictionary 为列表
	var result []string
	index := 0

	for index < len(context) {
		found := false
		for _, word := range Dictionary { // 优先按自定义词典分词
			if strings.HasPrefix(context[index:], word) {
				result = append(result, word)
				index += len(word)
				found = true
				break
			}
		}

		if !found { // 无法匹配自定义词典的子串
			if isAlphanumericOrDot(context[index]) {
				start := index
				for index < len(context) && isAlphanumericOrDot(context[index]) {
					index++
				}
				result = append(result, context[start:index])
			} else {
				hmm := false
				words := config.IFLYS_SEG_JIEBA.Cut(context[index:], hmm) // 处理余剩未处理词 jieba 分词
				if len(words) > 0 {
					result = append(result, words[0])
					index += len(words[0])
				} else {
					result = append(result, string(context[index]))
					index++
				}
			}
		}
	}
	return result
}

// isAlphanumericOrDot 判断字符是否为数字、字母或点号
func isAlphanumericOrDot(c byte) bool {
	return (c >= '0' && c <= '9') || (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || c == '.'
}

// 分词
func segmentationProcess(req *bean.Request, span *span.Span, context string) (bean.SegRes, error) {

	// 处理特殊符号
	context = replaceSpecialChars(context)

	// 处理分词
	words := segmentation(context, config.Dictionary)

	// 删除屏蔽词
	words = CheckRemove(words, BlockList[0], SuffixList[0])

	// 删除连续重复字符
	words = removeDuplicateChars(words)

	// 删除空格
	words = removeSpaceChars(words)

	keys, values, err := TermWeight(req, span, words)
	if err != nil {
		return bean.SegRes{}, err
	}

	// 返回
	return bean.SegRes{Keys: keys, Value: values}, nil
}

// query 分词
func QuerySeg0620(req *bean.Request, span *span.Span, query string) (bean.SegRes, error) {
	return segmentationProcess(req, span, query)
}

// title 分词
func TitleSeg0620(req *bean.Request, span *span.Span, title string) (bean.SegRes, error) {
	// 去除标题中的后缀信息
	title = processTitleSuffix(title)
	return segmentationProcess(req, span, title)
}
