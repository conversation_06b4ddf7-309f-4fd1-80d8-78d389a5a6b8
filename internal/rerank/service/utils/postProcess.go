package utils

import (
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/consts"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
)

// 医疗termweight版本后处理

func PostProcess1(span *span.Span, model string, rerankList []*map[string]interface{}) int {
	opSpan := span.AddSpan("后处理一")
	defer opSpan.Finish()
	topFive := len(rerankList)
	if topFive > 5 {
		topFive = 5
	}
	var threshold float64 = 0.0
	if model == consts.Medical_V0620 {
		threshold = 0.2
	}

	var num int
	if model == consts.Medical_V0620 || len(rerankList) > 20 {
		num = 10
	} else {
		num = 5
	}
	if topFive-1 >= 0 && topFive-1 < len(rerankList) {
		if common.Interface2F64((*rerankList[topFive-1])["words_score"]) == 0 {
			topTag := true
			for i := 0; i < topFive; i++ {
				if common.Interface2F64((*rerankList[i])["words_score"]) > threshold && int(common.Interface2I64((*rerankList[i])["_rank_index"])) < num {
					topTag = false
				}
			}

			if topTag {
				for i := range rerankList {
					if common.Interface2F64((*rerankList[i])["words_score"]) != -1 {
						(*rerankList[i])["words_score"] = 0.0
					}
				}
			}

			for i := range rerankList {
				(*rerankList[i])["tag"] = false
			}
		}
	}

	return topFive
}

// PostProcess2 合并后的后处理二函数
func PostProcess2(span *span.Span, model string, rerankList []*map[string]interface{}, topFive int) {
	// 后处理二
	opSpan := span.AddSpan("后处理二")
	defer opSpan.Finish()

	// 根据模型设置差异阈值
	differenceThreshold := 5
	if model == consts.Medical_V0620 {
		differenceThreshold = 4
	}

	difference := 0
	for i := 0; i < topFive; i++ {
		if common.Interface2I64((*rerankList[i])["_rank_index"]) > 5 {
			difference++
		}

		// Medical_V0620 特殊逻辑
		if model == consts.Medical_V0620 && difference == 4 && common.Interface2F64((*rerankList[i])["words_score"]) < 0.8 {
			updateWordsScoreAndTag(rerankList)
			break
		}
	}

	// 通用逻辑
	if difference == differenceThreshold && model != consts.Medical_V0620 {
		for i := range rerankList {
			wordsScore := common.Interface2F64((*rerankList[i])["words_score"])
			if wordsScore < 0.8 && wordsScore != -1 {
				(*rerankList[i])["words_score"] = 0.0
			}
			(*rerankList[i])["tag"] = false
		}
	}
}

// updateWordsScoreAndTag 通用更新 words_score 和 tag 的函数
func updateWordsScoreAndTag(rerankList []*map[string]interface{}) {
	for j := range rerankList {
		if common.Interface2F64((*rerankList[j])["words_score"]) != -1 {
			(*rerankList[j])["words_score"] = 0
		}
		(*rerankList[j])["tag"] = false
	}
}

func PostProcess3(span *span.Span, rerankList []*map[string]interface{}, topFive int) {
	// 后处理三
	opSpan := span.AddSpan("后处理三")
	defer opSpan.Finish()
	oneTag := true
	topTen := len(rerankList)
	if topTen > 10 {
		topTen = 10
	}
	for i := 0; i < topTen; i++ {
		if int(common.Interface2I64((*rerankList[i])["_rank_index"])) == 1 {
			oneTag = false
		}
	}
	if oneTag && topFive < len(rerankList) && common.Interface2F64((*rerankList[topFive])["words_score"]) < 0.8 {
		for i := range rerankList {
			if common.Interface2F64((*rerankList[i])["words_score"]) < 0.8 {
				if common.Interface2F64((*rerankList[i])["words_score"]) != -1 {
					(*rerankList[i])["words_score"] = 0.0
				}
				(*rerankList[i])["tag"] = false
			}
		}
	}
}
