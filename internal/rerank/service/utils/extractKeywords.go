package utils

import (
	"strings"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/config"
)

// ExtractKeywords 从标题中提取在内容中出现次数最多的关键词
func ExtractKeywords(title, content string) []string {
	// 对标题进行分词
	words := config.Seg.Cut(title, true)

	// 统计每个词在内容中的出现次数
	wordCount := make(map[string]int)
	for _, word := range words {
		// 过滤中文单字(长度为3)、空格、屏蔽词
		if len(word) <= 3 || strings.TrimSpace(word) == "" || config.NewsStopwords[word] {
			continue
		}
		wordCount[word] = strings.Count(content, word)
	}

	// 找出最大出现次数
	maxCount := 0
	for _, count := range wordCount {
		if count > maxCount {
			maxCount = count
		}
	}

	// 如果最大次数为0，表示没有匹配的词，返回空列表
	if maxCount == 0 {
		return []string{}
	}

	// 收集出现次数最多的词
	var result []string
	for word, count := range wordCount {
		if count == maxCount {
			result = append(result, word)
		}
	}

	return result
}
