package utils

import (
	"math"
	"strings"
	"unicode"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/common"
)

// MergeWordsWithTag 合并带标签的词
func MergeWordsWithTag(words []string, scores []float64, tags []bool, threshold float64) ([]string, []float64, []bool) {
	var mergedWords []string
	var mergedScores []float64
	var mergedTags []bool

	var currentWords []string
	var currentScore float64
	var currentTag bool

	for i := 0; i < len(words); i++ {
		word := words[i]
		score := scores[i]
		tag := tags[i]

		if score > threshold && !tag { // 分数大于阈值且标签为 false
			currentWords = append(currentWords, word)
			currentScore += score
		} else {
			if len(currentWords) > 0 {
				// 如果有合并的单词，存储并重置
				mergedWords = append(mergedWords, strings.Join(currentWords, ""))
				mergedScores = append(mergedScores, currentScore)
				mergedTags = append(mergedTags, currentTag)
				currentWords = nil
				currentScore = 0
				currentTag = false
			}
			// 存储未合并的单词
			mergedWords = append(mergedWords, word)
			mergedScores = append(mergedScores, score)
			mergedTags = append(mergedTags, tag)
		}
	}

	// 合并最后剩下的单词
	if len(currentWords) > 0 {
		mergedWords = append(mergedWords, strings.Join(currentWords, ""))
		mergedScores = append(mergedScores, currentScore)
		mergedTags = append(mergedTags, currentTag)
	}

	return mergedWords, mergedScores, mergedTags
}

// MatchingIntersection 计算子字符串在字符串中的交集长度
func MatchingIntersection(substring, str string) int {
	set := make(map[rune]bool)
	for _, char := range substring {
		set[char] = true
	}

	intersectionCount := 0
	for _, char := range str {
		if set[char] {
			intersectionCount++
		}
	}

	return intersectionCount
}

// 计算查询词和标题之间的匹配得分
func MatchWordsScore0620(query string, title string, content string, queryKeysOrigin []string, queryValuesOrigin []float64, titleKeys []string, titleValues []float64) (float64, [][]string, [][]string, [][]string, error) {
	queryKeys := make([]string, len(queryKeysOrigin))
	copy(queryKeys, queryKeysOrigin)
	queryValues := make([]float64, len(queryValuesOrigin))
	copy(queryValues, queryValuesOrigin)

	var wordsScore float64

	var matchDict [][]string    // 存储匹配词, 后续输出自测用, 部署时候可以删除相关字段和代码, 不影响排序结果, ,或者加入日志可供出问题时快速找出哪项匹配词出的问题
	var unmatchDict [][]string  // 存储未匹配词, 部署时候可以删除相关字段和代码, 不影响排序结果
	var unmatchDict_ [][]string // 存储非扣分词（低权重非扣分词或未匹配但非扣分词）, 部署时候可以删除相关字段和代码, 不影响排序结果

	// 检查查询字符串是否包含数字
	ContainsDigit := false
	for _, char := range query {
		if unicode.IsDigit(char) {
			ContainsDigit = true
			break
		}
	}

	// 如果查询中含有数字，直接返回0得分
	if ContainsDigit { // 因为含数字 query 暂未设计处理方式
		wordsScore = 0
	} else {

		queryLen := len(queryKeys)
		titleLen := len(titleKeys)

		// 用来标记匹配的数组
		queryTag := make([]bool, queryLen)
		titleTag := make([]bool, titleLen)

		for i := 0; i < titleLen; i++ {
			for j := 0; j < queryLen; j++ {
				// 如果两个关键词都未匹配过且长度相同
				if !titleTag[i] && !queryTag[j] && len([]rune(titleKeys[i])) == len([]rune(queryKeys[j])) {

					if titleKeys[i] == queryKeys[j] {
						// 直接匹配
						if titleValues[i] < queryValues[j] {
							wordsScore += titleValues[i]
						} else {
							wordsScore += queryValues[j]
						}
						titleTag[i] = true
						queryTag[j] = true
						matchDict = append(matchDict, []string{titleKeys[i]})

					} else if len([]rune(titleKeys[i])) > 1 && ComparePinyin(titleKeys[i], queryKeys[j]) {
						// 拼音匹配
						if titleValues[i] < queryValues[j] {
							wordsScore += titleValues[i]
						} else {
							wordsScore += queryValues[j]
						}
						titleTag[i] = true
						queryTag[j] = true
						matchDict = append(matchDict, []string{titleKeys[i], queryKeys[j]})
					}
				}
				if titleTag[i] {
					break
				}
			}
		}

		for i := 0; i < titleLen; i++ {
			for j := 0; j < queryLen; j++ {
				if !titleTag[i] && !queryTag[j] && CheckSynonyms(titleKeys[i], queryKeys[j]) {
					// 同义词匹配
					if titleValues[i] < queryValues[j] {
						wordsScore += titleValues[i]
					} else {
						wordsScore += queryValues[j]
					}
					titleTag[i] = true
					queryTag[j] = true
					matchDict = append(matchDict, []string{titleKeys[i], queryKeys[j]})
				}
			}
		}

		// fmt.Println(wordsScore)

		for i := 0; i < titleLen; i++ {
			for j := 0; j < queryLen; j++ {
				if !titleTag[i] && !queryTag[j] && CheckContrast(titleKeys[i], queryKeys[j]) {
					// 差别词寻找
					if titleValues[i] < queryValues[j] {
						wordsScore -= titleValues[i]
					} else {
						wordsScore -= queryValues[j]
					}
					titleTag[i] = true
					queryTag[j] = true
					unmatchDict = append(unmatchDict, []string{titleKeys[i], queryKeys[j]})
				}
			}
		}

		// fmt.Println(wordsScore)

		queryTagTemp := make([]bool, len(queryTag))
		copy(queryTagTemp, queryTag)

		titleTagTemp := make([]bool, len(titleTag))
		copy(titleTagTemp, titleTag)

		i := 0
		for i < titleLen {
			j := 0
			for j < queryLen {

				if !titleTag[i] && !queryTag[j] {

					if strings.Contains(queryKeys[j], titleKeys[i]) || strings.Contains(titleKeys[i], queryKeys[j]) {

						if CheckSynonyms(titleKeys[i], queryKeys[j]) {
							// 同义词匹配
							if titleValues[i] < queryValues[j] {
								wordsScore += titleValues[i]
							} else {
								wordsScore += queryValues[j]
							}
							titleTag[i] = true
							queryTag[j] = true
							matchDict = append(matchDict, []string{titleKeys[i], queryKeys[j]})
						} else {

							var smallerLen, largerLen int
							if len(titleKeys[i]) < len(queryKeys[j]) {
								smallerLen = len([]rune(titleKeys[i]))
								largerLen = len([]rune(queryKeys[j]))
							} else {
								smallerLen = len([]rune(queryKeys[j]))
								largerLen = len([]rune(titleKeys[i]))
							}
							substringRatio := float64(smallerLen) / float64(largerLen)

							var smallerValue float64
							if titleValues[i] < queryValues[j] {
								smallerValue = titleValues[i]
							} else {
								smallerValue = queryValues[j]
							}
							wordsScore += smallerValue * substringRatio

							if len([]rune(titleKeys[i])) < len([]rune(queryKeys[j])) {
								// 子串匹配一
								titleTag[i] = true
								queryTagTemp[j] = true
								matchDict = append(matchDict, []string{titleKeys[i], queryKeys[j]})
								queryValues[j] *= (1 - float64(len([]rune(titleKeys[i])))/float64(len([]rune(queryKeys[j]))))
								queryKeys[j] = strings.Replace(queryKeys[j], titleKeys[i], "", 1)
								i = 0
							} else if len([]rune(titleKeys[i])) > len([]rune(queryKeys[j])) {
								// 子串匹配二
								queryTag[j] = true
								titleTagTemp[i] = true
								matchDict = append(matchDict, []string{titleKeys[i], queryKeys[j]})
								titleValues[i] *= (1 - float64(len([]rune(queryKeys[j])))/float64(len([]rune(titleKeys[i]))))
								titleKeys[i] = strings.Replace(titleKeys[i], queryKeys[j], "", 1)
								j = 0
							} else {
								// 子串匹配三
								titleTag[i] = true
								queryTag[j] = true
								matchDict = append(matchDict, []string{titleKeys[i], queryKeys[j]})
								break
							}
						}
					} else {
						j++
					}
				} else {
					j++
				}
			}
			i++
		}

		for i := 0; i < titleLen; i++ {
			for j := 0; j < titleLen; j++ {
				if titleTag[i] && titleTagTemp[i] && !titleTag[j] && !titleTagTemp[j] && (titleKeys[i] == titleKeys[j] || CheckSynonyms(titleKeys[i], titleKeys[j])) {
					// 重复出现
					if titleValues[i] < titleValues[j] {
						wordsScore += (titleValues[i] / 2)
					} else {
						wordsScore += (titleValues[j] / 2)
					}
					titleTag[j] = true
				}
			}
		}

		for i := 0; i < queryLen; i++ {
			for j := 0; j < queryLen; j++ {
				if queryTag[i] && queryTagTemp[i] && !queryTag[j] && !queryTagTemp[j] && (queryKeys[i] == queryKeys[j] || CheckSynonyms(queryKeys[i], queryKeys[j])) {
					// 重复出现
					if queryValues[i] < queryValues[j] {
						wordsScore += (queryValues[i] / 2)
					} else {
						wordsScore += (queryValues[j] / 2)
					}
					queryTag[j] = true
				}
			}
		}

		// fmt.Println(wordsScore)

		for num := 0; num < 2; num++ {
			for i := 0; i < len(titleKeys); i++ {
				for j := 0; j < len(queryKeys); j++ {

					if !titleTag[i] && !queryTag[j] {
						intersection := MatchingIntersection(queryKeys[j], titleKeys[i])

						var smallerLen int
						if len([]rune(queryKeys[j])) < len([]rune(titleKeys[i])) {
							smallerLen = len([]rune(queryKeys[j]))
						} else {
							smallerLen = len([]rune(titleKeys[i]))
						}
						if intersection != 0 && float64(intersection) >= float64(smallerLen)*0.6 {
							// 字串匹配一
							var largerLen int
							if len([]rune(queryKeys[j])) > len([]rune(titleKeys[i])) {
								largerLen = len([]rune(queryKeys[j]))
							} else {
								largerLen = len([]rune(titleKeys[i]))
							}
							substringRatio := float64(intersection) / float64(largerLen)

							var smallerValue float64
							if titleValues[i] < queryValues[j] {
								smallerValue = titleValues[i]
							} else {
								smallerValue = queryValues[j]
							}
							wordsScore += smallerValue * substringRatio

							queryTag[j] = true
							titleTag[i] = true
							matchDict = append(matchDict, []string{titleKeys[i], queryKeys[j]})
						}
					}
				}
			}
			if num == 0 {
				for i := 0; i < len(queryTag); i++ {
					queryTag[i] = queryTag[i] || queryTagTemp[i]
				}

				for i := 0; i < len(titleTag); i++ {
					titleTag[i] = titleTag[i] || titleTagTemp[i]
				}
				queryKeys, queryValues, queryTag = MergeWordsWithTag(queryKeys, queryValues, queryTag, 0.2)
				titleKeys, titleValues, titleTag = MergeWordsWithTag(titleKeys, titleValues, titleTag, 0.2)
				// 字串匹配二
			}
		}

		// fmt.Println(wordsScore)

		for i := 0; i < len(titleKeys); i++ {
			if !titleTag[i] && titleValues[i] < 0.05 {
				unmatchDict_ = append(unmatchDict_, []string{titleKeys[i]})
			}
		}

		for i := 0; i < len(queryKeys); i++ {
			if !queryTag[i] && queryValues[i] < 0.05 {
				unmatchDict_ = append(unmatchDict_, []string{queryKeys[i]})
			}
		}

		// term_weight 权重 > 0.05 而未匹配 ——> 扣分
		// 处理 query
		var queryIndexes []int
		for i, num := range queryValues {
			if num >= 0.05 {
				queryIndexes = append(queryIndexes, i)
			}
		}
		if len(queryIndexes) > 0 {
			for _, index := range queryIndexes {
				if !queryTag[index] && !strings.Contains(title, queryKeys[index]) && !CheckCommon(queryKeys[index]) && !strings.Contains(content, queryKeys[index]) {
					wordsScore -= queryValues[index]
					unmatchDict = append(unmatchDict, []string{queryKeys[index]})
				} else if !queryTag[index] && !strings.Contains(title, queryKeys[index]) && !CheckCommon(queryKeys[index]) {
					wordsScore -= (queryValues[index] / 2)
					unmatchDict = append(unmatchDict, []string{queryKeys[index]})
				} else if !queryTag[index] && CheckCommon(queryKeys[index]) {
					unmatchDict_ = append(unmatchDict_, []string{queryKeys[index]})
				}
			}
		}
		// 处理 title
		var titleIndexes []int
		for i, num := range titleValues {
			if num >= 0.05 {
				titleIndexes = append(titleIndexes, i)
			}
		}
		if len(titleIndexes) > 0 {
			for _, index := range titleIndexes {
				if !titleTag[index] && !strings.Contains(query, titleKeys[index]) && !CheckCommon(titleKeys[index]) {
					wordsScore -= titleValues[index]
					unmatchDict = append(unmatchDict, []string{titleKeys[index]})
				} else if !titleTag[index] && CheckCommon(titleKeys[index]) {
					unmatchDict_ = append(unmatchDict_, []string{titleKeys[index]})
				}
			}
		}
		// 扣分最低不能低于0
		if wordsScore < 0 {
			wordsScore = 0
		}

		// term_weight 权重最大值 而未匹配（仅限 query 的 term_weight 权重最大值） ——> 置零
		// 找到最大值
		maxValue := queryValues[0]
		for _, num := range queryValues {
			if num > maxValue {
				maxValue = num
			}
		}
		// 找出符合条件的索引
		var queryMaxIndexes []int
		for i, num := range queryValues {
			if math.Abs(num-maxValue) <= maxValue*0.2 {
				queryMaxIndexes = append(queryMaxIndexes, i)
			}
		}
		// 得分最高不能高于0
		for _, index := range queryMaxIndexes {
			if !queryTag[index] {
				if MatchingIntersection(queryKeys[index], title) <= len([]rune(queryKeys[index]))/2 {
					if wordsScore > 0 {
						wordsScore = 0
					}
				}
			}
		}
	}

	return wordsScore, matchDict, unmatchDict, unmatchDict_, nil
}

// WordsScore 计算查询词和标题之间的匹配得分
func MatchWordsScore0310(query string, title string, queryKeysOrigin []string, queryValues []float64, titleKeys []string, titleValues []float64) (float64, error) {
	// 如果查询中含有数字，直接返回0得分
	if strings.ContainsAny(query, "0123456789") {
		return 0, nil
	}

	queryKeys := make([]string, len(queryKeysOrigin))
	copy(queryKeys, queryKeysOrigin)

	queryLen := len(queryKeys)
	titleLen := len(titleKeys)

	// 用来标记匹配的数组
	queryTag := make([]bool, queryLen)
	titleTag := make([]bool, titleLen)

	var wordsScore float64
	// 遍历标题和查询的关键词
	for i, titleKey := range titleKeys {
		for j, queryKey := range queryKeys {
			// 如果两个关键词都未匹配过且长度相同
			if !titleTag[i] && !queryTag[j] && len(titleKey) == len(queryKey) {
				// 直接匹配
				if titleKey == queryKey {
					wordsScore += math.Min(titleValues[i], queryValues[j])
					titleTag[i] = true
					queryTag[j] = true
				} else if len(titleKey) > 3 && ComparePinyin(titleKey, queryKey) {
					// 拼音匹配
					wordsScore += math.Min(titleValues[i], queryValues[j])
					titleTag[i] = true
					queryTag[j] = true
				}
			}
			if titleTag[i] {
				break
			}
		}
	}

	// 处理长度为3的查询词，降低其权重
	for j, queryKey := range queryKeys {
		if len(queryKey) == 3 && !queryTag[j] {
			queryValues[j] /= 2
		}
	}

	// 处理权重大于 0.05 且未匹配的词
	if wordsScore != 0 {
		queryIndexes := getIndexes(queryValues, 0.1)
		for _, index := range queryIndexes {
			if !queryTag[index] && !strings.Contains(title, queryKeys[index]) {
				wordsScore -= queryValues[index]
			}
		}

		titleIndexes := getIndexes(titleValues, 0.1)
		for _, index := range titleIndexes {
			if !titleTag[index] && !strings.Contains(query, titleKeys[index]) {
				wordsScore -= titleValues[index]
			}
		}
	}

	// 确保分数不会低于 0
	if wordsScore < 0 {
		wordsScore = 0
	}

	// 处理查询中最大权重的词如果未匹配
	queryMaxIndexes, err := getMaxWeightIndexes(queryValues)
	if err != nil {
		return wordsScore, err
	}
	for _, index := range queryMaxIndexes {
		if !queryTag[index] {
			if MatchingIntersection(queryKeys[index], title) <= len(queryKeys[index])/2 {
				wordsScore = 0
			}
		}
	}

	// 处理权重大于 0.1 的词
	queryIndexes := getIndexes(queryValues, 0.1)
	titleIndexes := getIndexes(titleValues, 0.1)

	tempQuery := strings.Builder{}
	for _, index := range queryIndexes {
		tempQuery.WriteString(queryKeys[index])
	}

	tempTitle := strings.Builder{}
	for _, index := range titleIndexes {
		tempTitle.WriteString(titleKeys[index])
	}

	wordsScoreOrigin := wordsScore

	// 检查查询词是否匹配
	for _, index := range queryIndexes {
		if queryTag[index] { // Term with weight > 0.1 not matched
			wordsScore = wordsScoreOrigin
			break
		}
		if MatchingIntersection(tempQuery.String(), title) <= len(tempQuery.String())/2 { // Not matched enough
			wordsScore = -1
		}
	}

	if wordsScore == -1 {
		for _, index := range titleIndexes {
			if titleTag[index] { // Term with weight > 0.1 not matched
				wordsScore = wordsScoreOrigin
				break
			}
			if MatchingIntersection(tempTitle.String(), query) <= len(tempTitle.String())/2 { // Not matched enough
				wordsScore = -1
			}
		}
	}

	return wordsScore, nil
}

// getIndexes 获取值大于等于阈值的索引
func getIndexes(values []float64, threshold float64) []int {
	var indexes []int
	for i, value := range values {
		if value >= threshold {
			indexes = append(indexes, i)
		}
	}
	return indexes
}

// getMaxWeightIndexes 获取最大权重的索引
func getMaxWeightIndexes(values []float64) ([]int, error) {
	var indexes []int
	maxValue, err := common.MaxValues(values)
	if err != nil {
		return indexes, err
	}
	for i, value := range values {
		if math.Abs(value-maxValue) <= maxValue*0.20 {
			indexes = append(indexes, i)
		}
	}
	return indexes, nil
}
