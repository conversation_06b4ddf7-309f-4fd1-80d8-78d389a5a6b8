# 贡献指南

感谢您对重排服务项目的关注！本文档为贡献者提供详细的指导和信息。

## 🤝 如何贡献

### 报告错误

在创建错误报告之前，请检查现有问题以避免重复。创建错误报告时，请包含：

- **清晰的标题和描述**
- **重现问题的步骤**
- **预期行为与实际行为**
- **环境详情**（操作系统、Go版本等）
- **相关日志或错误信息**
- **最小代码示例**（如适用）

### 建议功能

我们欢迎功能请求！请提供：

- **功能的清晰描述**
- **用例和动机**
- **建议的实现方案**（如果您有想法）
- **对现有功能的潜在影响**

### 代码贡献

1. **Fork 仓库**
2. **创建功能分支**: `git checkout -b feature/amazing-feature`
3. **进行更改**
4. **为新功能添加测试**
5. **确保测试通过**: `go test ./...`
6. **根据需要更新文档**
7. **提交清晰的提交信息**
8. **推送到您的 Fork**
9. **创建 Pull Request**

## 📝 开发指南

### 代码风格

- 遵循 [Go 代码审查评论](https://github.com/golang/go/wiki/CodeReviewComments)
- 使用 `gofmt` 进行格式化
- 使用 `golint` 和 `go vet` 进行代码质量检查
- 编写清晰、自文档化的代码
- 为复杂逻辑添加注释

### 测试

- 为所有新函数编写单元测试
- 保持测试覆盖率在 80% 以上
- 为 API 端点包含集成测试
- 为性能关键代码添加基准测试
- 适当时使用表驱动测试

```go
func TestRerankFunction(t *testing.T) {
    tests := []struct {
        name     string
        input    Input
        expected Output
        wantErr  bool
    }{
        {
            name:     "有效输入",
            input:    Input{Query: "测试"},
            expected: Output{Score: 0.9},
            wantErr:  false,
        },
        // 添加更多测试用例
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            result, err := RerankFunction(tt.input)
            if (err != nil) != tt.wantErr {
                t.Errorf("RerankFunction() error = %v, wantErr %v", err, tt.wantErr)
                return
            }
            if !reflect.DeepEqual(result, tt.expected) {
                t.Errorf("RerankFunction() = %v, want %v", result, tt.expected)
            }
        })
    }
}
```

### 文档

- 为面向用户的更改更新 README.md
- 为公共函数添加 godoc 注释
- 为端点更改更新 API 文档
- 在文档中包含示例

### 提交信息

使用约定式提交格式：

```
type(scope): description

[optional body]

[optional footer]
```

类型：
- `feat`: 新功能
- `fix`: 错误修复
- `docs`: 文档更改
- `style`: 代码风格更改
- `refactor`: 代码重构
- `test`: 添加或更新测试
- `chore`: 维护任务

示例：
```
feat(medical): 添加新的医学实体识别
fix(sports): 解决时间衰减计算错误
docs(api): 更新重排端点文档
```

## 🏗️ 项目结构

```
rerank-service/
├── cmd/                    # 应用程序入口点
├── internal/               # 私有应用程序代码
│   ├── rerank/            # 重排服务实现
│   │   ├── api.go         # API 处理器
│   │   ├── service/       # 业务逻辑
│   │   ├── config/        # 配置管理
│   │   └── bean/          # 数据模型
├── pkg/                   # 公共库代码
├── test/                  # 测试文件
├── config/                # 配置文件
├── cicd/                  # CI/CD 配置
├── docs/                  # 文档
└── scripts/               # 构建和部署脚本
```

## 🧪 测试指南

### 运行测试

```bash
# 运行所有测试
go test ./...

# 运行带覆盖率的测试
go test -cover ./...

# 运行特定包测试
go test ./internal/rerank/...

# 运行基准测试
go test -bench=. ./...

# 运行带竞态检测的测试
go test -race ./...
```

### 测试分类

1. **单元测试**: 测试单个函数和方法
2. **集成测试**: 测试组件交互
3. **API 测试**: 测试 HTTP 端点
4. **基准测试**: 测试性能特征

### Mock 使用

为依赖项使用接口以启用模拟：

```go
type TokenizerInterface interface {
    Tokenize(text string) ([]string, error)
}

type RerankService struct {
    tokenizer TokenizerInterface
}

// 在测试中，使用模拟实现
type MockTokenizer struct{}

func (m *MockTokenizer) Tokenize(text string) ([]string, error) {
    return []string{"模拟", "词元"}, nil
}
```

## 🔧 开发环境设置

### 先决条件

- Go 1.23.3 或更高版本
- Docker（用于集成测试）
- Make（可选，用于构建脚本）

### 本地开发

```bash
# 克隆仓库
git clone https://github.com/your-org/rerank-service.git
cd rerank-service

# 安装依赖
go mod download

# 运行测试
go test ./...

# 构建服务
go build -o rerank-server cmd/main.go

# 本地运行
./rerank-server server -c ./config/rerank
```

### IDE 设置

#### VS Code
推荐扩展：
- Go 扩展
- Go Test Explorer
- GitLens

#### GoLand
- 启用 Go 模块支持
- 配置代码风格以匹配项目标准

## 📋 Pull Request 流程

1. **更新文档** 对于任何面向用户的更改
2. **添加测试** 对于新功能
3. **确保 CI 通过** 所有检查
4. **请求审查** 来自维护者
5. **及时处理反馈**
6. **如果要求，压缩提交**

### PR 检查清单

- [ ] 测试已添加/更新并通过
- [ ] 文档已更新
- [ ] 代码遵循风格指南
- [ ] 无破坏性更改（或已清楚记录）
- [ ] 已考虑性能影响
- [ ] 已审查安全影响

## 🏷️ 发布流程

发布遵循语义版本控制（SemVer）：

- **MAJOR**: 破坏性更改
- **MINOR**: 新功能（向后兼容）
- **PATCH**: 错误修复（向后兼容）

### 发布步骤

1. 更新相关文件中的版本
2. 更新 CHANGELOG.md
3. 创建发布分支
4. 标记发布
5. 创建带有说明的 GitHub 发布

## 🛡️ 安全

### 报告安全问题

请将安全漏洞报告给 [<EMAIL>](mailto:<EMAIL>)，而不是创建公共问题。

### 安全指南

- 永远不要提交机密或凭据
- 使用安全编码实践
- 验证所有输入
- 遵循 OWASP 指南
- 保持依赖项更新

## 📞 获取帮助

- **文档**: 查看 [Wiki](https://github.com/your-org/rerank-service/wiki)
- **问题**: 搜索现有的 [GitHub Issues](https://github.com/your-org/rerank-service/issues)
- **讨论**: 使用 [GitHub Discussions](https://github.com/your-org/rerank-service/discussions)
- **QQ群**: 加入我们的 [QQ群](https://qm.qq.com/rerank-service)

## 🙏 认可

贡献者将在以下地方得到认可：
- README.md 贡献者部分
- 发布说明
- 年度贡献者亮点

感谢您为重排服务做出贡献！🎉
