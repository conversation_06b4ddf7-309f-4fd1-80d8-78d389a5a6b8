# 🔄 智能重排服务 (Rerank Service)

[![Go 版本](https://img.shields.io/badge/Go-1.23.3-blue.svg)](https://golang.org/)
[![许可证](https://img.shields.io/badge/License-Apache%202.0-green.svg)](LICENSE)
[![构建状态](https://img.shields.io/badge/Build-Passing-brightgreen.svg)]()
[![测试覆盖率](https://img.shields.io/badge/Coverage-85%25-yellow.svg)]()

> 🚀 基于 Go 语言构建的高性能、领域专用搜索结果智能重排服务，支持13种重排策略

## ✨ 核心特性

- 🎯 **13种重排策略** - 医疗5版本、体育4策略、新闻2版本、通用1版本、历史1版本
- ⚡ **高性能处理** - 支持并发处理，QPS 可达 1000+
- 🧠 **智能评分** - 多维度特征融合，包括文本匹配、时效性、权威性等
- 🔧 **灵活配置** - 丰富的参数调优选项，适应不同业务场景
- 📊 **完善监控** - 全面的指标监控和可观测性支持
- 🐳 **云原生** - 支持 Docker 容器化和 Kubernetes 部署

## 📋 完整重排策略列表

### 🏥 医疗重排策略 (5种)

| 模型标识 | 常量名称 | 调用方法 | 版本特性 | 适用场景 |
|----------|----------|----------|----------|----------|
| `5001_V20250516` | Medical_V0516 | Rank0516 | 产品策略排序、超短文本处理 | 生产环境主推 |
| `5001_V20250310` | Medical_V0310 | Rank0310 | 标准医疗重排、TitleSeg分词 | 平衡性能效果 |
| `5001_V20250620` | Medical_V0620 | Rank0310 | 优化版本、限制16条处理 | 高频查询优化 |
| `5001_V20251218` | Medical_V1218 | Rank1218 | 时效性增强、TimeStrengthReg | 时间敏感查询 |
| `1/5001/5001_V1120/5001_V20251120` | MEDICAL/HEALTH/V1120/Medical_V1120 | Rank1120 | 经典算法、稳定可靠 | 兜底策略 |

### ⚽ 体育重排策略 (4种)

| 意图/时效性 | 策略描述 | 算法实现 | 适用场景 |
|-------------|----------|----------|----------|
| HOTTOPIC | 热点话题重排 | newsRerank | 体育热点、话题讨论 |
| 强时效性 (0) | 实时赛事重排 | newsRerank | 比赛直播、即时比分 |
| 中时效性 (1) | 赛事回顾重排 | mediumScore | 赛事分析、球队动态 |
| 弱时效性 (2) | 历史数据重排 | weakScore | 球员资料、历史记录 |

### 📰 新闻重排策略 (2种)

| 模型标识 | 版本特性 | 核心功能 | 适用场景 |
|----------|----------|----------|----------|
| `news_V20250421` | 基础新闻重排 | 时间匹配优化、精确时间处理 | 标准新闻查询 |
| `news_V20250522` | 多样性新闻重排 | 主题去重、避免内容聚集 | 多样性要求高 |

### 🌐 其他重排策略 (2种)

| 策略 | 模型标识 | 特性 | 适用场景 |
|------|----------|------|----------|
| 通用重排 | `common` | 基础文本匹配、轻量级算法 | 兜底策略、未匹配领域 |
| 历史重排 | `5007` | 历史内容重排 | 历史相关查询 |

## 🎯 算法特性矩阵

| 特性功能 | V0516 | V0310 | V0620 | V1218 | V1120 | 体育 | 新闻 | 通用 | 历史 |
|----------|-------|-------|-------|-------|-------|------|------|------|------|
| **产品策略排序** | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **超短文本处理** | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **库优先级排序** | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **医学实体识别** | ✅ | ✅ | ✅ | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| **时效性检测** | ❌ | ❌ | ❌ | ✅ | ✅ | ✅ | ✅ | ❌ | ❌ |
| **分词优化** | ❌ | TitleSeg | TitleSeg0620 | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **处理数量限制** | 无 | 无 | 16条 | 无 | 无 | 无 | 无 | 无 | 无 |
| **意图识别** | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ | ❌ |
| **时效性分级** | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ | ❌ |
| **时间匹配** | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ | ❌ | ❌ |
| **多样性排序** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ |
| **主题去重** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ |
| **突发新闻检测** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ |

## 🚀 快速开始

### 环境要求

- Go 1.23.3 或更高版本
- Docker（可选）

### 安装部署

```bash
# 克隆仓库
git clone https://github.com/your-org/rerank-service.git
cd rerank-service

# 安装依赖
go mod tidy

# 编译服务
go build -tags=rerank -o rerank-server

# 启动服务
./rerank-server server -c ./config/rerank
```

### Docker 部署

```bash
# 构建镜像
docker build -f cicd/rerank.dockerfile -t rerank-service .

# 运行容器
docker run -p 50903:50903 -v ./config:/app/config rerank-service
```

## 📖 API 使用示例

### 医疗重排示例

```json
{
  "header": {"traceId": "medical-001"},
  "payload": {
    "model": "5001_V20250516",
    "topK": 10,
    "rankCollections": ["医疗权威库", "专业医学库"],
    "data": [{
      "query": "糖尿病症状治疗",
      "docs": [{"id": "1", "title": "糖尿病的早期症状", "_rank_score": 0.95}]
    }]
  }
}
```

### 体育重排示例

```json
{
  "header": {"traceId": "sports-001"},
  "payload": {
    "model": "5010",
    "intent": "NEWS",
    "timeliness": "0",
    "data": [{
      "query": "世界杯决赛结果",
      "docs": [{"id": "1", "title": "世界杯决赛精彩回顾", "_rank_score": 0.92}]
    }]
  }
}
```

### 新闻重排示例

```json
{
  "header": {"traceId": "news-001"},
  "payload": {
    "model": "news_V20250522",
    "data": [{
      "query": "2024年科技新闻",
      "docs": [{"id": "1", "title": "AI技术突破性进展", "_rank_score": 0.88}]
    }]
  }
}
```

## 🔧 配置说明

### 重排策略配置

```toml
# config/rerank/rerank.toml

# 医疗重排配置
[medical]
enable_entity_recognition = true
authority_weight = 0.4
recency_weight = 0.3
enable_product_strategy = true  # V0516专用

# 体育重排配置
[sports]
enable_live_events = true
time_decay_factor = 0.1
diversity_threshold = 0.8
intent_detection = true

# 新闻重排配置
[news]
enable_breaking_news = true
source_credibility_weight = 0.5
diversity_window = 5
enable_topic_dedup = true  # V20250522专用

# 通用重排配置
[common]
basic_text_matching = true
simple_authority_scoring = true
```

## 📊 性能基准

| 重排策略 | 平均延迟 | QPS | 内存使用 | 准确性(NDCG@10) |
|----------|----------|-----|----------|-----------------|
| 医疗V0516 | 85ms | 1200+ | 1.8GB | 0.87 |
| 医疗V0310 | 75ms | 1400+ | 1.5GB | 0.85 |
| 医疗V0620 | 45ms | 2200+ | 1.2GB | 0.83 |
| 体育重排 | 70ms | 1500+ | 1.4GB | 0.82 |
| 新闻重排 | 90ms | 1100+ | 1.6GB | 0.88 |
| 通用重排 | 35ms | 2800+ | 0.8GB | 0.75 |

## 🏗️ 系统架构

### 重排策略路由架构

```mermaid
graph TB
    subgraph "请求入口"
        A[HTTP 请求] --> B[RerankService]
        B --> C[performRanking]
    end

    subgraph "策略路由层"
        C --> D{switch domainID}

        D -->|MEDICAL/V1120/Medical_V1120/HEALTH| E1[medicalRerank.Rank1120]
        D -->|V1218/Medical_V1218| E2[medicalRerank.Rank1218]
        D -->|Medical_V0310/Medical_V0620| E3[medicalRerank.Rank0310]
        D -->|Medical_V0516| E4[medicalRerank.Rank0516]
        D -->|SPORTS| E5[sportsRerank.Rank]
        D -->|News_V20250421/News_V20250522| E6[newsRerank.Rank20250421]
        D -->|HISTORY| E7[historyRerank.Rank]
        D -->|default| E8[commonRerank.Rank]
    end

    subgraph "医疗重排算法"
        E1 --> F1[V1120: 基础医疗重排<br/>时效性检测+医学实体识别]
        E2 --> F2[V1218: 增强医疗重排<br/>优化时效性处理]
        E3 --> F3[V0310/V0620: 标准医疗重排<br/>分词优化+质量评分]
        E4 --> F4[V0516: 最新医疗重排<br/>产品策略+超短文本处理]
    end

    subgraph "体育重排算法"
        E5 --> G1{Intent判断}
        G1 -->|HOTTOPIC| G2[热点话题: newsRerank算法]
        G1 -->|NEWS/其他| G3{TimeLiness判断}
        G3 -->|0强时效| G4[实时赛事: newsRerank算法]
        G3 -->|1中时效| G5[赛事回顾: mediumScore算法]
        G3 -->|2弱时效| G6[历史数据: weakScore算法]
    end

    subgraph "新闻重排算法"
        E6 --> H1{版本判断}
        H1 -->|V20250421| H2[基础新闻重排<br/>时间匹配+范围提取]
        H1 -->|V20250522| H3[多样性新闻重排<br/>主题去重+交集检测]
    end

    subgraph "其他重排算法"
        E7 --> I1[历史重排算法]
        E8 --> I2[通用重排算法]
    end

    F1 --> J[结果返回]
    F2 --> J
    F3 --> J
    F4 --> J
    G2 --> J
    G4 --> J
    G5 --> J
    G6 --> J
    H2 --> J
    H3 --> J
    I1 --> J
    I2 --> J

    style E1 fill:#e8f5e8
    style E2 fill:#f3e5f5
    style E3 fill:#fff3e0
    style E4 fill:#e1f5fe
    style E5 fill:#fce4ec
    style E6 fill:#e0f2f1
    style E7 fill:#f1f8e9
    style E8 fill:#f5f5f5
```

### 项目结构

```
internal/rerank/
├── api.go                 # API 处理器
├── service/               # 业务逻辑
│   ├── rank/             # 重排算法实现
│   │   ├── rank_medical_*.go    # 医疗重排算法
│   │   ├── rank_sports.go       # 体育重排算法
│   │   ├── rank_news.go         # 新闻重排算法
│   │   └── rank_common.go       # 通用重排算法
│   └── rerank_service.go # 主服务逻辑
├── config/               # 配置管理
├── bean/                 # 数据模型
├── consts/               # 常量定义
└── refactor/             # 重构代码（新架构）
    ├── interfaces.go     # 接口定义
    ├── engine.go         # 重排引擎
    ├── strategies/       # 策略实现
    ├── extractors/       # 特征提取器
    ├── calculators/      # 评分计算器
    ├── components/       # 基础组件
    └── config/           # 配置管理
```

## 🧪 测试指南

### 运行测试

```bash
# 运行所有测试
go test ./...

# 运行带覆盖率的测试
go test -cover ./...

# 运行特定包测试
go test ./internal/rerank/...

# 运行基准测试
go test -bench=. ./...

# 运行带竞态检测的测试
go test -race ./...
```

### 测试分类

1. **单元测试**: 测试单个函数和方法
2. **集成测试**: 测试组件交互
3. **API 测试**: 测试 HTTP 端点
4. **基准测试**: 测试性能特征

### 测试示例

```go
func TestMedicalRerank(t *testing.T) {
    rerank := &MedicalRerank{}
    
    testCases := []struct {
        name     string
        query    string
        docs     []*Document
        expected float64
    }{
        {
            name:  "高相关性医疗查询",
            query: "糖尿病症状",
            docs: []*Document{
                {Title: "了解糖尿病症状", Authority: 8},
            },
            expected: 0.95,
        },
    }
    
    for _, tc := range testCases {
        t.Run(tc.name, func(t *testing.T) {
            result, err := rerank.Rank(context.Background(), tc.query, tc.docs)
            assert.NoError(t, err)
            assert.GreaterOrEqual(t, result[0].Score, tc.expected)
        })
    }
}
```

## 🤝 参与贡献

我们欢迎社区贡献！请查看我们的[贡献指南](CONTRIBUTING.md)了解详情。

### 开发流程

1. **Fork 仓库**
2. **创建功能分支**: `git checkout -b feature/amazing-feature`
3. **进行更改并添加测试**
4. **确保测试通过**: `go test ./...`
5. **提交代码**: `git commit -m 'Add amazing feature'`
6. **推送分支**: `git push origin feature/amazing-feature`
7. **创建 Pull Request**

### 代码规范

- 遵循 [Go 代码审查评论](https://github.com/golang/go/wiki/CodeReviewComments)
- 使用 `gofmt` 进行格式化
- 使用 `golint` 和 `go vet` 进行代码质量检查
- 编写清晰、自文档化的代码
- 为复杂逻辑添加注释
- 为新功能添加测试
- 及时更新文档

### 提交信息规范

使用约定式提交格式：

```
type(scope): description

[optional body]

[optional footer]
```

类型：
- `feat`: 新功能
- `fix`: 错误修复
- `docs`: 文档更改
- `style`: 代码风格更改
- `refactor`: 代码重构
- `test`: 添加或更新测试
- `chore`: 维护任务

示例：
```
feat(medical): 添加新的医学实体识别
fix(sports): 解决时间衰减计算错误
docs(api): 更新重排端点文档
```

## 🔍 监控运维

### 健康检查

```bash
# 服务健康状态
curl http://localhost:50903/actuator/prometheus

# API 健康检查
curl -X POST http://localhost:50903/rerank/api/v2 \
  -H "Content-Type: application/json" \
  -d '{"header":{"traceId":"health-check"},"payload":{"model":"common","data":[]}}'
```

### 关键指标

- `rerank_requests_total` - 总请求处理数
- `rerank_duration_seconds` - 请求处理时间
- `rerank_errors_total` - 按类型统计的错误数
- `rerank_concurrent_requests` - 活跃并发请求数

### Prometheus 配置

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'rerank-service'
    static_configs:
      - targets: ['rerank-service:50903']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 5s
```

### Grafana 仪表板

关键监控指标：
- **请求速率**: `rate(rerank_requests_total[5m])`
- **错误率**: `rate(rerank_errors_total[5m]) / rate(rerank_requests_total[5m])`
- **延迟 P99**: `histogram_quantile(0.99, rate(rerank_duration_seconds_bucket[5m]))`
- **内存使用**: `process_resident_memory_bytes`
- **协程数量**: `go_goroutines`

## 🚀 生产部署

### Kubernetes 部署

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rerank-service
  labels:
    app: rerank-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: rerank-service
  template:
    metadata:
      labels:
        app: rerank-service
    spec:
      containers:
      - name: rerank-service
        image: rerank-service:latest
        ports:
        - containerPort: 50903
        env:
        - name: RERANK_PORT
          value: "50903"
        - name: GOMAXPROCS
          value: "4"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/prometheus
            port: 50903
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /actuator/prometheus
            port: 50903
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: rerank-service
spec:
  selector:
    app: rerank-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 50903
  type: ClusterIP
```

### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'
services:
  rerank-service:
    build:
      context: .
      dockerfile: cicd/rerank.dockerfile
    ports:
      - "50903:50903"
    environment:
      - RERANK_PORT=50903
      - RERANK_LOG_LEVEL=info
      - GOMAXPROCS=4
    volumes:
      - ./config:/app/config:ro
      - ./resource:/app/resource:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:50903/actuator/prometheus"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
```

## 🔧 故障排查

### 常见问题

#### 内存使用过高
```bash
# 检查内存使用
docker stats rerank-service

# 分析堆内存
go tool pprof http://localhost:50903/debug/pprof/heap

# 调优垃圾回收器
export GOGC=50  # 更激进的 GC
export GOMEMLIMIT=1GiB  # 设置内存限制
```

#### 延迟过高
```bash
# 检查 CPU 性能分析
go tool pprof http://localhost:50903/debug/pprof/profile

# 监控协程
go tool pprof http://localhost:50903/debug/pprof/goroutine

# 调优工作池
# 在 config/rerank/rerank.toml 中
[[ants_pools]]
goroutine_num = 4096  # 增加工作池大小
```

#### 服务启动问题
```bash
# 检查配置
./rerank-server server -c ./config/rerank --dry-run

# 验证依赖服务
curl -f http://tokenizer-service:8080/health
curl -f http://model-server:8080/health

# 查看日志
tail -f /var/log/rerank/service.log
```

### 性能调优

#### Go 运行时优化
```bash
# 设置最优 GOMAXPROCS
export GOMAXPROCS=$(nproc)

# 调优垃圾回收器
export GOGC=100
export GOMEMLIMIT=2GiB

# 启用 CPU 性能分析
export GODEBUG=gctrace=1
```

#### 算法专用调优
```toml
# 医疗领域优化
[medical]
enable_entity_recognition = true
authority_weight = 0.4
recency_weight = 0.3

# 体育领域优化
[sports]
enable_live_events = true
time_decay_factor = 0.1
diversity_threshold = 0.8

# 新闻领域优化
[news]
enable_breaking_news = true
source_credibility_weight = 0.5
diversity_window = 5
```

## 🌟 发展路线图

### 短期目标 (2025年第一季度)
- [ ] **多语言支持** - 添加英语和其他语言支持
- [ ] **缓存层** - 实现基于 Redis 的结果缓存
- [ ] **A/B 测试框架** - 内置实验平台
- [ ] **实时模型更新** - 热交换重排模型

### 中期目标 (2025年第二、三季度)
- [ ] **机器学习流水线** - 自动化模型训练和部署
- [ ] **联邦学习** - 隐私保护的模型更新
- [ ] **基于图的重排** - 知识图谱集成
- [ ] **个性化** - 用户特定的重排偏好

### 长期目标 (2025年第四季度及以后)
- [ ] **神经重排模型** - 基于深度学习的重排
- [ ] **多模态支持** - 图像和视频内容重排
- [ ] **边缘计算** - 边缘节点分布式重排
- [ ] **AutoML 集成** - 自动化算法选择

## 🎯 算法选择建议

### 医疗领域选择指南

```mermaid
graph TD
    A[医疗查询需求] --> B{性能要求}

    B -->|高性能要求| C{查询频率}
    C -->|高频查询| D[选择 V0620<br/>限制16条处理]
    C -->|一般查询| E[选择 V0310<br/>标准版本]

    B -->|高质量要求| F{特殊需求}
    F -->|需要产品策略| G[选择 V0516<br/>最新版本]
    F -->|时效性敏感| H[选择 V1218<br/>增强版本]
    F -->|稳定性优先| I[选择 V1120<br/>基础版本]

    style D fill:#fff3e0
    style E fill:#fff3e0
    style G fill:#e8f5e8
    style H fill:#f3e5f5
    style I fill:#e1f5fe
```

### 体育领域选择指南

```mermaid
graph TD
    A[体育查询需求] --> B{查询意图}

    B -->|热点话题| C[HOTTOPIC<br/>新闻重排算法]
    B -->|新闻类查询| D{时效性需求}

    D -->|实时赛事| E[强时效性 0<br/>新闻重排算法]
    D -->|赛事回顾| F[中时效性 1<br/>中等衰减算法]
    D -->|历史数据| G[弱时效性 2<br/>弱衰减算法]

    style C fill:#fce4ec
    style E fill:#fce4ec
    style F fill:#fff8e1
    style G fill:#f1f8e9
```

### 新闻领域选择指南

```mermaid
graph TD
    A[新闻查询需求] --> B{结果要求}

    B -->|标准新闻查询| C[V20250421<br/>基础新闻重排]
    B -->|需要多样性| D[V20250522<br/>多样性新闻重排]

    C --> E[时间匹配优化<br/>精确时间处理]
    D --> F[主题去重优化<br/>避免内容聚集]

    style C fill:#e0f2f1
    style D fill:#e0f2f1
    style E fill:#e8f5e8
    style F fill:#fff3e0
```

## 🛡️ 安全

### 报告安全问题

请将安全漏洞报告给 [<EMAIL>](mailto:<EMAIL>)，而不是创建公共问题。

### 安全指南

- 永远不要提交机密或凭据
- 使用安全编码实践
- 验证所有输入
- 遵循 OWASP 指南
- 保持依赖项更新

## 🏷️ 发布流程

发布遵循语义版本控制（SemVer）：

- **MAJOR**: 破坏性更改
- **MINOR**: 新功能（向后兼容）
- **PATCH**: 错误修复（向后兼容）

### 发布步骤

1. 更新版本在相关文件中
2. 更新 CHANGELOG.md
3. 创建发布分支
4. 标记发布
5. 创建 GitHub 发布与说明

## 🤝 社区

### 贡献指南

我们欢迎社区的贡献！以下是您可以帮助的方式：

#### 🐛 错误报告
- 使用[错误报告模板](.github/ISSUE_TEMPLATE/bug_report.md)
- 包含重现步骤和环境详情
- 添加相关日志和错误信息

#### ✨ 功能请求
- 使用[功能请求模板](.github/ISSUE_TEMPLATE/feature_request.md)
- 描述用例和预期行为
- 考虑实现复杂性和影响

#### 🔧 代码贡献
- Fork 仓库并创建功能分支
- 遵循[编码标准](CONTRIBUTING.md#coding-standards)
- 为新功能添加测试
- 根据需要更新文档
- 提交带有清晰描述的拉取请求

### 行为准则

本项目遵循[贡献者公约行为准则](CODE_OF_CONDUCT.md)。参与时，您需要遵守此准则。

### 社区渠道

- 💬 **QQ群**: [加入我们的QQ群](https://qm.qq.com/rerank-service)
- 📧 **邮件列表**: [<EMAIL>](mailto:<EMAIL>)
- 🐦 **微博**: [@重排服务](https://weibo.com/RerankService)
- 📺 **B站**: [重排服务频道](https://space.bilibili.com/RerankService)

## 🙏 认可

贡献者将在以下地方得到认可：
- README.md 贡献者部分
- 发布说明
- 年度贡献者亮点

感谢您为重排服务做出贡献！🎉

## 📄 开源许可

本项目采用 Apache License 2.0 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

- [Gin](https://github.com/gin-gonic/gin) - HTTP Web 框架
- [Jieba](https://github.com/yanyiwu/gojieba) - 中文分词
- [Sonic](https://github.com/bytedance/sonic) - 高性能 JSON 库
- [errgroup](https://pkg.go.dev/golang.org/x/sync/errgroup) - 并发错误处理
- [Prometheus](https://prometheus.io/) - 监控和告警
- [Grafana](https://grafana.com/) - 可观测性平台

## 📞 技术支持

- 📧 **邮箱**: [<EMAIL>](mailto:<EMAIL>)
- 💬 **问题反馈**: [GitHub Issues](https://github.com/your-org/rerank-service/issues)
- 📖 **文档**: [项目 Wiki](https://github.com/your-org/rerank-service/wiki)
- 🆘 **安全问题**: [<EMAIL>](mailto:<EMAIL>)

---

<p align="center">
  <strong>⭐ 支持13种重排策略的智能重排服务 ⭐</strong>
</p>

<p align="center">
  用 ❤️ 构建，来自重排服务社区
</p>
