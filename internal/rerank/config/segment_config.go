package config

import (
	"bufio"
	"errors"
	"fmt"
	"os"
	"sort"
	"strings"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	base_factory "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/factory"
	base_model "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/model"
	"github.com/go-ego/gse"
	"github.com/teamlint/opencc"
	"github.com/yanyiwu/gojieba"
)

const SegmentModuleName = "segment"
const SegmentTomlName = "segment"

var G_SegmentFactory = base_factory.NewFactory(SegmentModuleName, NewSegmentInstance)
var G_SegmentModule = goboot.GetCustomModule(G_SegmentFactory)

var Seg gse.Segmenter
var SegBioMedical gse.Segmenter
var T2s *opencc.OpenCC
var NewsStopwords map[string]bool

type SegmentConfig struct {
	base_model.BaseModel
	SegmentPath           string   `toml:"segmentPath" default:"./resource/rerank/data/dict/s_1.txt"`
	BioMedicalSegmentPath string   `toml:"bioMedicalSegmentPath" default:"./resource/rerank/data/dict/biochemical.txt"`
	StopWordsPath         string   `toml:"stopWordsPath" default:"./resource/rerank/data/dict/stop_word.txt"`
	CCModPath             string   `toml:"ccModPath" default:"./resource/rerank/data/opencc/"`
	SegmentPaths          []string `toml:"segmentPaths"`
}

func NewSegmentInstance(option *SegmentConfig) (*SegmentConfig, error) {
	return option, nil
}

func InitSegment() error {
	var err error
	SegmentPaths := G_SegmentModule.DefaultInstance().SegmentPaths
	fmt.Println("SegmentPaths:", SegmentPaths)
	for _, segmentPath := range SegmentPaths {
		err = Seg.LoadDict(segmentPath)
		if err != nil {
			return errors.New("Load dictionary 1 error: " + err.Error())
		}
	}

	err = SegBioMedical.LoadDict([]string{G_SegmentModule.DefaultInstance().SegmentPath, G_SegmentModule.DefaultInstance().BioMedicalSegmentPath}...)
	if err != nil {
		return errors.New("Load dictionary 2 error: " + err.Error())
	}

	StopWordsPath := G_SegmentModule.DefaultInstance().StopWordsPath
	err = Seg.LoadStop(StopWordsPath)
	if err != nil {
		return errors.New("Load stop dictionary error: " + err.Error())
	}
	CCModPath := G_SegmentModule.DefaultInstance().CCModPath
	T2s, err = opencc.New("t2s", opencc.WithDir(CCModPath))
	if err != nil {
		return errors.New("new t2s error: " + err.Error())
	}

	err = InitNewsStopWords()
	if err != nil {
		return err
	}

	return nil
}

var IFLYS_SEG_JIEBA *gojieba.Jieba

func GetGoJiebaDictFiles() []string {
	// 获取jieeba资源文件路径
	segDictDir := fmt.Sprintf("%s%s", "./resource/seg", "/cppjieba/dict")
	files := []string{"jieba.dict.utf8", "hmm_model.utf8", "user.dict.utf8", "idf.utf8", "stop_words.utf8"}
	paths := make([]string, len(files))
	for index, file := range files {
		path := fmt.Sprintf("%s/%s", segDictDir, file)
		paths[index] = path
	}
	// 不够规范，消除提示
	fmt.Sprintf("jieba dict files:%v\n", paths)
	return paths
}

var Dictionary []string

func init() { // 初始化
	Dictionary = loadWordDict("./resource/rerank/wordDict0620.txt")
	if Dictionary == nil {
		fmt.Println("无法加载自定义词典，程序退出。")
		os.Exit(1)
	}
	// 对 Dictionary 按照长度降序排列
	sort.Slice(Dictionary, func(i, j int) bool {
		return len(Dictionary[i]) > len(Dictionary[j])
	})
}

// 加载自定义词典, Dictionary 存储为列表
func loadWordDict(dictPath string) []string {
	file, err := os.Open(dictPath)
	if err != nil {
		fmt.Printf("无法打开文件: %v\n", err)
		return nil
	}
	defer file.Close()

	var Dictionary []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := scanner.Text()
		Dictionary = append(Dictionary, line)
	}

	if err := scanner.Err(); err != nil {
		fmt.Printf("读取文件时出错: %v\n", err)
	}

	return Dictionary
}

func InitGoJieba() error {
	IFLYS_SEG_JIEBA = gojieba.NewJieba(GetGoJiebaDictFiles()...)
	// IFLYS_SEG_JIEBA = gojieba.NewJieba()
	return nil
	// segPaths := []string{G_SegmentModule.DefaultInstance().SegmentPath, G_SegmentModule.DefaultInstance().BioMedicalSegmentPath}
	// for _, segPath := range segPaths {
	// 	// 打开文件
	// 	file, err := os.Open(segPath)
	// 	if err != nil {
	// 		return err
	// 	}
	// 	// 确保文件在函数结束时关闭
	// 	defer file.Close()

	// 	// 创建一个 Scanner 对象用于逐行读取文件
	// 	scanner := bufio.NewScanner(file)
	// 	// 逐行读取文件内容
	// 	for scanner.Scan() {
	// 		line := scanner.Text()
	// 		// 这里可以对每一行内容进行处理
	// 		items := strings.Split(line, " ")
	// 		// fmt.Println("items:", items[0])
	// 		IFLYS_SEG_JIEBA.AddWord(items[0])
	// 	}

	// 	// 检查扫描过程中是否出现错误
	// 	if err := scanner.Err(); err != nil {
	// 		return err
	// 	}

	// }
	// fmt.Println("加载完成")
	// return nil
}

func InitNewsStopWords() error {
	path := "./resource/rerank/stopwords0522.txt"
	NewsStopwords = make(map[string]bool)
	// 打开文件
	file, err := os.Open(path)
	if err != nil {
		return err
	}
	// 确保文件在函数结束时关闭
	defer file.Close()

	// 创建一个 Scanner 对象用于逐行读取文件
	scanner := bufio.NewScanner(file)
	// 逐行读取文件内容
	for scanner.Scan() {
		line := scanner.Text()
		// 这里可以对每一行内容进行处理
		line = strings.TrimSpace(line)
		if line != "" {
			NewsStopwords[line] = true
		}
	}

	return nil
}
