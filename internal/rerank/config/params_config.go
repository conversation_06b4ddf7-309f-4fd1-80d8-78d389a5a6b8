package config

import (
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	base_factory "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/factory"
	base_model "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/model"
)

const ParamsModuleName = "params"
const ParamsTomlName = "params"

var G_ParamsFactory = base_factory.NewFactory(ParamsModuleName, NewParamsInstance)
var G_ParamModule = goboot.GetCustomModule(G_ParamsFactory)

type ParamsConfig struct {
	base_model.BaseModel
	HealthLevel        string             `toml:"healthLevel" mapstructure:"healthLevel"`
	Domain2BaikeWeight map[string]float64 `toml:"domain2baikeWeight" mapstructure:"domain2baikeWeight"`
	CommonLevel        string             `toml:"commonLevel" mapstructure:"commonLevel"`
	SportsLevel        string             `toml:"sportsLevel" mapstructure:"sportsLevel"`
	SocialExamLevel    string             `toml:"socialExamLevel" mapstructure:"socialExamLevel"`
}

func NewParamsInstance(option *ParamsConfig) (*ParamsConfig, error) {

	return option, nil
}
