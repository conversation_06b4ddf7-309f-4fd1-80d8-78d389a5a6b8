package rerank

import (
	"fmt"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/global"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rerank/service"
	proto_rerank "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/rerank"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
	tokenizer_v2 "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/tokenizer_v2_1"
	"github.com/bytedance/sonic"
)

var SonicAPI sonic.API

func sonicUnmarshal(out []byte, req *pandora_proto.PandoraRequestMessage[bean.RequestPayLoad]) error {
	// 自定义反序列化逻辑
	req.Payload = bean.RequestPayLoad{
		TopK:            -1,
		ScoreThreshold:  0.85,
		Step:            0.05,
		RankCollections: []string{},
		RankSites:       []string{},
	}
	return SonicAPI.Unmarshal(out, req)
}

func padoraRerankProcess(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[bean.RequestPayLoad]) (resp *pandora_proto.PandoraResponseMessage[bean.RespPayLoad]) {
	service := &service.RerankService{
		Resp: proto_rerank.ProtoRerankAPIV2.NewPandoraResponseMessage(),
		Ctx:  ctx,
		Req:  req,
	}
	service.Resp.Payload.Result = []*bean.QueryData{}
	service.HttpHandler()
	return service.Resp
}

func Init() error {
	// TODO: 优化
	err := goboot.RegisterCustomSingletonModule(config.G_ParamsFactory)
	if err != nil {
		return err
	}

	err = config.G_ParamModule.Need()
	if err != nil {
		return err
	}

	err = goboot.RegisterCustomSingletonModule(config.G_SegmentFactory)
	if err != nil {
		return err
	}

	err = config.G_SegmentModule.Need()
	if err != nil {
		return err
	}

	err = goboot.RegisterCustomSingletonModule(config.G_ResFactory)
	if err != nil {
		return err
	}

	err = config.G_ResModule.Need()
	if err != nil {
		return err
	}

	if err := goboot.RegisterCustomMultiModule(tokenizer_v2.G_TokenizerFactory); err != nil {
		return fmt.Errorf("register custom module failed, error: %s", err.Error())
	}

	// tokenizer_v2依赖是否加载
	tokenizer_v2.G_tokenizer.Must()

	err = config.Init()
	if err != nil {
		return err
	}
	global.Init()
	SonicAPI = sonic.Config{UseNumber: true}.Froze()

	router := goboot.HttpServer().DefaultInstance().Router
	v1 := router.Group("/rerank")
	{
		v1.POST("/api/v2", proto_rerank.ProtoRerankAPIV2.GinWrapper().SetHandler(padoraRerankProcess).SetRequestUnmarshal(sonicUnmarshal).HandlerFunc())
	}

	return nil
}
