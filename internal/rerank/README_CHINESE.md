# 🔄 智能重排服务 (Rerank Service)

[![Go 版本](https://img.shields.io/badge/Go-1.23.3-blue.svg)](https://golang.org/)
[![许可证](https://img.shields.io/badge/License-Apache%202.0-green.svg)](LICENSE)
[![构建状态](https://img.shields.io/badge/Build-Passing-brightgreen.svg)]()
[![测试覆盖率](https://img.shields.io/badge/Coverage-85%25-yellow.svg)]()
[![Go 报告卡](https://goreportcard.com/badge/github.com/your-org/rerank-service)](https://goreportcard.com/report/github.com/your-org/rerank-service)

> 🚀 基于 Go 语言构建的高性能、领域专用搜索结果智能重排服务

## ✨ 核心特性

- 🎯 **多领域支持** - 针对医疗、体育、新闻、通用等领域的专业化重排算法
- ⚡ **高性能处理** - 支持并发处理，QPS 可达 1000+
- 🧠 **智能评分** - 多维度特征融合，包括文本匹配、时效性、权威性等
- 🔧 **灵活配置** - 丰富的参数调优选项，适应不同业务场景
- 📊 **完善监控** - 全面的指标监控和可观测性支持
- 🐳 **云原生** - 支持 Docker 容器化和 Kubernetes 部署
- 🔌 **插件架构** - 易于扩展的自定义重排算法框架

## 🏗️ 系统架构

### 整体架构图

```mermaid
graph TB
    subgraph "客户端层"
        A[HTTP 客户端] --> B[负载均衡器<br/>Nginx/HAProxy]
    end

    subgraph "网关层"
        B --> C[API 网关<br/>限流/鉴权/路由]
        C --> D[Pandora 框架<br/>请求解析/响应封装]
    end

    subgraph "服务层"
        D --> E[RerankService<br/>主服务入口]
        E --> F{领域路由器<br/>Model 识别}

        F -->|medical_v0516| G[MedicalRerank<br/>医疗重排算法]
        F -->|sports| H[SportsRerank<br/>体育重排算法]
        F -->|news_v20250421| I[NewsRerank<br/>新闻重排算法]
        F -->|common| J[CommonRerank<br/>通用重排算法]

        subgraph "医疗算法版本"
            G --> G1[Rank0516<br/>最新版本<br/>产品策略排序]
            G --> G2[Rank0310<br/>标准版本<br/>基础医疗重排]
            G --> G3[Rank0620<br/>优化版本<br/>限制处理16条]
            G --> G4[Rank1218<br/>增强版本<br/>时效性优化]
            G --> G5[Rank1120<br/>基础版本<br/>经典算法]
        end

        subgraph "体育算法策略"
            H --> H1[强时效性 0<br/>实时赛事<br/>新闻重排算法]
            H --> H2[中时效性 1<br/>赛事回顾<br/>中等时间衰减]
            H --> H3[弱时效性 2<br/>历史数据<br/>弱时间衰减]
            H --> H4[热点话题<br/>HOTTOPIC<br/>新闻重排算法]
        end

        subgraph "新闻算法版本"
            I --> I1[V20250421<br/>基础新闻重排<br/>时间匹配优化]
            I --> I2[V20250522<br/>多样性新闻重排<br/>主题去重优化]
        end
    end

    subgraph "特征工程层"
        G1 --> K[特征提取器]
        G2 --> K
        H1 --> K
        I --> K
        J --> K

        K --> L[文本处理<br/>分词/标准化]
        K --> M[时间特征<br/>时效性计算]
        K --> N[权威性特征<br/>域名/PageRank]
        K --> O[质量特征<br/>内容长度/结构]
        K --> P[语义特征<br/>相似度计算]
    end

    subgraph "评分计算层"
        L --> Q[评分融合器]
        M --> Q
        N --> Q
        O --> Q
        P --> Q

        Q --> R[LCS 匹配得分]
        Q --> S[Jaccard 相似度]
        Q --> T[编辑距离得分]
        Q --> U[时间衰减得分]
        Q --> V[权威性得分]
    end

    subgraph "排序处理层"
        R --> W[分档排序器]
        S --> W
        T --> W
        U --> W
        V --> W

        W --> X[产品策略排序<br/>库优先级]
        X --> Y[多样性处理<br/>去重/聚类]
        Y --> Z[最终结果排序]
    end

    subgraph "依赖服务层"
        AA[分词器服务<br/>Tokenizer] --> L
        BB[模型推理服务<br/>ASE/TLB] --> P
        CC[配置管理服务<br/>动态配置] --> F
        DD[监控服务<br/>Prometheus] --> E
    end

    Z --> EE[HTTP 响应<br/>JSON 格式]

    style E fill:#e1f5fe
    style F fill:#f3e5f5
    style G fill:#e8f5e8
    style H fill:#fff3e0
    style I fill:#fce4ec
    style J fill:#f1f8e9
    style K fill:#e0f2f1
    style Q fill:#fff8e1
    style W fill:#f3e5f5
```

### 数据流架构图

```mermaid
sequenceDiagram
    participant C as 客户端
    participant G as API网关
    participant R as RerankService
    participant D as 领域路由器
    participant M as 医疗重排
    participant F as 特征提取
    participant S as 评分计算
    participant T as 分词器
    participant A as ASE服务

    C->>G: HTTP POST /rerank/api/v2
    G->>R: 解析请求参数
    R->>R: 请求验证
    R->>D: 识别领域模型

    par 并发处理多查询
        D->>M: 医疗重排算法
        M->>F: 提取文档特征
        F->>T: 文本分词处理
        T-->>F: 返回分词结果
        F->>A: 语义相似度计算
        A-->>F: 返回相似度分数
        F-->>M: 返回特征向量
        M->>S: 计算重排分数
        S-->>M: 返回评分结果
        M->>M: 排序和后处理
        M-->>D: 返回重排结果
    end

    D-->>R: 收集所有结果
    R->>R: 结果聚合和格式化
    R-->>G: 返回重排响应
    G-->>C: HTTP JSON 响应
```

### 核心类结构图 (UML)

```mermaid
classDiagram
    class RerankService {
        -Ctx: PandoraContext
        -medicalRerank: MedicalRerank
        -commonRerank: CommonRerank
        -newsRerank: NewsRerank
        -sportsRerank: SportsRerank
        -Req: PandoraRequestMessage
        -Resp: PandoraResponseMessage
        +Init() void
        +HttpHandler() void
        +rank() void
        +processRanking(span) QueryData[]
        +performRanking(span, domainID, data) map[]
        +checkRequest(span) error
        +handleError(error) void
        +collectResults(resultMap) QueryData[]
    }

    class RankInterface {
        <<interface>>
        +Rank(span, req, data) map[]
    }

    class MedicalRerank {
        -Ctx: PandoraContext
        +Rank(span, req, data) map[]
        +Rank0516(span, req, data) map[]
        +Rank0310(span, req, data) map[]
        +Rank1218(span, req, data) map[]
        +Rank1120(span, req, data) map[]
        +SortResults(results, priority) void
        +SortResultsByChar(results) void
        +answerStage(docs, step, threshold) map[][]
        +calcRerankScore(params...) float64
        +Segcalc(req, span, query) SegRes
        +processQuery(req, span, query) []string, []float64
        +calculateWordScore(params...) error
        +DocProcess(req, span, data, rerankList) error
        +groupResults(results) [][]map[]
        +extractYear(title) int64
    }

    class SportsRerank {
        -Ctx: PandoraContext
        +Rank(span, req, data) map[]
        +sportsRerank(req, timeliness, query, data) map[]
        +newsRerank(data) map[]
        +mediumScore(data, tmpData, query) map[]
        +weakScore(data, tmpData, query) map[]
        +answerStage(docs, step, threshold) map[][]
        +handleHotTopic(docs) map[]
        +handleNews(req, data) map[]
        +processTimeliness(timeliness, data) map[]
    }

    class NewsRerank {
        -Ctx: PandoraContext
        +Rank(span, req, data) map[]
        +Rank20250421(span, req, data) map[]
        +Rank20250522(span, req, data) map[]
        +diversityRanking(results) map[]
        +breakingNewsDetection(query, docs) bool
        +sourceCredibilityScore(domain) float64
        +MatchTimesScore(params...) float64
        +processQuery(req, span, query, params...) []string, []float64
        +sortResultsWithDiversity(results, topicMap) void
        +TopicIntersection(topic1, topic2) bool
        +compareDateComponents(params...) bool
    }

    class CommonRerank {
        -Ctx: PandoraContext
        +Rank(span, req, data) map[]
        +calcRerankScore(params...) float64
        +answerStage(docs, step, threshold) map[][]
        +textMatching(query, title) float64
        +timeDecayScore(postTime) float64
    }

    class FeatureExtractor {
        +extractTextFeatures(query, doc) Features
        +extractTimeFeatures(query, doc) Features
        +extractAuthorityFeatures(doc) Features
        +extractQualityFeatures(doc) Features
        +calculateLCS(tokens1, tokens2) float64
        +calculateJaccard(tokens1, tokens2) float64
        +calculateEditDistance(str1, str2) float64
    }

    class ScoreCalculator {
        +fusionScore(features) float64
        +normalizeScore(score) float64
        +weightedCombination(scores, weights) float64
        +timeDecayFunction(timeDiff) float64
        +authorityWeight(domain, pagerank) float64
    }

    class ConfigManager {
        +paramsConfig: ParamsConfig
        +resConfig: ResConfig
        +segmentConfig: SegmentConfig
        +domainConfig: DomainConfig
        +Init() error
        +LoadStopWords() error
        +LoadMedicalDomain() error
        +InitSegment() error
    }

    class ParamsConfig {
        +HealthLevel: string
        +Domain2BaikeWeight: map[string]float64
        +CommonLevel: string
        +SportsLevel: string
    }

    class ResConfig {
        +BaseResDir: string
        +StwPath: string
        +MedicalDomainPath: string
        +BlockDict: string[]
        +PrefixDict: string[]
        +SuffixDict: string[]
        +SynonymsDict: string[][]
    }

    class SegmentConfig {
        +SegmentPath: string
        +BioMedicalSegmentPath: string
        +StopWordsPath: string
        +CCModPath: string
        +SegmentPaths: string[]
    }

    class RequestPayLoad {
        +Model: string
        +TopK: int32
        +ScoreThreshold: float64
        +Step: float64
        +RankCollections: string[]
        +RankSites: string[]
        +Intent: string
        +TimeLiness: string
        +Data: QueryData[]
    }

    class QueryData {
        +Query: string
        +Type: string
        +Extra: map[]
        +Docs: map[]
    }

    class RespPayLoad {
        +Result: QueryData[]
    }

    %% 关系定义
    RerankService --> MedicalRerank : 组合
    RerankService --> SportsRerank : 组合
    RerankService --> NewsRerank : 组合
    RerankService --> CommonRerank : 组合
    RerankService --> RequestPayLoad : 使用
    RerankService --> RespPayLoad : 使用

    MedicalRerank ..|> RankInterface : 实现
    SportsRerank ..|> RankInterface : 实现
    NewsRerank ..|> RankInterface : 实现
    CommonRerank ..|> RankInterface : 实现

    MedicalRerank --> FeatureExtractor : 使用
    SportsRerank --> FeatureExtractor : 使用
    NewsRerank --> FeatureExtractor : 使用
    CommonRerank --> FeatureExtractor : 使用

    MedicalRerank --> ScoreCalculator : 使用
    SportsRerank --> ScoreCalculator : 使用
    NewsRerank --> ScoreCalculator : 使用
    CommonRerank --> ScoreCalculator : 使用

    ConfigManager --> ParamsConfig : 管理
    ConfigManager --> ResConfig : 管理
    ConfigManager --> SegmentConfig : 管理

    RequestPayLoad --> QueryData : 包含
    RespPayLoad --> QueryData : 包含
```

### 并发处理架构图

```mermaid
graph TB
    subgraph "并发控制层"
        A[errgroup.Group<br/>错误组管理] --> B[context.WithCancel<br/>上下文控制]
        B --> C[concurrent_map<br/>并发安全映射]
    end

    subgraph "工作池管理"
        D[ants_pools<br/>协程池] --> E[goroutine_num: 8192<br/>工作协程数量]
        E --> F[任务队列<br/>Query 分发]
    end

    subgraph "并发执行单元"
        F --> G1[Worker 1<br/>Query 处理]
        F --> G2[Worker 2<br/>Query 处理]
        F --> G3[Worker 3<br/>Query 处理]
        F --> GN[Worker N<br/>Query 处理]

        G1 --> H1[领域识别<br/>Model 路由]
        G2 --> H2[领域识别<br/>Model 路由]
        G3 --> H3[领域识别<br/>Model 路由]
        GN --> HN[领域识别<br/>Model 路由]

        H1 --> I1[算法执行<br/>特征+评分]
        H2 --> I2[算法执行<br/>特征+评分]
        H3 --> I3[算法执行<br/>特征+评分]
        HN --> IN[算法执行<br/>特征+评分]
    end

    subgraph "结果收集层"
        I1 --> J[结果聚合器<br/>Index 排序]
        I2 --> J
        I3 --> J
        IN --> J

        J --> K[最终结果<br/>QueryData[]]
    end

    subgraph "错误处理"
        L[错误检测] --> M[取消信号<br/>cancel()]
        M --> N[清理资源<br/>defer cleanup]
    end

    A -.-> L
    C --> J

    style A fill:#ffebee
    style D fill:#e8f5e8
    style J fill:#e1f5fe
    style L fill:#fff3e0
```

### 配置管理架构图

```mermaid
graph TB
    subgraph "配置文件层"
        A[goboot.toml<br/>基础服务配置] --> B[ConfigManager<br/>配置管理器]
        C[rerank.toml<br/>业务配置] --> B
        D[params.toml<br/>参数配置] --> B
        E[domain.toml<br/>领域配置] --> B
    end

    subgraph "配置模块"
        B --> F[ParamsConfig<br/>参数配置模块]
        B --> G[ResConfig<br/>资源配置模块]
        B --> H[SegmentConfig<br/>分词配置模块]
        B --> I[DomainConfig<br/>领域配置模块]

        F --> F1[HealthLevel: L06<br/>医疗等级]
        F --> F2[Domain2BaikeWeight<br/>领域权重映射]
        F --> F3[CommonLevel: L03<br/>通用等级]
        F --> F4[SportsLevel: L09<br/>体育等级]

        G --> G1[BaseResDir<br/>资源基础目录]
        G --> G2[StwPath<br/>停用词路径]
        G --> G3[MedicalDomainPath<br/>医疗领域词典]
        G --> G4[BlockDict<br/>屏蔽词典]
        G --> G5[SynonymsDict<br/>同义词典]

        H --> H1[SegmentPath<br/>分词词典路径]
        H --> H2[StopWordsPath<br/>停用词路径]
        H --> H3[CCModPath<br/>繁简转换路径]
        H --> H4[SegmentPaths<br/>多词典路径]

        I --> I1[LevelCollections<br/>等级库集合]
        I --> I2[RankCollections<br/>排序库集合]
        I --> I3[DomainId<br/>领域标识]
    end

    subgraph "工厂模式"
        J[G_ParamsFactory<br/>参数工厂] --> F
        K[G_ResFactory<br/>资源工厂] --> G
        L[G_SegmentFactory<br/>分词工厂] --> H
        M[G_DomainFactory<br/>领域工厂] --> I
    end

    subgraph "外部依赖配置"
        N[Tokenizer 配置<br/>分词器设置] --> N1[ernie-3.0-nano-zh<br/>模型路径]
        N --> N2[max_length: 512<br/>最大长度]
        N --> N3[worker_num: 3<br/>工作线程数]

        O[ASE 配置<br/>模型推理设置] --> O1[termweight_v20231221<br/>模型名称]
        O --> O2[tlb_server<br/>服务地址]
        O --> O3[timeout_mills: 2000<br/>超时设置]

        P[Ants Pool 配置<br/>协程池设置] --> P1[goroutine_num: 8192<br/>协程数量]
        P --> P2[queue_size<br/>队列大小]
    end

    B --> N
    B --> O
    B --> P

    style B fill:#e1f5fe
    style F fill:#e8f5e8
    style G fill:#fff3e0
    style H fill:#f3e5f5
    style I fill:#fce4ec
```

### 依赖服务交互图

```mermaid
graph TB
    subgraph "重排服务核心"
        A[RerankService<br/>主服务] --> B[特征提取器<br/>FeatureExtractor]
        B --> C[评分计算器<br/>ScoreCalculator]
    end

    subgraph "分词服务"
        D[Tokenizer Service<br/>分词服务] --> D1[Jieba 分词器<br/>中文分词]
        D --> D2[GoJieba 引擎<br/>Go 实现]
        D --> D3[词典管理<br/>医疗/体育词典]

        D1 --> D4[分词结果<br/>Token 数组]
        D2 --> D4
        D3 --> D4
    end

    subgraph "模型推理服务"
        E[ASE Service<br/>模型推理] --> E1[TLB 负载均衡<br/>服务发现]
        E --> E2[本地推理<br/>IP 直连]
        E --> E3[云端推理<br/>ASE 接口]

        E1 --> E4[语义相似度<br/>Similarity Score]
        E2 --> E4
        E3 --> E4
    end

    subgraph "配置服务"
        F[Config Service<br/>配置管理] --> F1[动态配置<br/>热更新]
        F --> F2[静态配置<br/>文件加载]
        F --> F3[环境配置<br/>多环境支持]

        F1 --> F4[配置参数<br/>实时生效]
        F2 --> F4
        F3 --> F4
    end

    subgraph "监控服务"
        G[Monitor Service<br/>监控服务] --> G1[Prometheus<br/>指标收集]
        G --> G2[Grafana<br/>可视化]
        G --> G3[Alertmanager<br/>告警管理]

        G1 --> G4[性能指标<br/>QPS/延迟/错误率]
        G2 --> G4
        G3 --> G4
    end

    subgraph "存储服务"
        H[Storage Service<br/>存储服务] --> H1[Redis Cache<br/>结果缓存]
        H --> H2[File System<br/>词典文件]
        H --> H3[Database<br/>配置存储]

        H1 --> H4[数据持久化<br/>快速访问]
        H2 --> H4
        H3 --> H4
    end

    %% 服务间调用关系
    B -.->|分词请求| D4
    B -.->|相似度计算| E4
    A -.->|配置获取| F4
    A -.->|指标上报| G4
    A -.->|缓存读写| H4

    %% 数据流向
    D4 -->|分词结果| B
    E4 -->|相似度分数| B
    F4 -->|配置参数| A
    G4 -->|监控数据| A
    H4 -->|缓存数据| A

    style A fill:#e1f5fe
    style D fill:#e8f5e8
    style E fill:#fff3e0
    style F fill:#f3e5f5
    style G fill:#fce4ec
    style H fill:#f1f8e9
```

### 算法执行流程图

```mermaid
flowchart TD
    A[开始重排] --> B{检查请求参数}
    B -->|无效| C[返回错误 13001-13004]
    B -->|有效| D[解析领域模型]

    D --> E{模型类型判断}
    E -->|medical_v0516| F[医疗重排 V0516]
    E -->|medical_v0310/v0620| G[医疗重排 V0310]
    E -->|medical_v1218| H[医疗重排 V1218]
    E -->|medical_v1120/health| I[医疗重排 V1120]
    E -->|sports| J[体育重排]
    E -->|news_v20250421/v20250522| K[新闻重排]
    E -->|其他| L[通用重排]

    F --> M[医疗V0516特征提取]
    G --> N[医疗V0310特征提取]
    H --> O[医疗V1218特征提取]
    I --> P[医疗V1120特征提取]

    M --> M1[产品策略排序<br/>库优先级处理]
    M1 --> M2[超短文本处理<br/>字符长度排序]

    N --> N1{版本判断}
    N1 -->|V0310| N2[标准分词处理<br/>TitleSeg]
    N1 -->|V0620| N3[优化分词处理<br/>TitleSeg0620<br/>限制16条处理]
    N2 --> N4[医学实体识别]
    N3 --> N4
    N4 --> N5[权威性评分]

    O --> O1[时效性强度检测<br/>TimeStrengthReg]
    O1 --> O2[医学实体识别]
    O2 --> O3[权威性评分]

    P --> P1[时效性强度检测<br/>TimeStrengthReg]
    P1 --> P2[医学实体识别]
    P2 --> P3[权威性评分]

    J --> Q[体育特征提取]
    Q --> R{意图判断}
    R -->|HOTTOPIC| S[热点话题<br/>新闻重排算法]
    R -->|NEWS/其他| T{时效性等级}
    T -->|强时效 0| U[实时赛事排序<br/>新闻重排算法]
    T -->|中时效 1| V[赛事回顾排序<br/>中等时间衰减]
    T -->|弱时效 2| W[历史数据排序<br/>弱时间衰减]

    K --> X[新闻特征提取]
    X --> Y{版本判断}
    Y -->|V20250421| Z[基础新闻重排<br/>时间匹配优化]
    Y -->|V20250522| AA[多样性新闻重排<br/>主题去重优化]
    Z --> BB[时间词匹配<br/>时间范围提取]
    AA --> BB
    BB --> CC[多样性排序<br/>主题交集检测]
    CC --> DD[来源可信度评分]

    L --> EE[通用特征提取]
    EE --> FF[基础文本匹配]
    FF --> GG[简单权威性评分]

    M2 --> HH[分档处理]
    N5 --> HH
    O3 --> HH
    P3 --> HH
    S --> HH
    U --> HH
    V --> HH
    W --> HH
    DD --> HH
    GG --> HH

    HH --> II[TopK 截取]
    II --> JJ[分数阈值过滤]
    JJ --> KK[最终排序]
    KK --> LL[结果格式化]
    LL --> MM[返回响应]

    style A fill:#e8f5e8
    style E fill:#fff3e0
    style CC fill:#e1f5fe
    style HH fill:#f3e5f5
```

### 错误处理架构图

```mermaid
graph TB
    subgraph "错误分类体系"
        A[错误码设计<br/>5位数字] --> B[业务类型<br/>前2位]
        A --> C[错误类型<br/>后3位]

        B --> B1[10xxx 通用错误]
        B --> B2[11xxx 精排错误]
        B --> B3[12xxx 粗排错误]
        B --> B4[13xxx 重排错误]

        C --> C1[001-099 参数错误]
        C --> C2[100-199 业务逻辑错误]
        C --> C3[200-299 系统错误]
        C --> C4[300-399 依赖服务错误]
    end

    subgraph "重排错误码"
        D[13001 请求数据为空] --> E[RerankError_ReqDataEmptyError]
        F[13002 请求query为空] --> G[RerankError_ReqQueryEmptyError]
        H[13003 请求数据id重复] --> I[RerankError_ReqDataIdDuplicateError]
        J[13004 traceId为空] --> K[RerankError_ReqTraceIdEmptyError]
        L[13005 内部逻辑错误] --> M[RerankError_UnknowError]
        N[13006 数据转换错误] --> O[RerankError_DataConvertError]
    end

    subgraph "错误处理流程"
        P[异常捕获] --> Q{错误类型判断}
        Q -->|参数错误| R[参数验证失败]
        Q -->|业务错误| S[业务逻辑异常]
        Q -->|系统错误| T[系统运行异常]
        Q -->|依赖错误| U[外部服务异常]

        R --> V[记录错误日志]
        S --> V
        T --> V
        U --> V

        V --> W[错误码映射]
        W --> X[错误信息格式化]
        X --> Y[返回错误响应]
    end

    subgraph "错误恢复机制"
        Z[Panic 恢复] --> AA[defer recover()]
        AA --> BB[错误堆栈记录]
        BB --> CC[服务状态重置]
        CC --> DD[继续处理请求]

        EE[超时处理] --> FF[context.WithTimeout]
        FF --> GG[取消信号传播]
        GG --> HH[资源清理]

        II[并发错误] --> JJ[errgroup.Group]
        JJ --> KK[错误聚合]
        KK --> LL[快速失败]
    end

    subgraph "监控告警"
        MM[错误率监控] --> NN[Prometheus 指标]
        NN --> OO[错误率阈值]
        OO --> PP[告警触发]
        PP --> QQ[通知运维]

        RR[错误日志] --> SS[ELK 收集]
        SS --> TT[错误分析]
        TT --> UU[问题定位]
    end

    P --> Z
    P --> EE
    P --> II
    V --> MM
    V --> RR

    style A fill:#ffebee
    style P fill:#fff3e0
    style Z fill:#e8f5e8
    style MM fill:#e1f5fe
```

## 🚀 快速开始

### 环境要求

- Go 1.23.3 或更高版本
- Docker（可选）

### 安装部署

```bash
# 克隆仓库
git clone https://github.com/your-org/rerank-service.git
cd rerank-service

# 安装依赖
go mod tidy

# 编译服务
go build -tags=rerank -o rerank-server

# 启动服务
./rerank-server server -c ./config/rerank
```

### Docker 部署

```bash
# 构建镜像
docker build -f cicd/rerank.dockerfile -t rerank-service .

# 运行容器
docker run -p 50903:50903 -v ./config:/app/config rerank-service
```

## 📖 API 文档

### 重排接口

**POST** `/rerank/api/v2`

#### 请求示例

```json
{
  "header": {
    "traceId": "unique-trace-id"
  },
  "payload": {
    "model": "medical_v0516",
    "topK": 10,
    "scoreThreshold": 0.7,
    "data": [
      {
        "query": "心脏病症状",
        "docs": [
          {
            "id": "doc1",
            "title": "了解心脏病",
            "summary": "心脏病综合指南...",
            "url": "https://example.com/heart-disease",
            "rank_score": 0.95,
            "post_ts": 1663136820000
          }
        ]
      }
    ]
  }
}
```

#### 响应示例

```json
{
  "header": {
    "code": 0,
    "message": "success",
    "traceId": "unique-trace-id"
  },
  "payload": {
    "result": [
      {
        "query": "心脏病症状",
        "docs": [
          {
            "id": "doc1",
            "title": "了解心脏病",
            "_rerank_score": 0.98,
            "_rerank_index": 1
          }
        ]
      }
    ]
  }
}
```

## 🎯 支持的领域

### 🏥 医疗重排

#### V0516 版本 (最新版本)
- **模型标识**: `5001_V20250516`
- **核心特性**:
  - 产品策略排序 (库优先级处理)
  - 超短文本处理 (字符长度排序)
  - 年份提取和时间戳处理
  - 分组结果处理 (语义分数水平分组)
- **适用场景**: 生产环境主推版本，适合对排序质量要求较高的场景
- **技术亮点**: 支持RankCollections优先级排序，处理超短文本优化

#### V0310/V0620 版本 (标准版本)
- **模型标识**: `5001_V20250310`, `5001_V20250620`
- **核心特性**:
  - V0310: 标准医疗重排算法，使用TitleSeg分词
  - V0620: 优化版本，限制处理前16条结果，使用TitleSeg0620分词
  - 医学实体识别和权威性评分
  - 质量等级评分 (q_user/q_level)
- **适用场景**: 标准医疗查询，平衡性能和效果
- **技术亮点**: V0620版本针对高频查询优化，减少计算开销

#### V1218 版本 (增强版本)
- **模型标识**: `5001_V20251218`
- **核心特性**:
  - 时效性强度检测 (TimeStrengthReg正则匹配)
  - 增强的医学实体识别
  - 优化的权威性评分算法
  - 时间敏感查询特殊处理
- **适用场景**: 对时效性要求较高的医疗查询
- **技术亮点**: 强时间性查询检测，时间强度系数调整

#### V1120 版本 (基础版本)
- **模型标识**: `1`, `5001`, `5001_V1120`, `5001_V20251120`
- **核心特性**:
  - 经典医疗重排算法
  - 基础的时效性强度检测
  - 标准的医学实体识别
  - 领域ID支持 (domainId)
- **适用场景**: 兜底策略，稳定可靠的基础版本
- **技术亮点**: 成熟稳定，兼容性好

### ⚽ 体育重排

#### 意图驱动的重排策略
- **模型标识**: `5010`
- **意图类型**:
  - **HOTTOPIC (热点话题)**: 使用新闻重排算法
  - **NEWS (新闻类)**: 根据时效性等级选择策略
  - **其他意图**: 默认使用时效性分级处理

#### 时效性分级策略
- **强时效性 (0)**:
  - 算法: 新闻重排算法 (newsRerank)
  - 适用: 实时赛事、比赛直播、即时比分
  - 特点: 时间衰减系数 0.2，激进的时间权重

- **中时效性 (1)**:
  - 算法: 中等时间衰减 (mediumScore)
  - 适用: 赛事回顾、比赛分析、球队动态
  - 特点: 平衡时效性和相关性

- **弱时效性 (2)**:
  - 算法: 弱时间衰减 (weakScore)
  - 适用: 历史数据、球员资料、规则介绍
  - 特点: 以相关性为主，时间权重较低

### 📰 新闻重排

#### V20250421 版本 (基础新闻重排)
- **模型标识**: `news_V20250421`
- **核心特性**:
  - 时间匹配优化 (MatchTimesScore)
  - 时间词识别和匹配
  - 时间范围提取 (ExtractRangeTime/ExtractTime)
  - 年份判断和时间戳处理
- **适用场景**: 标准新闻查询，时间敏感内容
- **技术亮点**: 精确的时间匹配算法，支持多种时间格式

#### V20250522 版本 (多样性新闻重排)
- **模型标识**: `news_V20250522`
- **核心特性**:
  - 继承V20250421的所有功能
  - 多样性排序 (sortResultsWithDiversity)
  - 主题交集检测 (TopicIntersection)
  - 前5位结果多样性优化
- **适用场景**: 需要结果多样性的新闻查询
- **技术亮点**: 主题去重算法，避免相似内容聚集

#### 新闻重排核心算法
- **时间得分计算**: 基于时间戳的衰减函数 `1 - tanh(0.05 * index)`
- **时间匹配策略**:
  - 时间戳判断: 发布时间与查询时间范围比较
  - 时间词匹配: 标题中时间词的存在性检查
  - 日期组件比较: 年月日的精确匹配
  - 年份判断: 单年份查询的特殊处理

### 🌐 通用重排
- **模型标识**: `common` (默认兜底策略)
- **核心特性**: 通用文本匹配、基础权威性评分
- **应用场景**: 通用搜索查询、未匹配到特定领域的查询
- **技术亮点**: 轻量级算法，快速响应

### 📋 重排策略对照表

| 模型标识 | 常量名称 | 算法版本 | 调用方法 | 主要特性 | 适用场景 |
|----------|----------|----------|----------|----------|----------|
| `1` | MEDICAL | 医疗V1120 | Rank1120 | 基础医疗重排 | 通用医疗查询 |
| `5001` | HEALTH | 医疗V1120 | Rank1120 | 健康领域重排 | 健康相关查询 |
| `5001_V1120` | V1120 | 医疗V1120 | Rank1120 | 经典算法版本 | 稳定性要求高 |
| `5001_V20251120` | Medical_V1120 | 医疗V1120 | Rank1120 | 标准医疗重排 | 标准医疗场景 |
| `5001_V1218` | V1218 | 医疗V1218 | Rank1218 | 时效性增强 | 时间敏感查询 |
| `5001_V20251218` | Medical_V1218 | 医疗V1218 | Rank1218 | 增强版医疗重排 | 高质量医疗内容 |
| `5001_V20250310` | Medical_V0310 | 医疗V0310 | Rank0310 | 标准版本 | 平衡性能效果 |
| `5001_V20250620` | Medical_V0620 | 医疗V0310 | Rank0310 | 优化版本(限16条) | 高频查询优化 |
| `5001_V20250516` | Medical_V0516 | 医疗V0516 | Rank0516 | 最新版本 | 生产环境主推 |
| `5010` | SPORTS | 体育重排 | Rank | 多策略体育重排 | 体育相关查询 |
| `news_V20250421` | News_V20250421 | 新闻V20250421 | Rank20250421 | 基础新闻重排 | 标准新闻查询 |
| `news_V20250522` | News_V20250522 | 新闻V20250421 | Rank20250421 | 多样性新闻重排 | 多样性要求高 |
| `其他` | - | 通用重排 | Rank | 通用文本匹配 | 兜底策略 |

### 🔄 重排策略选择流程

```mermaid
graph TD
    A[接收重排请求] --> B{解析Model参数}

    B -->|医疗相关| C{医疗版本判断}
    C -->|5001_V20250516| D[医疗V0516<br/>最新版本]
    C -->|5001_V20250310<br/>5001_V20250620| E[医疗V0310<br/>标准版本]
    C -->|5001_V1218<br/>5001_V20251218| F[医疗V1218<br/>增强版本]
    C -->|1, 5001<br/>5001_V1120<br/>5001_V20251120| G[医疗V1120<br/>基础版本]

    B -->|5010| H{体育意图判断}
    H -->|HOTTOPIC| I[热点话题<br/>新闻重排算法]
    H -->|NEWS/其他| J{时效性等级}
    J -->|强时效 0| K[实时赛事<br/>新闻重排算法]
    J -->|中时效 1| L[赛事回顾<br/>中等衰减]
    J -->|弱时效 2| M[历史数据<br/>弱衰减]

    B -->|新闻相关| N{新闻版本判断}
    N -->|news_V20250421| O[基础新闻重排<br/>时间匹配优化]
    N -->|news_V20250522| P[多样性新闻重排<br/>主题去重优化]

    B -->|其他| Q[通用重排<br/>兜底策略]

    D --> R[执行重排算法]
    E --> R
    F --> R
    G --> R
    I --> R
    K --> R
    L --> R
    M --> R
    O --> R
    P --> R
    Q --> R

    R --> S[返回重排结果]

    style D fill:#e8f5e8
    style E fill:#fff3e0
    style F fill:#f3e5f5
    style G fill:#e1f5fe
    style I fill:#fce4ec
    style K fill:#fce4ec
    style L fill:#fff8e1
    style M fill:#f1f8e9
    style O fill:#e0f2f1
    style P fill:#e0f2f1
    style Q fill:#f5f5f5
```

### 📊 算法特性对比表

| 特性 | V0516 | V0310 | V0620 | V1218 | V1120 | 体育重排 | 新闻重排 | 通用重排 |
|------|-------|-------|-------|-------|-------|----------|----------|----------|
| **产品策略排序** | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **超短文本处理** | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **库优先级排序** | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **分组结果处理** | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **年份提取** | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **医学实体识别** | ✅ | ✅ | ✅ | ✅ | ✅ | ❌ | ❌ | ❌ |
| **权威性评分** | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| **时效性检测** | ❌ | ❌ | ❌ | ✅ | ✅ | ✅ | ✅ | ❌ |
| **分词优化** | ❌ | TitleSeg | TitleSeg0620 | ❌ | ❌ | ❌ | ❌ | ❌ |
| **处理数量限制** | 无限制 | 无限制 | 16条 | 无限制 | 无限制 | 无限制 | 无限制 | 无限制 |
| **时间匹配** | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ | ❌ |
| **多样性排序** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ✅(V522) | ❌ |
| **意图识别** | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ |
| **时效性分级** | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ |
| **主题去重** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ✅(V522) | ❌ |
| **突发新闻检测** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ |
| **来源可信度** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ |

### 🎯 算法选择建议

#### 医疗领域选择指南

```mermaid
graph TD
    A[医疗查询需求] --> B{性能要求}

    B -->|高性能要求| C{查询频率}
    C -->|高频查询| D[选择 V0620<br/>限制16条处理]
    C -->|一般查询| E[选择 V0310<br/>标准版本]

    B -->|高质量要求| F{特殊需求}
    F -->|需要产品策略| G[选择 V0516<br/>最新版本]
    F -->|时效性敏感| H[选择 V1218<br/>增强版本]
    F -->|稳定性优先| I[选择 V1120<br/>基础版本]

    style D fill:#fff3e0
    style E fill:#fff3e0
    style G fill:#e8f5e8
    style H fill:#f3e5f5
    style I fill:#e1f5fe
```

#### 体育领域选择指南

```mermaid
graph TD
    A[体育查询需求] --> B{查询意图}

    B -->|热点话题| C[HOTTOPIC<br/>新闻重排算法]
    B -->|新闻类查询| D{时效性需求}

    D -->|实时赛事| E[强时效性 0<br/>新闻重排算法]
    D -->|赛事回顾| F[中时效性 1<br/>中等衰减算法]
    D -->|历史数据| G[弱时效性 2<br/>弱衰减算法]

    style C fill:#fce4ec
    style E fill:#fce4ec
    style F fill:#fff8e1
    style G fill:#f1f8e9
```

#### 新闻领域选择指南

```mermaid
graph TD
    A[新闻查询需求] --> B{结果要求}

    B -->|标准新闻查询| C[V20250421<br/>基础新闻重排]
    B -->|需要多样性| D[V20250522<br/>多样性新闻重排]

    C --> E[时间匹配优化<br/>精确时间处理]
    D --> F[主题去重优化<br/>避免内容聚集]

    style C fill:#e0f2f1
    style D fill:#e0f2f1
    style E fill:#e8f5e8
    style F fill:#fff3e0
```

## ⚙️ 配置说明

### 基础配置

```toml
# config/rerank/rerank.toml
[[tokenizers]]
enabled = true
name = "rerank-ernie-3.0"
path = "./resource/tokenizer/ernie-3.0/tokenizer.json"
max_length = 512

[[ases]]
enabled = true
name = "termweight_v20231221"
timeout_mills = 2000
```

### 高级参数

| 参数 | 说明 | 默认值 | 范围 |
|------|------|--------|------|
| `topK` | 返回的最大结果数 | -1 (全部) | -1, 1-1000 |
| `scoreThreshold` | 最小分数阈值 | 0.85 | 0.0-1.0 |
| `step` | 分档步长 | 0.05 | 0.01-0.5 |
| `timeliness` | 时效性等级 | "1" | "0", "1", "2" |

## 🔧 开发指南

### 添加自定义重排算法

```go
// internal/rerank/service/rank/rank_custom.go
package rank

type CustomRerank struct {
    Ctx *pandora_context.PandoraContext
}

func (r *CustomRerank) Rank(span *span.Span, req *bean.Request, data *bean.QueryData) ([]*map[string]any, error) {
    // 实现自定义重排逻辑
    var rerankScore = make([]*map[string]any, 0)
    
    for _, doc := range data.Docs {
        score := r.calculateCustomScore(req, data.Query, doc)
        (*doc)["_rerank_score"] = score
        rerankScore = append(rerankScore, doc)
    }
    
    // 按分数排序
    sort.Slice(rerankScore, func(i, j int) bool {
        score1 := (*rerankScore[i])["_rerank_score"].(float64)
        score2 := (*rerankScore[j])["_rerank_score"].(float64)
        return score1 > score2
    })
    
    return rerankScore, nil
}
```

### 运行测试

```bash
# 运行所有测试
go test ./...

# 运行特定测试
go test ./test/rerank/

# 运行覆盖率测试
go test -cover ./...
```

## 📊 性能指标

### 基准测试

| 指标 | 数值 | 说明 |
|------|------|------|
| **延迟 (P99)** | < 100ms | 单查询处理时间 |
| **吞吐量** | 1000+ QPS | 并发处理能力 |
| **内存使用** | < 2GB | 稳定状态下 |
| **CPU 使用** | < 80% | 高负载场景 |

### 准确性指标

| 领域 | NDCG@10 | MAP | CTR 提升 |
|------|---------|-----|----------|
| 医疗 | 0.85+ | 0.80+ | 15-25% |
| 体育 | 0.82+ | 0.78+ | 12-20% |
| 新闻 | 0.88+ | 0.83+ | 18-28% |
| 通用 | 0.80+ | 0.75+ | 10-18% |

## 🔍 监控运维

### 健康检查

```bash
# 服务健康状态
curl http://localhost:50903/actuator/prometheus

# API 健康检查
curl -X POST http://localhost:50903/rerank/api/v2 \
  -H "Content-Type: application/json" \
  -d '{"header":{"traceId":"health-check"},"payload":{"model":"common","data":[]}}'
```

### 关键指标

- `rerank_requests_total` - 总请求处理数
- `rerank_duration_seconds` - 请求处理时间
- `rerank_errors_total` - 按类型统计的错误数
- `rerank_concurrent_requests` - 活跃并发请求数

## 🧠 算法深度解析

### 特征工程流水线

重排服务采用了先进的多阶段特征工程流水线：

#### 1. 文本处理
```go
// 文本标准化和分词
func (r *Rerank) processText(query, title, content string) Features {
    // 使用结巴分词进行中文分词
    queryTokens := jieba.Cut(query, true)
    titleTokens := jieba.Cut(title, true)

    // 计算文本相似度特征
    features := Features{
        LCS:           calculateLCS(queryTokens, titleTokens),
        JaccardSim:    calculateJaccard(queryTokens, titleTokens),
        EditDistance:  calculateEditDistance(query, title),
        TermFreq:      calculateTF(queryTokens, content),
    }

    return features
}
```

#### 2. 时间特征
```go
// 基于时间衰减函数的评分
func (r *Rerank) calculateTimeScore(postTime int64, query string) float64 {
    currentTime := time.Now().Unix() * 1000
    timeDiff := float64(currentTime - postTime) / (24 * 3600 * 1000) // 天数

    // 指数衰减处理时效性
    timeDecay := math.Exp(-timeDiff / 30.0) // 30天半衰期

    // 时效性查询加权
    timeBoost := 1.0
    if containsTimeKeywords(query) {
        timeBoost = 1.5
    }

    return timeDecay * timeBoost
}
```

#### 3. 权威性评分
```go
// 多因子权威性计算
func (r *Rerank) calculateAuthorityScore(doc Document) float64 {
    domainScore := r.getDomainAuthority(doc.Domain)
    pageRankScore := float64(doc.PageRank) / 10.0
    userSignals := r.getUserEngagementScore(doc.ID)

    // 加权组合
    return 0.4*domainScore + 0.3*pageRankScore + 0.3*userSignals
}
```

### 领域专用优化

#### 医疗领域
- **医学实体识别**: 识别疾病、症状、治疗方法
- **权威性加权**: 优先考虑医疗机构和认证来源
- **时效性平衡**: 平衡最新研究与既定知识

#### 体育领域
- **赛事检测**: 识别实时赛事、比赛和锦标赛
- **时效性缩放**: 对体育新闻采用激进的时间衰减
- **队伍/球员识别**: 增强体育实体匹配

#### 新闻领域
- **突发新闻检测**: 实时事件识别
- **多样性优化**: 防止结果中的主题聚集
- **来源可信度**: 权衡知名新闻机构

## 🏗️ 系统架构详解

### 微服务设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   负载均衡器     │    │   API 网关      │    │   重排核心      │
│                 │────│                 │────│                 │
│ (Nginx/HAProxy) │    │  (限流控制)     │    │  (Go 服务)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                                        │
                       ┌────────────────────────────────┼────────────────────────────────┐
                       │                                │                                │
              ┌─────────▼─────────┐           ┌─────────▼─────────┐           ┌─────────▼─────────┐
              │   分词器          │           │   模型服务器      │           │   配置管理器      │
              │  (文本处理)       │           │  (ML 推理)       │           │  (动态配置)       │
              └───────────────────┘           └───────────────────┘           └───────────────────┘
```

### 并发处理模型

```go
// 使用错误组进行并发查询处理
func (s *RerankService) processRanking(span *span.Span) ([]*bean.QueryData, error) {
    resultMap := concurrent_map.New[int, []*map[string]any]()

    ctx, cancel := context.WithCancel(s.Ctx.GetContext())
    defer cancel()

    var eg errgroup.Group
    for i, data := range s.Req.Payload.Data {
        i, data := i, data // 捕获循环变量
        eg.Go(func() error {
            select {
            case <-ctx.Done():
                return nil
            default:
                result, err := s.performRanking(span, s.Req.Payload.Model, data)
                if err != nil {
                    cancel() // 出错时取消其他协程
                    return err
                }
                resultMap.Set(i, result)
                return nil
            }
        })
    }

    if err := eg.Wait(); err != nil {
        return nil, err
    }

    return s.collectResults(resultMap), nil
}
```

## 🔧 高级配置

### 环境变量

```bash
# 服务配置
export RERANK_PORT=50903
export RERANK_LOG_LEVEL=info
export RERANK_MAX_WORKERS=100

# 模型配置
export TOKENIZER_PATH="./resource/tokenizer"
export MODEL_SERVER_URL="http://model-server:8080"
export MODEL_TIMEOUT=2000

# 性能调优
export GOMAXPROCS=8
export GOGC=100
export GOMEMLIMIT=2GiB
```

### 生产环境配置

```toml
# config/production/rerank.toml
[server]
port = 50903
read_timeout = "30s"
write_timeout = "30s"
max_header_bytes = 1048576

[performance]
max_concurrent_requests = 1000
worker_pool_size = 100
request_timeout = "10s"
circuit_breaker_threshold = 0.1

[monitoring]
metrics_enabled = true
tracing_enabled = true
log_level = "info"
health_check_interval = "30s"

[[algorithms]]
name = "medical_v0516"
enabled = true
weight = 1.0
timeout = "5s"

[[algorithms]]
name = "sports"
enabled = true
weight = 0.8
timeout = "3s"
```

## 🧪 测试策略

### 单元测试

```go
func TestMedicalRerank(t *testing.T) {
    rerank := &MedicalRerank{}

    testCases := []struct {
        name     string
        query    string
        docs     []*Document
        expected float64
    }{
        {
            name:  "高相关性医疗查询",
            query: "糖尿病症状",
            docs: []*Document{
                {Title: "了解糖尿病症状", Authority: 8},
            },
            expected: 0.95,
        },
    }

    for _, tc := range testCases {
        t.Run(tc.name, func(t *testing.T) {
            result, err := rerank.Rank(context.Background(), tc.query, tc.docs)
            assert.NoError(t, err)
            assert.GreaterOrEqual(t, result[0].Score, tc.expected)
        })
    }
}
```

### 集成测试

```go
func TestRerankAPI(t *testing.T) {
    server := setupTestServer()
    defer server.Close()

    payload := RerankRequest{
        Header: Header{TraceID: "test-123"},
        Payload: Payload{
            Model: "medical_v0516",
            Data: []QueryData{
                {
                    Query: "心脏病治疗",
                    Docs:  generateTestDocs(10),
                },
            },
        },
    }

    resp, err := http.Post(server.URL+"/rerank/api/v2", "application/json",
                          bytes.NewBuffer(marshal(payload)))
    assert.NoError(t, err)
    assert.Equal(t, http.StatusOK, resp.StatusCode)

    var result RerankResponse
    json.NewDecoder(resp.Body).Decode(&result)
    assert.Equal(t, 0, result.Header.Code)
    assert.NotEmpty(t, result.Payload.Result)
}
```

## 🤝 参与贡献

我们欢迎社区贡献！请查看我们的[贡献指南](CONTRIBUTING.md)了解详情。

### 开发流程

1. Fork 本仓库
2. 创建特性分支: `git checkout -b feature/amazing-feature`
3. 提交更改并添加测试
4. 确保测试通过: `go test ./...`
5. 提交代码: `git commit -m 'Add amazing feature'`
6. 推送分支: `git push origin feature/amazing-feature`
7. 创建 Pull Request

### 代码规范

- 遵循 [Go 代码审查评论](https://github.com/golang/go/wiki/CodeReviewComments)
- 使用 `gofmt` 格式化代码
- 为新功能添加测试
- 及时更新文档

## 🚀 生产部署

### Kubernetes 部署

```yaml
# k8s/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: rerank-service
  labels:
    app: rerank-service
spec:
  replicas: 3
  selector:
    matchLabels:
      app: rerank-service
  template:
    metadata:
      labels:
        app: rerank-service
    spec:
      containers:
      - name: rerank-service
        image: rerank-service:latest
        ports:
        - containerPort: 50903
        env:
        - name: RERANK_PORT
          value: "50903"
        - name: GOMAXPROCS
          value: "4"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /actuator/prometheus
            port: 50903
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /actuator/prometheus
            port: 50903
          initialDelaySeconds: 5
          periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: rerank-service
spec:
  selector:
    app: rerank-service
  ports:
  - protocol: TCP
    port: 80
    targetPort: 50903
  type: ClusterIP
```

### Docker Compose

```yaml
# docker-compose.yml
version: '3.8'
services:
  rerank-service:
    build:
      context: .
      dockerfile: cicd/rerank.dockerfile
    ports:
      - "50903:50903"
    environment:
      - RERANK_PORT=50903
      - RERANK_LOG_LEVEL=info
      - GOMAXPROCS=4
    volumes:
      - ./config:/app/config:ro
      - ./resource:/app/resource:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:50903/actuator/prometheus"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
```

## 🔍 故障排查

### 常见问题

#### 内存使用过高
```bash
# 检查内存使用
docker stats rerank-service

# 分析堆内存
go tool pprof http://localhost:50903/debug/pprof/heap

# 调优垃圾回收器
export GOGC=50  # 更激进的 GC
export GOMEMLIMIT=1GiB  # 设置内存限制
```

#### 延迟过高
```bash
# 检查 CPU 性能分析
go tool pprof http://localhost:50903/debug/pprof/profile

# 监控协程
go tool pprof http://localhost:50903/debug/pprof/goroutine

# 调优工作池
# 在 config/rerank/rerank.toml 中
[[ants_pools]]
goroutine_num = 4096  # 增加工作池大小
```

#### 服务启动问题
```bash
# 检查配置
./rerank-server server -c ./config/rerank --dry-run

# 验证依赖服务
curl -f http://tokenizer-service:8080/health
curl -f http://model-server:8080/health

# 查看日志
tail -f /var/log/rerank/service.log
```

### 性能调优

#### Go 运行时优化
```bash
# 设置最优 GOMAXPROCS
export GOMAXPROCS=$(nproc)

# 调优垃圾回收器
export GOGC=100
export GOMEMLIMIT=2GiB

# 启用 CPU 性能分析
export GODEBUG=gctrace=1
```

#### 算法专用调优
```toml
# 医疗领域优化
[medical]
enable_entity_recognition = true
authority_weight = 0.4
recency_weight = 0.3

# 体育领域优化
[sports]
enable_live_events = true
time_decay_factor = 0.1
diversity_threshold = 0.8

# 新闻领域优化
[news]
enable_breaking_news = true
source_credibility_weight = 0.5
diversity_window = 5
```

## 📊 监控与可观测性

### Prometheus 指标

```yaml
# monitoring/prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'rerank-service'
    static_configs:
      - targets: ['rerank-service:50903']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 5s
```

### Grafana 仪表板

关键监控指标：
- **请求速率**: `rate(rerank_requests_total[5m])`
- **错误率**: `rate(rerank_errors_total[5m]) / rate(rerank_requests_total[5m])`
- **延迟 P99**: `histogram_quantile(0.99, rate(rerank_duration_seconds_bucket[5m]))`
- **内存使用**: `process_resident_memory_bytes`
- **协程数量**: `go_goroutines`

### 告警规则

```yaml
# alerts/rerank.yml
groups:
- name: rerank-service
  rules:
  - alert: RerankHighErrorRate
    expr: rate(rerank_errors_total[5m]) / rate(rerank_requests_total[5m]) > 0.05
    for: 2m
    labels:
      severity: warning
    annotations:
      summary: "重排服务错误率过高"
      description: "错误率为 {{ $value | humanizePercentage }}"

  - alert: RerankHighLatency
    expr: histogram_quantile(0.99, rate(rerank_duration_seconds_bucket[5m])) > 0.5
    for: 3m
    labels:
      severity: warning
    annotations:
      summary: "重排服务延迟过高"
      description: "P99 延迟为 {{ $value }}s"
```

## 🌟 发展路线图

### 短期目标 (2025年第一季度)
- [ ] **多语言支持** - 添加英语和其他语言支持
- [ ] **缓存层** - 实现基于 Redis 的结果缓存
- [ ] **A/B 测试框架** - 内置实验平台
- [ ] **实时模型更新** - 热交换重排模型

### 中期目标 (2025年第二、三季度)
- [ ] **机器学习流水线** - 自动化模型训练和部署
- [ ] **联邦学习** - 隐私保护的模型更新
- [ ] **基于图的重排** - 知识图谱集成
- [ ] **个性化** - 用户特定的重排偏好

### 长期目标 (2025年第四季度及以后)
- [ ] **神经重排模型** - 基于深度学习的重排
- [ ] **多模态支持** - 图像和视频内容重排
- [ ] **边缘计算** - 边缘节点分布式重排
- [ ] **AutoML 集成** - 自动化算法选择

## 🤝 社区

### 贡献指南

我们欢迎社区的贡献！以下是您可以帮助的方式：

#### 🐛 错误报告
- 使用[错误报告模板](.github/ISSUE_TEMPLATE/bug_report.md)
- 包含重现步骤和环境详情
- 添加相关日志和错误信息

#### ✨ 功能请求
- 使用[功能请求模板](.github/ISSUE_TEMPLATE/feature_request.md)
- 描述用例和预期行为
- 考虑实现复杂性和影响

#### 🔧 代码贡献
- Fork 仓库并创建功能分支
- 遵循[编码标准](CONTRIBUTING.md#coding-standards)
- 为新功能添加测试
- 根据需要更新文档
- 提交带有清晰描述的拉取请求

### 行为准则

本项目遵循[贡献者公约行为准则](CODE_OF_CONDUCT.md)。参与时，您需要遵守此准则。

### 社区渠道

- 💬 **QQ群**: [加入我们的QQ群](https://qm.qq.com/rerank-service)
- 📧 **邮件列表**: [<EMAIL>](mailto:<EMAIL>)
- 🐦 **微博**: [@重排服务](https://weibo.com/RerankService)
- 📺 **B站**: [重排服务频道](https://space.bilibili.com/RerankService)

## 📄 开源许可

本项目采用 Apache License 2.0 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

- [Gin](https://github.com/gin-gonic/gin) - HTTP Web 框架
- [Jieba](https://github.com/yanyiwu/gojieba) - 中文分词
- [Sonic](https://github.com/bytedance/sonic) - 高性能 JSON 库
- [errgroup](https://pkg.go.dev/golang.org/x/sync/errgroup) - 并发错误处理
- [Prometheus](https://prometheus.io/) - 监控和告警
- [Grafana](https://grafana.com/) - 可观测性平台

## 📞 技术支持

- 📧 **邮箱**: [<EMAIL>](mailto:<EMAIL>)
- 💬 **问题反馈**: [GitHub Issues](https://github.com/your-org/rerank-service/issues)
- 📖 **文档**: [项目 Wiki](https://github.com/your-org/rerank-service/wiki)
- 🆘 **安全问题**: [<EMAIL>](mailto:<EMAIL>)

---

<p align="center">
  <strong>⭐ 如果这个项目对您有帮助，请给我们一个 Star！⭐</strong>
</p>

<p align="center">
  用 ❤️ 构建，来自重排服务社区
</p>
