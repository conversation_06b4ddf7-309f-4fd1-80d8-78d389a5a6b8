package post_func

var g_funcs = map[string]func(out map[string][]byte) map[string][]byte{}

const HeaderPostFuncName = "model_infer_post_func"

func init() {
	//完成注册
	g_funcs["default"] = defaultFunc
}

func defaultFunc(out map[string][]byte) map[string][]byte {
	return out
}

func GetPostFunc(name string) (func(out map[string][]byte) map[string][]byte, bool) {
	f, ok := g_funcs[name]
	return f, ok
}
