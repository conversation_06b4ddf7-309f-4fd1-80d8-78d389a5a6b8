package modelserver

import (
	"fmt"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/model-server/post_func"
	proto_modelserver "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/model-server"
	onnx_runtime "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/onnx-modelserver/runtime"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
	"github.com/gin-gonic/gin"
)

type modelServer struct {
	onnxInst *onnx_runtime.OnnxRuntime
}

func (r *modelServer) handler(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_modelserver.ReqPayload]) *pandora_proto.PandoraResponseMessage[proto_modelserver.RespPayload] {
	resp := proto_modelserver.ModelServerProto.NewPandoraResponseMessage()

	resMap, err := r.onnxInst.Infer(ctx.GetContext(), &req.Payload.InferReq)
	if err != nil {
		return fillHeader(resp, selferrors.ModelServerError_InferFailed.Detaild(err.Error()))
	}

	//增加后处理逻辑
	postFuncName := ctx.GetGinCtx().Request.Header.Get(post_func.HeaderPostFuncName)

	if postF, ok := post_func.GetPostFunc(postFuncName); ok {
		resp.Payload.Outputs = postF(resp.Payload.Outputs)
	}

	resp.Payload.InferResp = *resMap
	return resp
}

func fillHeader(resp *pandora_proto.PandoraResponseMessage[proto_modelserver.RespPayload], err *errtypes.SelfError) *pandora_proto.PandoraResponseMessage[proto_modelserver.RespPayload] {
	resp.Header.Code = err.Code()
	resp.Header.Success = err.Error()
	return resp
}

// Init 服务初始化
func Init() error {
	// 添加自定义配置
	if err := goboot.RegisterCustomMultiModule(OnnxFactory); err != nil {
		panic(fmt.Sprintf("register custom module failed, error: %s", err.Error()))
	}

	bootModule := goboot.GetCustomModule(OnnxFactory)

	// 检查自定义配置是否正确加载
	if bootModule.Need() != nil {
		return nil
	}

	configs := bootModule.GetAllConfig()
	for _, config := range configs {
		service := &modelServer{
			onnxInst: bootModule.GetInstance(config.UName),
		}

		fmt.Println("model server:" + "/" + config.Name())
		//注册服务
		goboot.HttpServer().DefaultInstance().Router.POST("/"+config.Name(), proto_modelserver.ModelServerProto.GinWrapper().SetHandler(service.handler).HandlerFunc())
	}

	goboot.HttpServer().DefaultInstance().Router.GET("/models", func(ctx *gin.Context) {
		configs := bootModule.GetAllConfig()
		ctx.JSON(200, configs)
		return
	})

	return nil
}
