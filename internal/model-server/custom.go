package modelserver

import (
	onnx_conf "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/onnx-modelserver/conf"
	onnx_runtime "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/onnx-modelserver/runtime"
	base_factory "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/factory"
	base_model "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/model"
)

const TomlModel = "onnxruntime"

var OnnxFactory = base_factory.NewFactory(TomlModel, NewOnnxInst)

// OnnxConfig 模型配置
type OnnxConfig struct {
	base_model.BaseModel
	onnx_conf.OnnxConfig
}

// NewOnnxInst 创建模型实例
func NewOnnxInst(option *OnnxConfig) (*onnx_runtime.OnnxRuntime, error) {
	return onnx_runtime.NewRuntime(&option.OnnxConfig)
}
