package service

import (
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/service/stage"
	proto_seg "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/seg"
	pandora_span "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

var QueryParser = NewQueryParser()

type queryParser struct {
	stages []quStage
}

type quStage interface {
	Exec(ptcx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_seg.SegAPIV2QueryRequestPayLoad], detail []*bean.QUDetail, mod int, span *pandora_span.Span) (Err *errtypes.SelfError)
	GetName() string
}

func NewQueryParser() *queryParser {
	return &queryParser{
		stages: []quStage{
			new(stage.Preprocess),
			new(stage.TextSeg),
			new(stage.TermWeight),
			new(stage.Synonym),
			// new(stage.Postprocess),
		},
	}
}

func (parser *queryParser) Run(ptcx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_seg.SegAPIV2QueryRequestPayLoad], details []*bean.QUDetail, mod int, span *pandora_span.Span) ([]*bean.QUDetail, *errtypes.SelfError) {

	for _, stage := range parser.stages {

		err := stage.Exec(ptcx, req, details, mod, span)

		if err != nil {

			return details, err
		}
	}

	return details, nil
}
