package stage

import (
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/service/utils"
	proto_seg "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/seg"
	pandora_span "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

type Synonym struct {
}

func (s *Synonym) Exec(ptcx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_seg.SegAPIV2QueryRequestPayLoad], details []*bean.QUDetail, mod int, span *pandora_span.Span) (Err *errtypes.SelfError) {
	opSpan := span.AddSpan("同义词")
	defer opSpan.Finish()

	if !req.Payload.OpenSynonyms {
		return nil
	}
	for _, detail := range details {
		for i := 0; i < len(detail.Parsed.Words); i++ {
			value, ok := utils.SynonymDict.Get(detail.Parsed.Words[i].Word)
			if ok && len(value) > 0 {
				detail.Parsed.Words[i].Synonyms = value
			}
		}
	}
	return nil
}

func (s *Synonym) GetName() string {
	return "synonym"
}
