package stage

import (
	"fmt"
	"math"
	"strings"
	"sync"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/global"
	proto_seg "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/seg"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	pandora_span "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	util "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/data_tool"
	aseclient "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/ase_sdk/client"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
	tokenizer_wrapper "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/tokenizer_v2_1"
)

type TermWeight struct {
}

func (s *TermWeight) GetName() string {
	return "termweight"
}

func (s *TermWeight) assembleToken(terms [][]string) (map[string][]byte, map[string][]int64, error) {
	tokenMap := make(map[string][]byte)
	shapeMap := make(map[string][]int64)
	lengthMax := 0
	batchSize := len(terms)
	queries := make([]string, batchSize)
	termsLength := make([][]int64, batchSize)
	var encodings3 = make([]*tokenizer_wrapper.Encoding[int64], 0, len(terms))

	for i, term := range terms {

		lengthMax = max(lengthMax, len(term))
		termsLength[i] = make([]int64, len(term))
		queries[i] = strings.Join(term, "")

		tokenModel := tokenizer_wrapper.NewTokenizerEncodingWithAsync[int64](global.IFLYS_TokenModelServer)
		tokenSample := tokenizer_wrapper.NewTokenizerEncoding[int64](global.IFLYS_SAMPLE_Tokenizer)
		encodings, _ := tokenSample.EncodeBatch(term, false)
		encodings2, _ := tokenModel.AsyncEncodeBatch(term, false)

		for j, encoding := range encodings {
			termsLength[i][j] = int64(len(encoding.Ids))
		}
		en := &tokenizer_wrapper.Encoding[int64]{}
		en.Ids = append(en.Ids, global.CLS)
		en.Types = append(en.Types, global.TYPES)
		en.Masks = append(en.Masks, global.MASK)
		for _, encoding := range encodings2 {
			for i, _ := range encoding.Ids {
				if encoding.Ids[i] > 0 {
					en.Ids = append(en.Ids, encoding.Ids[i])
					en.Masks = append(en.Masks, encoding.Masks[i])
					en.Types = append(en.Types, encoding.Types[i])
				}
			}
		}
		en.Ids = append(en.Ids, global.SEP)
		en.Types = append(en.Types, global.TYPES)
		en.Masks = append(en.Masks, global.MASK)
		encodings3 = append(encodings3, en)
	}

	// 构造term_t_lens
	termsTLens := make([][]int64, batchSize)
	for i := 0; i < batchSize; i++ {
		termsTLens[i] = make([]int64, lengthMax)
		for j := 0; j < lengthMax; j++ {
			termsTLens[i][j] = 1
		}
	}

	for i, lens := range termsLength {
		copy(termsTLens[i][:len(lens)], lens)
	}

	ids := make([][]int64, len(encodings3))
	for i, encoding := range encodings3 {
		ids[i] = encoding.Ids
	}
	seqLength := len(ids[0])

	// 构造terms_mask
	termsMask := make([][][]float32, batchSize)
	for i := 0; i < batchSize; i++ {
		termsMask[i] = make([][]float32, lengthMax)
		for j := 0; j < lengthMax; j++ {
			termsMask[i][j] = make([]float32, seqLength)
		}
	}

	for i := 0; i < batchSize; i++ {
		lens := termsLength[i]
		var start int64 = 1
		for j, l := range lens {
			for k := start; k < start+l; k++ {
				termsMask[i][j][k] = 1
			}
			start += l
		}
	}

	for i, encoding := range encodings3 {
		termsMaskFlat := make([]float32, 0)
		for _, row := range termsMask[i] {
			termsMaskFlat = append(termsMaskFlat, row...)
		}
		tokenMap["input_ids"] = append(tokenMap["input_ids"], util.SliceToBytes(encoding.Ids)...)
		tokenMap["input_mask"] = append(tokenMap["attention_mask"], util.SliceToBytes(encoding.Masks)...)
		tokenMap["token_type_ids"] = append(tokenMap["token_type_ids"], util.SliceToBytes(encoding.Types)...)
		tokenMap["terms_t_lens"] = append(tokenMap["terms_t_lens"], util.SliceToBytes(termsTLens[i])...)
		tokenMap["terms_mask"] = append(tokenMap["terms_mask"], util.SliceToBytes(termsMaskFlat)...)
	}

	_batchSize := common.Interface2I64(len(queries))
	shapeMap["input_ids"] = []int64{_batchSize, int64(len(encodings3[0].Ids))}
	shapeMap["input_mask"] = []int64{_batchSize, int64(len(encodings3[0].Masks))}
	shapeMap["token_type_ids"] = []int64{_batchSize, int64(len(encodings3[0].Types))}
	shapeMap["terms_t_lens"] = []int64{_batchSize, int64(len(termsTLens[0]))}
	shapeMap["terms_mask"] = []int64{_batchSize, int64(lengthMax), int64(len(encodings3[0].Ids))}
	return tokenMap, shapeMap, nil
}

func (s *TermWeight) Exec(ptcx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_seg.SegAPIV2QueryRequestPayLoad], details []*bean.QUDetail, mod int, span *pandora_span.Span) *errtypes.SelfError {
	opSpan := span.AddSpan("权重计算")
	defer opSpan.Finish()
	// 判断是否要计算权重
	if !req.Payload.OpenWeight {
		return nil
	}

	var Err *errtypes.SelfError
	var wg sync.WaitGroup
	wg.Add(len(details))
	for _, detail := range details {
		go func() {
			defer wg.Done()
			words := []string{}
			for _, word := range detail.Parsed.Words {
				words = append(words, word.Word)
			}
			terms := [][]string{words}
			// tokenizer
			tokenMap, shapeMap, err := s.assembleToken(terms)
			if err != nil {
				Err = selferrors.SegError_TokenizerError.Detaild(err.Error())
				return
			}
			// 调用模型推理
			client := goboot.AseSdk().GetInstance(global.TermWeightVersion).Request().
				SetTraceId(req.Header.TraceId).
				SetParentSpan(opSpan).
				SetHeaderTag(req.Header.Tag)
			resp, err := client.Send(ptcx.GetContext(), &aseclient.InferReq{
				Inputs: tokenMap,
				Shapes: shapeMap,
			})
			if err != nil {
				goboot.Logger().DefaultInstance().Error(err.Error())
				Err = selferrors.SegError_InferError.Detaild(err.Error())
				return
			}
			if resp != nil && resp.Header.Code != 0 {
				msg := fmt.Sprintf("code: %v, message: %s", resp.Header.Code, resp.Header.Success)
				goboot.Logger().DefaultInstance().Error(msg)
				Err = selferrors.SegError_InferError.Detaild(msg)
				return
			}
			outputs := util.BytesToSlice[float32](resp.Payload.Outputs["term_weights"])
			termWeights, _ := common.Normalize(outputs)

			weightMap := map[string]float32{}
			for i := 0; i < len(words); i++ {
				if math.IsNaN(float64(termWeights[i])) {
					weightMap[words[i]] = -2
				} else {
					weightMap[words[i]] = termWeights[i]
				}
			}
			for _, word := range detail.Parsed.Words {
				if _, ok := weightMap[word.Word]; ok {
					word.Weight = weightMap[word.Word]
				}
			}

			// 删除detail.Parsed.Words中word.Weight=-2的word
			var filteredWords []*proto_seg.ParsedWord
			for _, word := range detail.Parsed.Words {
				if word.Weight != -2 {
					filteredWords = append(filteredWords, word)
				}
			}
			detail.Parsed.Words = filteredWords

		}()
	}
	wg.Wait()

	return Err
}
