package stage

import (
	"regexp"
	"strings"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/common"
	proto_seg "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/seg"
	pandora_span "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
	"github.com/liuzl/gocc"
)

// var puncReg = regexp.MustCompile("[，_《。》、？；：‘’＂“”【「】」！@￥…（）—,<>/?;:'\"[]{}~`!@#$^&%%*()-=+]")

var puncReg = regexp.MustCompile("[!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~，《。》、？；￥…（）]+")

// var emojiReg = regexp.MustCompile(`[\x{1F300}-\x{1F64F}\x{1F680}-\x{1F6FF}\x{2600}-\x{26FF}\x{2700}-\x{27BF}]+`)
var emojiReg2 = regexp.MustCompile(`[\x{1F600}-\x{1F64F}]|[\x{1F300}-\x{1F5FF}]|[\x{1F680}-\x{1F6FF}]|[\x{2600}-\x{26FF}]|[\x{2700}-\x{27BF}]|[\x{FE00}-\x{FE0F}]|[\x{1F1E6}-\x{1F1FF}]|[\x{1F200}-\x{1F2FF}]|[\x{1F900}-\x{1F9FF}]`)

var zeroWidthCharReg = regexp.MustCompile(`[\x{200B}-\x{200F}\x{202A}-\x{202E}\x{2060}-\x{206F}\x{FEFF}]`)
var t2sConverter = initT2SConverter()

func initT2SConverter() *gocc.OpenCC {
	t2s, err := gocc.New("t2s")
	if err != nil {
		panic(err)
	}
	return t2s
}

// 预处理
type Preprocess struct {
}

func (s *Preprocess) Exec(ptcx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_seg.SegAPIV2QueryRequestPayLoad], details []*bean.QUDetail, mod int, span *pandora_span.Span) (Err *errtypes.SelfError) {
	opSpan := span.AddSpan("预处理")
	defer opSpan.Finish()

	for _, detail := range details {
		query := detail.Query
		// 全角转半角
		query = common.ConvertFullWidthToHalfWidth(query)
		//去除特殊符号
		cleanText := puncReg.ReplaceAllString(query, " ")
		cleanText = strings.TrimSpace(cleanText)
		cleanText = emojiReg2.ReplaceAllString(cleanText, "")
		cleanText = zeroWidthCharReg.ReplaceAllString(cleanText, "")
		if cleanText == "" {
			detail.Parsed.Query = ""
			return selferrors.SegError_SegReqPreEmptyError
		}
		//繁体转简体
		out, err := t2sConverter.Convert(cleanText)
		if err != nil {
			return selferrors.SegError_SegDataTransferError
		}
		cleanText = out
		//大小转小写
		detail.Parsed.Query = strings.ToLower(cleanText)
		detail.Query = detail.Parsed.Query
	}
	return
}

func (s *Preprocess) GetName() string {
	return "preprocess"
}
