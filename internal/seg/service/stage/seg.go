package stage

import (
	"net/http"
	"strings"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/global"
	proto_seg "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/seg"
	pandora_span "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

type TextSeg struct {
	client *http.Client
}

func (s *TextSeg) Exec(ptcx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_seg.SegAPIV2QueryRequestPayLoad], details []*bean.QUDetail, mod int, span *pandora_span.Span) (Err *errtypes.SelfError) {
	opSpan := span.AddSpan("分词计算")
	defer opSpan.Finish()

	Err = s.seg_with_jieba(req, details, mod)
	return Err
}

func (s *TextSeg) GetName() string {
	// return "文本分词"
	return "seg"
}

func (s *TextSeg) seg_with_jieba(req *pandora_proto.PandoraRequestMessage[proto_seg.SegAPIV2QueryRequestPayLoad], details []*bean.QUDetail, mod int) (Err *errtypes.SelfError) {
	// 获取query
	for _, detail := range details {
		text := detail.Parsed.Query
		nounCnt := 0
		// jieba分词
		words := global.IFLYS_SEG_JIEBA.Cut(text, true)
		parsedWords := make([]*proto_seg.ParsedWord, 0)
		for _, word := range words {
			word = strings.TrimSpace(word)
			if len(word) == 0 {
				continue
			}
			tag, ok := config.JiebaPos.Get(word)
			if !ok {
				tag = "x"
			}
			parsedWords = append(parsedWords, &proto_seg.ParsedWord{
				Word:     word,
				Tag:      tag,
				Weight:   -1,
				Synonyms: []string{},
			})
			if _, ok := global.NounTags[tag]; ok {
				nounCnt++
			}
		}
		nounRatio := float32(0)
		if len(parsedWords) > 0 {
			nounRatio = float32(nounCnt) / float32(len(parsedWords))
		}
		detail.Parsed = &proto_seg.ParsedDetail{
			Query:     text,
			Words:     parsedWords,
			NounRatio: nounRatio,
		}
	}
	return
}
