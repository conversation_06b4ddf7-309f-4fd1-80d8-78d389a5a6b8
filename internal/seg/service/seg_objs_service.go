package service

import (
	"fmt"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/common"
	proto_seg "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/seg"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

type ObjsParseService struct {
	Ctx  *pandora_context.PandoraContext
	Resp *pandora_proto.PandoraResponseMessage[proto_seg.SegAPIV2ObjsResponsePayLoad]
	Req  *pandora_proto.PandoraRequestMessage[proto_seg.SegAPIV2ObjsRequestPayLoad]
}

func (s *ObjsParseService) checkRequest() error {

	if s.Req.Header.TraceId == "" {
		err := selferrors.CommonError_MissingField.Detaild("TraceId为空")
		s.Resp.Header.Code = err.Code()
		s.Resp.Header.Success = err.String()
		return err
	}
	if len(s.Req.Payload.Data) == 0 {
		err := selferrors.SegError_SegReqEmptyError.Detaild("请求数据为空")
		s.Resp.Header.Code = err.Code()
		s.Resp.Header.Success = err.String()
		s.Resp.Header.TraceId = s.Req.Header.TraceId
		s.Resp.Payload.Data = make([]*map[string]any, len(s.Req.Payload.Data))
		s.Resp.Payload.Mod = 4
		return err
	}
	return nil
}

// TODO:思考一下可并行，目前不是性能瓶颈，优先提测，后续优化
func (s *ObjsParseService) parse() {
	err := s.checkRequest()
	if err != nil {
		return
	}
	dataResult := make([]*map[string]any, 0)
	for _, data := range s.Req.Payload.Data {

		resultMap := make(map[string]any)
		var cutResult string
		var id_exist bool = false

		for fieldName, text := range *data {
			if fieldName == "id" {
				switch text.(type) {
				case int, int32, int64, float32, float64:
					resultMap[fieldName] = fmt.Sprintf("%d", common.Interface2I64(text))
					id_exist = true
					continue
				default:
					s.Resp.Header.Code = selferrors.SegError_SegReqParserError.Code()
					s.Resp.Header.Success = selferrors.SegError_SegReqParserError.Error()
					s.Resp.Header.TraceId = s.Req.Header.TraceId
					s.Resp.Payload.Data = make([]*map[string]any, 0)
					s.Resp.Payload.Mod = 4
					return
				}
			}

			r, ok := text.(string)
			if !ok {
				s.Resp.Header.Code = selferrors.SegError_SegReqParserError.Code()
				s.Resp.Header.Success = selferrors.SegError_SegReqParserError.Error()
				s.Resp.Header.TraceId = s.Req.Header.TraceId
				s.Resp.Payload.Data = make([]*map[string]any, 0)
				s.Resp.Payload.Mod = 4
				return
			}
			cutResult, _, _ = segInst.seg(r, 4)
			resultMap[fieldName] = cutResult
		}

		if !id_exist {
			s.Resp.Header.Code = selferrors.SegError_SegReqParserError.Code()
			s.Resp.Header.Success = selferrors.SegError_SegReqParserError.Error()
			s.Resp.Header.TraceId = s.Req.Header.TraceId
			s.Resp.Payload.Data = make([]*map[string]any, 0)
			s.Resp.Payload.Mod = 4
			return
		}

		dataResult = append(dataResult, &resultMap)
	}
	s.Resp.Header.TraceId = s.Req.Header.TraceId
	s.Resp.Payload.Data = dataResult
	s.Resp.Payload.Mod = 4
	return
}

func (s *ObjsParseService) SegObjsHandler() {
	nspan := s.Ctx.RootSpan().AddSpan("执行数据分词服务")
	defer nspan.Finish()
	s.parse()
}
