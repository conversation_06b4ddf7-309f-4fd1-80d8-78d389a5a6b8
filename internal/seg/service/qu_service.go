package service

import (
	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/config"
	proto_seg "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/seg"
	pandora_span "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

type QueryParseService struct {
	Ctx *pandora_context.PandoraContext

	Resp *pandora_proto.PandoraResponseMessage[proto_seg.SegAPIV2QueryResponsePayLoad]
	Req  *pandora_proto.PandoraRequestMessage[proto_seg.SegAPIV2QueryRequestPayLoad]
}

func (s *QueryParseService) checkRequest(span *pandora_span.Span) error {
	opSpan := span.AddSpan("参数检查")
	defer opSpan.Finish()
	if s.Req.Header.TraceId == "" {
		err := selferrors.CommonError_MissingField.Detaild("TraceId为空")
		s.Resp.Header.Code = err.Code()
		s.Resp.Header.Success = err.String()
		return err
	}
	if len(s.Req.Payload.Query) == 0 || len(s.Req.Payload.Query[0]) == 0 {
		err := selferrors.CommonError_MissingField.Detaild("请求query为空")
		s.Resp.Header.Code = err.Code()
		s.Resp.Header.Success = err.String()
		return err
	}
	return nil
}

func (s *QueryParseService) parse() {
	// 服务耗时统计
	span := s.Ctx.RootSpan().AddSpan("执行query分词操作")
	defer span.Finish()

	span.TraceInfo("request", s.Req)

	err := s.checkRequest(span)
	if err != nil {
		return
	}

	var results []*proto_seg.ParsedDetail

	results = make([]*proto_seg.ParsedDetail, 0)

	quDetails := make([]*bean.QUDetail, 0, len(s.Req.Payload.Query))
	for i := 0; i < len(s.Req.Payload.Query); i++ {
		var query = s.Req.Payload.Query[i]
		if config.G_InterveneModule.DefaultInstance().CutLength > 0 && len(s.Req.Payload.Query[i]) > config.G_InterveneModule.DefaultInstance().CutLength {
			query = common.TruncateStringByChars(s.Req.Payload.Query[i], config.G_InterveneModule.DefaultInstance().CutLength)
		}
		quDetails = append(quDetails, &bean.QUDetail{
			Query:  query,
			Parsed: &proto_seg.ParsedDetail{},
		})
	}

	details, Err := QueryParser.Run(s.Ctx, s.Req, quDetails, 0, span)

	for i := 0; i < len(details); i++ {
		results = append(results, details[i].Parsed)
	}

	if Err != nil {
		s.Resp.Header.Code = Err.Code()
		s.Resp.Header.Success = Err.String()
		s.Resp.Header.TraceId = s.Req.Header.TraceId
		s.Resp.Payload.Results = results

	} else {
		s.Resp.Header.TraceId = s.Req.Header.TraceId
		s.Resp.Payload.Results = results
	}
	span.TraceInfo("response", s.Resp)
	return

}

func (s *QueryParseService) SegQueryHandler() {
	s.parse()
}
