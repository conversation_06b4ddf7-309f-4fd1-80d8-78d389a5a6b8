package service

import (
	"strings"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/global"
)

type Seg struct {
}

func (s *Seg) seg(text string, mod int) (string, string, string) {
	if strings.TrimSpace(text) == "" {
		return text, "", ""
	}
	switch mod {
	case global.JiebaCut:
		return strings.Join(global.IFLYS_SEG_JIEBA.Cut(text, true), " "), "", ""
	case global.JiebaCutAll:
		return strings.Join(global.IFLYS_SEG_JIEBA.CutAll(text), " "), "", ""
	// case global.JiebaCutPos:
	// 	segRes := jiebaInst.Pos(text, false)
	// 	// fmt.Println(segRes)
	// 	cutResult := common.MapAndFilter(
	// 		segRes,
	// 		func(segPos seg.SegPos) string {
	// 			return segPos.Text
	// 		},
	// 		func(segPos seg.SegPos) bool {
	// 			return strings.TrimSpace(segPos.Text) != ""
	// 		},
	// 	)
	// 	tagsResult := common.MapAndFilter(
	// 		segRes,
	// 		func(segPos seg.SegPos) string {
	// 			return segPos.Pos
	// 		},
	// 		func(segPos seg.SegPos) bool {
	// 			return strings.TrimSpace(segPos.Text) != ""
	// 		},
	// 	)
	// 	return strings.Join(cutResult, " "), strings.Join(tagsResult, " "), ""
	default:
		return strings.Join(global.IFLYS_SEG_JIEBA.CutForSearch(text, true), " "), "", ""
	}
}
