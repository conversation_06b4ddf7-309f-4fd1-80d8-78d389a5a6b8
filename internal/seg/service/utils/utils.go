package utils

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"github.com/spf13/cast"
)

func ToInt64Slice(data, sep string) []int64 {
	slice := strings.Split(data, sep)
	var result []int64
	for _, v := range slice {
		result = append(result, cast.ToInt64(v))
	}
	return result
}

func IntContain(src int, dst []int) bool {
	for _, eachItem := range dst {
		if eachItem == src {
			return true
		}
	}
	return false
}

func Filter[T any](slice []T, predicate func(index int, item T) bool) []T {
	filtered := make([]T, 0)
	for index, item := range slice {
		if predicate(index, item) {
			filtered = append(filtered, item)
		}
	}
	return filtered
}

func Map[T any, C any](slice []T, mapper func(index int, item T) C) []C {
	mapped := make([]C, len(slice))
	for index, item := range slice {
		mapped[index] = mapper(index, item)
	}
	return mapped
}

func MaxScore[T any](slice []T, score func(i int, a T) float64) T {
	max := slice[0]
	maIndex := 0
	for i, item := range slice {
		if score(i, item) > score(maIndex, max) {
			max = item
			maIndex = i
		}
	}
	return max
}

func If[T any](condition bool, trueVal, falseVal T) T {
	if condition {
		return trueVal
	}
	return falseVal
}

func Interface2S(value interface{}) (result string) {
	if value == nil {
		return ""
	}

	switch value := value.(type) {

	case []byte: // 取缔 []uint8 --> string
		result = string(value)

	case int64:
		result = strconv.FormatInt(value, 10)

	case float64:
		result = strconv.FormatFloat(value, 'f', -1, 64)

	case json.Number:
		result = value.String()

	case string:
		result = value

	default:
		newValue, _ := json.Marshal(value)
		result = string(newValue)
	}

	return
}

func Interface2I64(value interface{}) int64 {
	if value == nil {
		return 0
	}
	var result int64
	var err error
	switch value := value.(type) {

	case int64:
		result = value

	case float64:
		result = int64(value)

	case string:
		result, err = strconv.ParseInt(value, 10, 64)
		if err != nil {
			fmt.Errorf(err.Error())
		}

	default:
	}

	return result
}
