package utils

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/bean"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/common/http"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/global"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
)

var SynonymPandora = pandora.NewPandoraProto[bean.SynonymPayLoad, bean.SynonymPayLoad]()

// var SynonymDict = maputil.NewConcurrentMap[string, []string](0)
var SynonymDict = common.NewSafeMap[string, []string]()

var synonymMutex sync.Mutex

func LoadSynonymsFromApi() (string, *errtypes.SelfError) {
	fmt.Sprintf("分词干预开关: %v\n", config.G_InterveneModule.DefaultInstance().SegEnabled)
	tmpSynonymDict := common.NewSafeMap[string, []string]()
	// 获取最佳的服务节点
	host, err := goboot.TlbSdk().DefaultInstance().GetBestService(context.TODO(), config.G_InterveneModule.DefaultInstance().ServiceName)

	if err != nil {
		return "can not find synonym service", selferrors.SegError_SegInterveneError.Detaild(err.Error())
	}

	url := fmt.Sprintf("http://%s:%s%s", host.Host, strconv.Itoa(int(host.Port)), config.G_InterveneModule.DefaultInstance().SynonymUrl)

	synonymResp, err := http.GetFunc(url)
	if err != nil {
		return fmt.Sprintf("%s:%s", host.Host, strconv.Itoa(int(host.Port))), selferrors.SegError_SegInterveneError.Detaild(err.Error())
	}

	if synonymResp.Header.Code != 0 {
		return fmt.Sprintf("%s:%s", host.Host, strconv.Itoa(int(host.Port))), selferrors.SegError_SegInterveneError.Detaild(synonymResp.Header.Message)
	}

	if synonymResp.PayLoad.Data == nil {
		return fmt.Sprintf("%s:%s", host.Host, strconv.Itoa(int(host.Port))), selferrors.SegError_SegInterveneError.Detaild("synonym data is nil")
	}

	synonymItems := synonymResp.PayLoad.Data
	for _, item := range synonymItems {
		tmpSynonymDict.Set(item.Word, strings.Split(item.RelationWords, ","))

		if config.G_InterveneModule.DefaultInstance().SegEnabled {
			global.IFLYS_SEG_JIEBA.AddWord(item.Word)
		}
		words := strings.Split(item.MutualRelationWords, ",")
		for _, word := range words {
			if config.G_InterveneModule.DefaultInstance().SegEnabled {
				global.IFLYS_SEG_JIEBA.AddWord(item.Word)
			}
			lst, ok := tmpSynonymDict.Get(word)
			if ok {
				lst = append(lst, item.Word)
				tmpSynonymDict.Set(word, lst)
			} else {

				tmpSynonymDict.Set(word, []string{word})
			}
		}
	}

	// 将 tmpSynonymDict 赋值到 SynonymDict
	// 使用互斥锁保护赋值操作
	synonymMutex.Lock()
	SynonymDict = tmpSynonymDict
	synonymMutex.Unlock()

	return fmt.Sprintf("%s:%s", host.Host, strconv.Itoa(int(host.Port))), nil
}
