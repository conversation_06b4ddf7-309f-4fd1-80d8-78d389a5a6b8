package service

import (
	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	proto_seg "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/seg"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

type TextsParseService struct {
	Ctx  *pandora_context.PandoraContext
	Resp *pandora_proto.PandoraResponseMessage[proto_seg.SegAPIV2TextsResponsePayLoad]
	Req  *pandora_proto.PandoraRequestMessage[proto_seg.SegAPIV2TextsRequestPayLoad]
}

var segInst = new(Seg)

func (s *TextsParseService) checkRequest() error {

	if s.Req.Header.TraceId == "" {
		err := selferrors.CommonError_MissingField.Detaild("TraceId为空")
		s.Resp.Header.Code = err.Code()
		s.Resp.Header.Success = err.String()
		return err
	}
	if len(s.Req.Payload.Data) == 0 {
		err := selferrors.SegError_SegReqEmptyError.Detaild("请求数据为空")
		s.Resp.Header.TraceId = s.Req.Header.TraceId
		s.Resp.Header.Code = err.Code()
		s.Resp.Header.Success = err.String()
		s.Resp.Payload.Data = make([]string, len(s.Req.Payload.Data))
		s.Resp.Payload.Mod = 4
		return err
	}
	return nil
}

func (s *TextsParseService) parse() {
	err := s.checkRequest()
	if err != nil {
		return
	}

	textsResult := make([]string, len(s.Req.Payload.Data))

	var cutResult string
	for index, text := range s.Req.Payload.Data {
		cutResult, _, _ = segInst.seg(text, 4)
		textsResult[index] = cutResult

	}
	s.Resp.Header.TraceId = s.Req.Header.TraceId
	s.Resp.Payload.Data = textsResult
	s.Resp.Payload.Mod = 4
	return
}

func (s *TextsParseService) SegTextsHandler() {
	nspan := s.Ctx.RootSpan().AddSpan("执行数据分词服务")
	defer nspan.Finish()
	s.parse()

}
