package service

import (
	"fmt"
	"time"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/common"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/service/utils"
	proto_seg "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/seg"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/elk"
	http_server "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/http_server"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

type ForceUpdateService struct {
	Ctx  *pandora_context.PandoraContext
	Resp *pandora_proto.PandoraResponseMessage[proto_seg.GetRespPayLoad]
}

func (s *ForceUpdateService) ForceUpdateHandler() {

	
	s.Resp.Header.TraceId = common.GenerateUUID()
	server, err := utils.LoadSynonymsFromApi()
	if err != nil {
		// 资源更新出错
		goboot.Elklog().DefaultInstance().Error("updateSynonyms",
			elk.Type("updateSynonyms"),
			elk.Data(map[string]any{"msg": fmt.Sprintf("Auto update intervene data error: %s, addr: %s!!!!", err.Error(), server)}))
			//TODO: 定义一个错误码，不要使用-1
			http_server.Metrics().StatsErrors(goboot.BootConf().DefaultConfig().ServiceName, config.G_InterveneModule.DefaultConfig().SynonymUrl, "GET", -1, 1)
		s.Resp.Header.Code = err.Code()
		s.Resp.Header.Success = err.String()
	} else {
		// 资源更新成功
		http_server.Metrics().StatsErrors(goboot.BootConf().DefaultConfig().ServiceName, config.G_InterveneModule.DefaultConfig().SynonymUrl, "GET", 0, 1)
	}

	s.Resp.Payload = proto_seg.GetRespPayLoad{
		Timestamp: time.Now().UnixMilli(),
	}
}
