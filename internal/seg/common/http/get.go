package http

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/bean"
)

func GetFunc(url string) (resp bean.SynonymResponse, err error) {
	response, err := http.Get(url)
	if err != nil {
		return resp, err
	}
	defer response.Body.Close()

	if response.StatusCode != http.StatusOK {
		return resp, fmt.Errorf("Unexpected status code: %d", response.StatusCode)
	}

	body, err := ioutil.ReadAll(response.Body)
	if err != nil {
		return resp, err
	}

	err = json.Unmarshal(body, &resp)
	if err != nil {
		return resp, err
	}

	return resp, nil
}
