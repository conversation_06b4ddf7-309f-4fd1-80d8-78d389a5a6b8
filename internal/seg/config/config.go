package config

import (
	"bufio"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/common"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	base_factory "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/factory"
	base_model "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/model"
)

type AseConfig struct {
	base_model.BaseModel
	AppID     string `toml:"appId" mapstructure:"appId"`
	ApiKey    string `toml:"apiKey" mapstructure:"apiKey"`
	ApiSecret string `toml:"apiSecret" mapstructure:"apiSecret"`
	Url       string `toml:"url" mapstructure:"url"`
	Timeout   int    `toml:"timeout",mapstructure:"timeout",default:"500"`
}

// 资源路径不给默认值，错误则服务不启动
type resConfig struct {
	base_model.BaseModel
	BaseResDir  string `toml:"resbasedir" mapstructure:"resbasedir"`
	SynonymPath string `toml:"synonymPath" mapstructure:"synonymPath"`
	FileCount   int    `toml:"fileCount" default:"1"`
	ParseCount  int    `toml:"parseCount" default:"10"`
}

type interveneConfig struct {
	base_model.BaseModel
	Open             bool   `toml:"open" mapstructure:"open" default:"false"`
	InterveneEnabled bool   `toml:"interveneEnabled" mapstructure:"interveneEnabled" default:"false"`
	SegEnabled       bool   `toml:"segEnabled" mapstructure:"segEnabled" default:"false"`
	ServiceName      string `toml:"serviceName" mapstructure:"serviceName" default:"index-search"`
	SynonymUrl       string `toml:"synonymUrl" mapstructure:"synonymUrl" default:""`
	Tricker          int    `toml:"tricker" mapstructure:"tricker" default:"0"`
	CutLength        int    `toml:"cutLength" mapstructure:"cutLength" default:"-1"`
	// PuncStr          string `toml:"puncStr" mapstructure:"puncStr" default:""`
	// EmojiStr         string `toml:"emojiStr" mapstructure:"emojiStr" default:""`
	// ZeroWidthStr     string `toml:"zeroWidthStr" mapstructure:"zeroWidthStr" default:""`
}

const ResModuleName = "seg_res"
const ResTomlName = "seg_res"

var G_ResFactory = base_factory.NewFactory(ResModuleName, NewResInstance)
var G_ResModule = goboot.GetCustomModule(G_ResFactory)

func NewResInstance(option *resConfig) (*resConfig, error) {
	return option, nil
}

var InterveneModuleName = "seg_intervene"
var InterveneTomlName = "seg_intervene"

var G_InterveneFactory = base_factory.NewFactory(InterveneModuleName, NewInterveneInstance)
var G_InterveneModule = goboot.GetCustomModule(G_InterveneFactory)

func NewInterveneInstance(option *interveneConfig) (*interveneConfig, error) {
	return option, nil
}

func GetGoJiebaDictFiles() []string {
	// 获取jieeba资源文件路径
	segDictDir := fmt.Sprintf("%s%s", "./resource/seg", "/cppjieba/dict")
	files := []string{"jieba.dict.utf8", "hmm_model.utf8", "user.dict.utf8", "idf.utf8", "stop_words.utf8"}
	paths := make([]string, len(files))
	for index, file := range files {
		path := fmt.Sprintf("%s/%s", segDictDir, file)
		paths[index] = path
	}
	fmt.Sprintf("jieba dict files:%v\n", paths)
	return paths
}

// var JiebaPos = maputil.NewConcurrentMap[string, string](0)
var JiebaPos = common.NewSafeMap[string, string]()

func readLines(filePath string, dataCh chan<- string) {
	// 关闭dataCh
	defer close(dataCh)
	file, err := os.Open(filePath)
	if err != nil {
		panic(err)
	}
	defer file.Close()

	scanner := bufio.NewScanner(file)
	scanner.Buffer(make([]byte, 0, 64*1024), 1024*1024)
	for scanner.Scan() {
		line := string(scanner.Bytes())
		dataCh <- line
	}
}

func parseWorker(dataCh <-chan string, wg *sync.WaitGroup) {
	defer wg.Done()

	for data := range dataCh {
		items := strings.Split(data, " ")
		if len(items) >= 3 {
			JiebaPos.Set(items[0], items[2])
		}
	}

}

// 测试耗时: 350~380ms
func LoadJiebaPos() error {
	startTime := time.Now() // 记录开始时间

	defer func() {
		endTime := time.Now()                 // 记录结束时间
		elapsedTime := endTime.Sub(startTime) // 计算耗时
		fmt.Printf("LoadJiebaPos耗时: %s\n", elapsedTime)
	}()
	dataCh := make(chan string, 1000)
	posDictPath := fmt.Sprintf("%s%s", "./resource/seg", "/jieba/dict.txt")
	fmt.Sprintf("posDictPath:%s\n", posDictPath)

	go func() {
		readLines(posDictPath, dataCh)
	}()

	prCount := G_ResModule.DefaultInstance().ParseCount
	var wgParse sync.WaitGroup
	wgParse.Add(prCount)
	for i := 1; i <= prCount; i++ {
		go parseWorker(dataCh, &wgParse)
	}

	wgParse.Wait()
	return nil
}

func Init() error {
	// 加载jieba词性
	err := LoadJiebaPos()
	return err
}
