package common

import (
	"fmt"
	"regexp"
	"testing"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/common"
)

func TestRegex(t *testing.T) {
	// 定义一个正则表达式，匹配数字
	var numRegexp = regexp.MustCompile(`\d+`)

	// 使用正则表达式查找字符串中的数字
	str := "我有100个苹果和20个橙子"
	nums := numRegexp.FindAllString(str, -1)

	fmt.Println(nums) // 输出：[100 20]
}

func TestClean(t *testing.T) {
	text := common.UniformText("滴图（北京）科技有限公司")
	fmt.Println(text)
}

func TestReg(t *testing.T) {
	reg, err := regexp.Compile("[!\"#$%&'()*+,-./:;<=>?@[\\]^_`{|}~ ]+")
	if err != nil {
		panic(err)
	}
	s := "Hello! This @string# contains$ special% ^&*characters.科大讯飞"
	noSpecialChars := reg.ReplaceAllString(s, "")
	fmt.Println(noSpecialChars)
}
