package common

import (
	"errors"
	"fmt"
	"reflect"
	"runtime"
	"testing"

	"github.com/bytedance/sonic"
)

func TestSlice(t *testing.T) {
	s := []string{"a", "b", "c"}

	fmt.Println(s[:3])
}

func TestErr(t *testing.T) {
	var err error
	defer func() {
		if _err := recover(); _err != nil {
			// slog.Error(_err)
			// debug.PrintStack()
			fmt.Printf("%T\n", _err)
			fmt.Println(reflect.TypeOf(_err))
			errStr := fmt.Sprintf("%v", _err)

			err = errors.New(errStr)

			err = fmt.Errorf("%v", _err)
		}
		fmt.Printf("%T", err)
	}()

	s := []string{"a", "b", "c"}

	fmt.Println(s[:13])
}

func getGoroutineID() int {
	var buf [64]byte
	runtime.Stack(buf[:], false)
	var id int
	fmt.Sscanf(string(buf[:]), "goroutine %d", &id)
	return id
}

func TestRoutine(t *testing.T) {
	go func() {
		fmt.Println("协程1的ID:", getGoroutineID())
	}()

	go func() {
		fmt.Println("协程2的ID:", getGoroutineID())
	}()

	// 等待协程执行完成
	runtime.Gosched()
}

func TestSonic(t *testing.T) {
	value := 100
	valueStr, _ := sonic.MarshalString(value)
	fmt.Println(valueStr)
}

func TestParam(t *testing.T) {
	// params := []string{"1", "a", "ddd"}
	fun1("1", "a", "ddd")

}

func fun1(names ...string) {
	fmt.Println(names)
	fmt.Println(reflect.TypeOf(names))
}
