import base64
import hashlib
import hmac
import requests
from datetime import datetime
from time import mktime
from urllib.parse import urlencode
from wsgiref.handlers import format_date_time
from urllib import parse
import  json
import  jsonpath_rw
import time

#模型服务请求地址
APPId = "9d4f7017"
APIKey = "461a9ca5ba93757f2e7d8aa9e33b818c"
APISecret = "NWJlZDU2MmY4MTFlY2RjNGQ3NjJkYzhm"

# 请求数据
request_data = {
	"header":{
		"app_id":"123456",
		"uid":"39769795890",
		"did":"SR082321940000200",
		"imei":"8664020318693660",
		"imsi":"4600264952729100",
		"mac":"6c:92:bf:65:c6:14",
		"net_type":"wifi",
		"net_isp":"CMCC",
		"status":3,
		"res_id":""
	},
	"parameter":{
		"term_weight":{
			"result":{
				"encoding":"utf8",
				"compress":"raw",
				"format":"plain"
			}
		}
	},
	"payload":{
		"input":{
			"encoding":"utf8",
			"compress":"raw",
			"format":"plain",
			"status":3,
			"text":""
		}
	}
}

# 请求地址
request_url = "https://cn-huabei-1.xf-yun.com/v1/private/sabb3e785"


response_path_list = ['$..payload.result', ]

def build_auth_request_url(request_url, method="POST", api_key="", api_secret=""):
    url_result = parse.urlparse(request_url)
    print(f"url_result:{url_result}")
    date = format_date_time(mktime(datetime.now().timetuple()))
    signature_origin = "host: {}\ndate: {}\n{} {} HTTP/1.1".format(url_result.hostname, date, method, url_result.path)
    signature_sha = hmac.new(api_secret.encode('utf-8'), signature_origin.encode('utf-8'),
                             digestmod=hashlib.sha256).digest()
    signature_sha = base64.b64encode(signature_sha).decode(encoding='utf-8')
    authorization_origin = "api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"" % (
        api_key, "hmac-sha256", "host date request-line", signature_sha)
    authorization = base64.b64encode(authorization_origin.encode('utf-8')).decode(encoding='utf-8')
    values = {
        "host": url_result.hostname,
        "date": date,
        "authorization": authorization
    }
    return request_url + "?" + urlencode(values)

def deal_response(response):
    temp_result = json.loads(response.content.decode())
    header = temp_result.get('header')
    if header is None:
        return
    code = header.get('code')
    if header is None or code != 0:
        print("获取结果失败，请根据code查证问题原因")
        return

    # 打印Base64解码后数据并生成文件
    if response_path_list is None or len(response_path_list) == 0:
        return
    for response_path in response_path_list:
        response_expr = jsonpath_rw.parse(response_path)
        response_match = response_expr.find(temp_result)
        if len(response_match) > 0:
            for response_item in response_match:
                if response_item.value is None:
                    continue
                encoding = response_item.value.get('encoding')
                if encoding is None or len(encoding) == 0:
                    continue
                for media_type in ["text"]:
                    media_value = response_item.value.get(media_type)
                    if media_value is None or len(media_value) == 0:
                        continue
                    real_data = base64.b64decode(media_value).decode("utf-8")
                    return json.loads(real_data)
    return None

def GetTimesTamp():
    t = time.time()
    return int(round(t * 1000))    #毫秒级时间戳


def DiffTimes(st):
    t = time.time()
    ct = int(round(t * 1000))
    elpase = ct-st
    return elpase

def prepare(request_url, request_data, method, app_id, api_key, api_secret):
    auth_request_url = build_auth_request_url(request_url, method, api_key, api_secret)
    url_result = parse.urlparse(request_url)
    headers = {'content-type': "application/json", 'host': url_result.hostname, 'app_id': app_id}
    return auth_request_url,json.dumps(request_data),headers

def execute(auth_request_url,data,headers):
    st = GetTimesTamp()
    print(f"auth_request_url:{auth_request_url}")
    print(f"data:{data}")
    response = requests.post(auth_request_url, data=data, headers=headers)
    # print(response.content)
    elpase = DiffTimes(st)
    results = deal_response(response)
    return  results,elpase


'''
执行：python3 ./request_ase_api.py
request_url: 请求的url
request_data：请求协议。
'''
if __name__ == '__main__':
    request_data['header']['app_id'] = APPId
    data = [["滨海新区融媒体中心编委会", "副主任", "新媒体", "编辑部", "主任", "冯伟"],["小学", "信息", "科技", "学科", "跨学科", "融合", "举例子"],["湖南", "郴州", "张逸宽", "谁"]]
    # data = [["周恩来","和","毛泽东","的","十个","昼夜"]]
    json_data = json.dumps(obj=data,ensure_ascii=False).encode("utf-8")
    print(f"json_data:{json_data}")
    request_data["payload"]["input"]["text"] =base64.b64encode(json_data).decode()
    

    auth_request_url,data,headers = prepare(request_url, request_data, "POST", APPId, APIKey, APISecret)
    results,elapse = execute(auth_request_url,data,headers)

    print(results)
