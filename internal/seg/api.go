package seg

import (
	"fmt"
	"time"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/global"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/service"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/service/utils"

	proto_seg "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/seg"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/elk"
	http_server "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/pkg/http_server"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
	tokenizer_v2 "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/tokenizer_v2_1"
)

func padoraSegQueryProcess(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_seg.SegAPIV2QueryRequestPayLoad]) (resp *pandora_proto.PandoraResponseMessage[proto_seg.SegAPIV2QueryResponsePayLoad]) {
	service := &service.QueryParseService{
		Ctx:  ctx,
		Resp: proto_seg.ProtoSegAPIV2Query.NewPandoraResponseMessage(),
		Req:  req,
	}
	service.SegQueryHandler()
	return service.Resp
}

func padoraSegObjsProcess(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_seg.SegAPIV2ObjsRequestPayLoad]) (resp *pandora_proto.PandoraResponseMessage[proto_seg.SegAPIV2ObjsResponsePayLoad]) {
	service := &service.ObjsParseService{
		Ctx:  ctx,
		Resp: proto_seg.ProtoSegAPIV2Objs.NewPandoraResponseMessage(),
		Req:  req,
	}
	service.SegObjsHandler()
	return service.Resp
}

func padoraSegTextsProcess(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_seg.SegAPIV2TextsRequestPayLoad]) (resp *pandora_proto.PandoraResponseMessage[proto_seg.SegAPIV2TextsResponsePayLoad]) {
	service := &service.TextsParseService{
		Ctx:  ctx,
		Resp: proto_seg.ProtoSegAPIV2Texts.NewPandoraResponseMessage(),
		Req:  req,
	}
	service.SegTextsHandler()
	return service.Resp
}

func pandoraForceUpdateProcess(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_seg.GetRespPayLoad]) (resp *pandora_proto.PandoraResponseMessage[proto_seg.GetRespPayLoad]) {
	service := &service.ForceUpdateService{
		Resp: proto_seg.ProtoSegForceUpdateAPIV2.NewPandoraResponseMessage(),
		Ctx:  ctx,
	}
	service.ForceUpdateHandler()
	return service.Resp
}

func startInterveneScheduler() {
	fmt.Sprintf("Intervene tricker: %d\n", config.G_InterveneModule.DefaultInstance().Tricker)
	ticker := time.NewTicker(time.Duration(config.G_InterveneModule.DefaultInstance().Tricker) * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			server, err := utils.LoadSynonymsFromApi()

			if err != nil {
				goboot.Elklog().DefaultInstance().Error("updateSynonyms",
					elk.Type("updateSynonyms"),
					elk.Data(map[string]any{"msg": fmt.Sprintf("Auto update intervene data error: %s, addr: %s!!!!", err.Error(), server)}))
				http_server.Metrics().StatsErrors(goboot.BootConf().DefaultConfig().ServiceName, config.G_InterveneModule.DefaultConfig().SynonymUrl, "GET", -1, 1)
			} else {
				http_server.Metrics().StatsErrors(goboot.BootConf().DefaultConfig().ServiceName, config.G_InterveneModule.DefaultConfig().SynonymUrl, "GET", 0, 1)
			}
		}
	}
}

func Init() error {

	err := goboot.RegisterCustomSingletonModule(config.G_ResFactory)
	if err != nil {
		return err
	}

	err = config.G_ResModule.Need()
	if err != nil {
		return err
	}

	err = goboot.RegisterCustomSingletonModule(config.G_InterveneFactory)
	if err != nil {
		return err
	}

	err = config.G_InterveneModule.Need()
	if err != nil {
		return err
	}

	// tokenizer_v2依赖是否加载
	tokenizer_v2.G_tokenizer.Must()

	err = global.Init()
	if err != nil {
		return err
	}

	if config.G_InterveneModule.DefaultInstance().Open {

		server, Err := utils.LoadSynonymsFromApi()
		if Err != nil {
			goboot.Logger().DefaultInstance().Error(Err.String())
			goboot.Elklog().DefaultInstance().Error("updateSynonyms",
				elk.Type("updateSynonyms"),
				elk.Data(map[string]any{"msg": fmt.Sprintf("Auto update intervene data error: %s, addr: %s!!!!", Err.String(), server)}))
			http_server.Metrics().StatsErrors(goboot.BootConf().DefaultConfig().ServiceName, config.G_InterveneModule.DefaultConfig().SynonymUrl, "GET", -1, 1)
			return Err
		} else {
			http_server.Metrics().StatsErrors(goboot.BootConf().DefaultConfig().ServiceName, config.G_InterveneModule.DefaultConfig().SynonymUrl, "GET", 0, 1)
		}
		go startInterveneScheduler()
	}

	router := goboot.HttpServer().DefaultInstance().Router
	v1 := router.Group("/seg")
	{
		v1.POST("/api/v2/query", proto_seg.ProtoSegAPIV2Query.GinWrapper().SetHandler(padoraSegQueryProcess).HandlerFunc())
		v1.POST("/api/v2/objs", proto_seg.ProtoSegAPIV2Objs.GinWrapper().SetHandler(padoraSegObjsProcess).HandlerFunc())
		v1.POST("/api/v2/texts", proto_seg.ProtoSegAPIV2Texts.GinWrapper().SetHandler(padoraSegTextsProcess).HandlerFunc())
		v1.GET("/api/v2/forceUpdate", proto_seg.ProtoSegForceUpdateAPIV2.GinWrapper().SetHandler(pandoraForceUpdateProcess).HandlerFunc())
	}
	return nil
}
