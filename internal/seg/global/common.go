package global

import (
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/seg/config"
	tokenizerv2_1 "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/tokenizer_v2_1"
	"github.com/yanyiwu/gojieba"
)

var (
	IFLYS_SEG_JIEBA        *gojieba.Jieba
	IFLYS_SAMPLE_Tokenizer *tokenizerv2_1.TokenizerWrapper
	IFLYS_TokenModelServer *tokenizerv2_1.AsyncTokenizer
)

func Init() error {
	// 初始化资源和公共实例
	IFLYS_SEG_JIEBA = gojieba.NewJieba(config.GetGoJiebaDictFiles()...)
	// 初始化资源和公共实例
	IFLYS_TokenModelServer = tokenizerv2_1.G_tokenizer.DefaultInstance()

	IFLYS_SAMPLE_Tokenizer = tokenizerv2_1.NewSimpleTokenizer(tokenizerv2_1.G_tokenizer.DefaultConfig().Path, tokenizerv2_1.G_tokenizer.DefaultConfig().UseLocal)
	IFLYS_SEG_JIEBA.AddWord("科大讯飞股份有限公司")

	err := config.Init()
	if err != nil {
		return err
	}
	return nil
}
