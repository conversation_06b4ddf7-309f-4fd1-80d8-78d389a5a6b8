package global

const (
	JiebaCut       = 4
	JiebaCutAll    = 3
	JiebaCutSearch = 2
	JiebaCutPos    = 1

	LACCut = 0
)

// 名词（实体词）tag
const (
	N   = "n"
	F   = "f"
	S   = "s"
	NR  = "nr"
	NS  = "ns"
	NT  = "nt"
	NW  = "nw"
	NZ  = "nz"
	PER = "PER"
	LOC = "LOC"
	ORG = "ORG"
)

var NounTags = map[string]struct{}{
	N:   {},
	F:   {},
	S:   {},
	NR:  {},
	NS:  {},
	NT:  {},
	NW:  {},
	NZ:  {},
	PER: {},
	LOC: {},
	ORG: {},
}

type API string

var SynonymAPI API = "synonym"

var CLS int64 = 1
var SEP int64 = 2
var TYPES int64 = 0
var MASK int64 = 1

var TokenModelName string = "Xenova-ernie-3.0"
var TermWeightVersion string = "termweight_v20231221"
