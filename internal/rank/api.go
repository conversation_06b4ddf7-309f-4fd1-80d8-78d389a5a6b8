package rank

import (
	"fmt"
	"time"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rank/entity"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rank/service"
	proto_rank "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/rank"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
	"github.com/bytedance/sonic"
	"github.com/go-playground/validator/v10"
	tokenizer_v2 "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/tokenizer_v2_1"
)

var sonicAPI sonic.API

type rank struct {
	validate *validator.Validate
}

func (r *rank) RankHandler(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[proto_rank.Payload]) *pandora_proto.PandoraResponseMessage[proto_rank.Payload] {
	now := time.Now()
	span := ctx.RootSpan().AddSpan("rank-handler")
	defer span.Finish()
	span.TraceInfo("query_num", len(req.Payload.Data))

	appidSpan := ctx.RootSpan().AddSpan(req.Header.AppId)
	defer appidSpan.Finish()

	resp := proto_rank.RankAPI.NewPandoraResponseMessage()

	// 请求参数校验
	if err := r.validate.Struct(req); err != nil {
		return fillHeader(resp, selferrors.CommonError_InvalidInput.Detaild(err.Error()))
	}

	ctx.Store().SetData(entity.KeyTag, req.Header.Tag)

	// 调用rank服务
	if err := service.Service.ProcessReq(ctx, span, &req.Payload); err != nil {
		if selfError, ok := err.(*errtypes.SelfError); ok {
			return fillHeader(resp, selfError)
		}
		return fillHeader(resp, selferrors.RankError_InferFailed.Detaild(err.Error()))
	}

	resp.Payload = req.Payload
	resp.Header.Success = fmt.Sprintf("success, cost: %dms", time.Since(now).Milliseconds())
	return resp
}

func fillHeader(resp *pandora_proto.PandoraResponseMessage[proto_rank.Payload], err *errtypes.SelfError) *pandora_proto.PandoraResponseMessage[proto_rank.Payload] {
	resp.Header.Code = err.Code()
	resp.Header.Success = err.String()
	return resp
}

func sonicUnmarshal(out []byte, req *pandora_proto.PandoraRequestMessage[proto_rank.Payload]) error {
	return sonicAPI.Unmarshal(out, req)
}

// Init 服务初始化
func Init() error {
	// 添加自定义配置
	if err := goboot.RegisterCustomMultiModule(entity.ModelFactory); err != nil {
		panic(fmt.Sprintf("register custom module failed, error: %s", err.Error()))
	}

	// 检查自定义配置是否正确加载
	goboot.GetCustomModule(entity.ModelFactory).Must()

	// tokenizer_v2依赖是否加载
	tokenizer_v2.G_tokenizer.Must()

	// asesdk依赖是否加载
	goboot.AseSdk().Must()

	// 服务初始化
	if err := service.Service.Init(); err != nil {
		return err
	}

	sonicAPI = sonic.Config{UseInt64: true}.Froze()

	rankInst := &rank{
		validate: validator.New(validator.WithRequiredStructEnabled()),
	}

	//注册服务
	goboot.HttpServer().DefaultInstance().Router.POST("/rank/api/v2", proto_rank.RankAPI.GinWrapper().SetHandler(rankInst.RankHandler).SetRequestUnmarshal(sonicUnmarshal).HandlerFunc())

	return nil
}
