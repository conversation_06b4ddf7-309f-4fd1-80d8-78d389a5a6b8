package service

import (
	"fmt"
	"sort"
	"strings"

	"golang.org/x/sync/errgroup"

	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
	tokenizer_v2 "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/tokenizer_v2_1"
	"github.com/samber/lo"
	lop "github.com/samber/lo/parallel"

	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/rank/entity"
	proto_rank "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/proto/rank"
	pandora_span "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/boot_tools/span"
	util "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/data_tool"
	aseclient "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/ase_sdk/client"
	pandora_context "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/context"
	wrapper "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/tokenizer_v2_1"
)

type service struct {
	aseMap   map[string]*aseItem
	rankExpr *util.RankExpr // 排序文本表达式
}

type aseItem struct {
	aseConf   *entity.ModelConfig
	aseSdk    *aseclient.InferClient
	tokenizer *wrapper.AsyncTokenizer
}

// Service 排序服务单例
var Service = new(service)

// 初始化服务
func (s *service) Init() error {
	models := goboot.GetCustomModule(entity.ModelFactory).GetAllConfig()
	s.aseMap = make(map[string]*aseItem, len(models))
	for _, model := range models {
		item := &aseItem{}
		item.aseConf = model

		if item.tokenizer = tokenizer_v2.G_tokenizer.GetInstance(model.UName); item.tokenizer == nil {
			return selferrors.PrerankError_ModelNotSupport.Detaild(fmt.Sprintf("invalid tokenizer: %s", model.UName))
		}

		if item.aseSdk = goboot.AseSdk().GetInstance(model.UName); item.aseSdk == nil {
			return selferrors.PrerankError_ModelNotSupport.Detaild(fmt.Sprintf("invalid ase sdk: %s", model.UName))
		}

		s.aseMap[model.UName] = item
	}
	// 初始化排序表达式
	s.rankExpr = util.NewRankExpr()
	return nil
}

// 精排推理
func (s *service) rank(ctx *pandora_context.PandoraContext, span *pandora_span.Span, reqQA *proto_rank.ReqQA) ([]float32, error) {
	rankSpan := span.AddSpan("rank")
	defer rankSpan.Finish()

	rankSpan.TraceInfo("doc_num", len(reqQA.Answers))

	// 根据模型获取ase配置
	item, ok := s.aseMap[reqQA.Model]
	if !ok {
		return nil, selferrors.RankError_ModelNotSupport.Detaild(fmt.Sprintf("invalid model: %s", reqQA.Model))
	}

	aseConf := item.aseConf
	rankSpan.TraceInfo("bacth_size", aseConf.BatchSize)

	tlbTag, _ := ctx.Store().GetData(entity.KeyTag)

	// 按照指定batch_size重新划分doc
	var g errgroup.Group
	res := make([]float32, len(reqQA.Answers))
	docsBatch := lo.Chunk(reqQA.Answers, aseConf.BatchSize)
	for batchID, docs := range docsBatch {
		batchSpan := rankSpan.AddSpan("one-batch")
		g.Go(func() error {
			tokenizerSpan := batchSpan.AddSpan("tokenizer")
			token, shape, err := s.assembleToken(reqQA.Model, util.Repeat(reqQA.Query, len(docs)), docs)
			if err != nil {
				return err
			}
			tokenizerSpan.Finish()

			inferSpan := batchSpan.AddSpan("infer")
			// 调用模型推理
			client := item.aseSdk.Request().SetTraceId(ctx.TraceId).SetParentSpan(inferSpan).SetHeaderTag(tlbTag.(string))
			resp, err := client.Send(ctx.GetContext(), &aseclient.InferReq{
				Inputs: token,
				Shapes: shape,
			})

			if err != nil {
				return selferrors.RankError_AseRequestFailed.Detaild(err.Error())
			}
			inferSpan.Finish()

			scores := util.BytesToSlice[float32](resp.Payload.Outputs[entity.LogitsName])
			scores = util.Sigmoid(scores)

			for i, item := range scores {
				res[batchID*aseConf.BatchSize+i] = item
			}

			batchSpan.Finish()
			return nil
		})
	}

	if err := g.Wait(); err != nil {
		return nil, err
	}

	return res, nil
}

// 精排请求
func (s *service) ProcessReq(ctx *pandora_context.PandoraContext, span *pandora_span.Span, req *proto_rank.Payload) (err error) {
	defer func() {
		if err != nil {
			span.TraceInfo("ProcessReqFailed", err.Error())
		}
	}()

	// 无效数据过滤
	req.Data = lo.Filter(req.Data, func(item *proto_rank.DataItem, _ int) bool {
		return item != nil
	})

	processSpan := span.AddSpan("multi-query")
	// 并发请求
	var g errgroup.Group
	for i := range req.Data {
		g.Go(func() error {
			if req.Chunk {
				return s.process2(ctx, processSpan, req, req.Data[i])
			}
			return s.process(ctx, processSpan, req, req.Data[i])
		})
	}
	err = g.Wait()
	processSpan.Finish()

	if err != nil {
		return err
	}

	postSpan := span.AddSpan("post-process")
	// 结果合并
	switch req.MergeMode {
	case entity.MergeRewrite:
		// type 填入
		for _, item := range req.Data {
			lo.ForEach(item.Docs, func(doc map[string]any, _ int) {
				doc[entity.TYPE] = item.Type
			})
		}

		// query分组
		group := lo.GroupBy(req.Data, func(item *proto_rank.DataItem) string {
			if item.Type == entity.TypeRewritten && len(item.Extra) > 0 {
				return item.Extra[0].Query
			}
			return item.Query
		})

		// 分组合并
		req.Data = lo.MapToSlice(group, func(query string, items []*proto_rank.DataItem) *proto_rank.DataItem {
			for i := 1; i < len(items); i++ {
				items[0].Query = query
				items[0].Type = entity.TypeRewritten
				items[0].Extra = lo.If(len(items[i].Extra) > 0, items[i].Extra).Else(items[0].Extra)
				items[0].Docs = append(items[0].Docs, items[i].Docs...)
			}
			return items[0]
		})
	}

	for _, dataItem := range req.Data {
		// 结果排序
		sort.Slice(dataItem.Docs, func(i, j int) bool {
			score1 := dataItem.Docs[i][entity.RankScore].(float32)
			score2 := dataItem.Docs[j][entity.RankScore].(float32)
			return score1 > score2
		})

		// 结果去重
		dataItem.Docs = lo.UniqBy(dataItem.Docs, func(doc map[string]any) int64 {
			num, ok := doc[entity.ID].(int64)
			if !ok {
				return 0
			}
			return num
		})

		// 取topK，-1为不限
		if req.TopK > -1 && len(dataItem.Docs) > req.TopK {
			dataItem.Docs = dataItem.Docs[:req.TopK]
		}

		// 排序序号
		for i, doc := range dataItem.Docs {
			doc[entity.RankIndex] = i + 1
		}
	}
	postSpan.Finish()

	return nil
}

// 推理结果处理
func (s *service) process(ctx *pandora_context.PandoraContext, span *pandora_span.Span, params *proto_rank.Payload, item *proto_rank.DataItem) error {
	inferSpan := span.AddSpan("single-query")
	defer inferSpan.Finish()

	// 过滤id为空的
	item.Docs = lo.Filter(item.Docs, func(doc map[string]any, _ int) bool {
		_, ok := doc[entity.ID]
		return ok
	})

	if len(item.Docs) == 0 {
		return nil
	}

	parseSpan := span.AddSpan("doc-parse")
	// 请求转换
	validDocs := make([]map[string]any, 0, len(item.Docs)) // 用于保存过滤后的有效 doc
	docs := make([]string, 0, len(item.Docs))
	for _, doc := range item.Docs {
		// 排序内容有误，直接过滤
		exprRes, err := s.rankExpr.Run(params.RankExpr, doc)
		if err != nil {
			inferSpan.SetTag("rankExpr", fmt.Sprintf("expr:%v, error:%v", params.RankExpr, err))
			goboot.Logger().DefaultInstance().Error(fmt.Sprintf("rankExpr run failed : %s", err.Error()))
			continue
		}

		content, ok := exprRes.(string)
		if !ok {
			return selferrors.CommonError_TypeConvertFailed.Detaild("can't convert expr result to string, please enable chunk mode")
		}

		docs = append(docs, content)
		validDocs = append(validDocs, doc) // 只保留有效的 doc
	}

	// 更新 item.Docs 为有效的 docs
	item.Docs = validDocs

	reqQA := &proto_rank.ReqQA{
		Query:   item.Query,
		Model:   params.Model,
		Answers: docs,
	}

	// 改写策略
	switch params.MergeMode {
	case entity.MergeRewrite:
		if len(item.Extra) > 0 {
			// 改写query替换为原有query
			reqQA.Query = item.Extra[0].Query
			// 改写词替换为原有词
			reqQA.Answers = lop.Map(docs, func(doc string, _ int) string {
				for _, word := range item.Extra {
					doc = strings.ReplaceAll(doc, word.Replace, word.Raw)
				}
				return doc
			})
		}
	}

	parseSpan.Finish()

	// 处理请求
	scores, err := s.rank(ctx, inferSpan, reqQA)
	if err != nil {
		return err
	}

	// 精排得分填入
	for i, score := range scores {
		item.Docs[i][entity.RankScore] = score
	}

	// 阈值卡分
	item.Docs = lo.Filter(item.Docs, func(doc map[string]any, _ int) bool {
		return doc[entity.RankScore].(float32) > float32(params.Threshold)
	})

	return nil
}

// 推理结果处理, 分片排序
func (s *service) process2(ctx *pandora_context.PandoraContext, span *pandora_span.Span, params *proto_rank.Payload, item *proto_rank.DataItem) error {
	inferSpan := span.AddSpan("single-query")
	defer inferSpan.Finish()

	// 过滤id为空的
	item.Docs = lo.Filter(item.Docs, func(doc map[string]any, _ int) bool {
		_, ok := doc[entity.ID]
		return ok
	})

	if len(item.Docs) == 0 {
		return nil
	}

	parseSpan := span.AddSpan("doc-parse")
	// 请求转换
	validDocs := make([]map[string]any, 0, len(item.Docs)) // 用于保存过滤后的有效 doc
	docs := make([]string, 0, len(item.Docs))
	chunks := make([]int, 0, len(item.Docs))
	for _, doc := range item.Docs {
		// 排序内容有误，直接过滤
		exprRes, err := s.rankExpr.Run(params.RankExpr, doc)
		if err != nil {
			inferSpan.SetTag("rankExpr", fmt.Sprintf("expr:%v, error:%v", params.RankExpr, err))
			goboot.Logger().DefaultInstance().Error(fmt.Sprintf("rankExpr run failed : %s", err.Error()))
			continue
		}

		contents, ok := exprRes.([]any)
		if !ok {
			return selferrors.CommonError_TypeConvertFailed.Detaild("can't convert expr result to []string, please disable chunk mode")
		}
		chunks = append(chunks, len(contents))
		for _, c := range contents {
			docs = append(docs, c.(string))
		}

		validDocs = append(validDocs, doc) // 只保留有效的 doc
	}

	// 更新 item.Docs 为有效的 docs
	item.Docs = validDocs

	reqQA := &proto_rank.ReqQA{
		Query:   item.Query,
		Model:   params.Model,
		Answers: docs,
	}

	// 改写策略
	switch params.MergeMode {
	case entity.MergeRewrite:
		if len(item.Extra) > 0 {
			// 改写query替换为原有query
			reqQA.Query = item.Extra[0].Query
			// 改写词替换为原有词
			reqQA.Answers = lop.Map(docs, func(doc string, _ int) string {
				for _, word := range item.Extra {
					doc = strings.ReplaceAll(doc, word.Replace, word.Raw)
				}
				return doc
			})
		}
	}

	parseSpan.Finish()

	// 处理请求
	scores, err := s.rank(ctx, inferSpan, reqQA)
	if err != nil {
		return err
	}

	// 精排得分填入，doc得分取对应所有分片中最高得分
	startIndex := 0
	for i, doc := range item.Docs {
		for j := range chunks[i] {
			score := scores[startIndex+j]
			span := doc[entity.SpanIDs].([]any)[j].(map[string]any)
			span[entity.RankScore] = score
		}

		doc[entity.RankScore] = util.MaxInRange(scores, startIndex, startIndex+chunks[i])
		startIndex += chunks[i]
	}

	// 阈值卡分
	item.Docs = lo.Filter(item.Docs, func(doc map[string]any, _ int) bool {
		return doc[entity.RankScore].(float32) > float32(params.Threshold)
	})

	return nil
}

func (s *service) assembleToken(model string, queries, answers []string) (map[string][]byte, map[string][]int64, error) {
	tokenMap := make(map[string][]byte)
	shapeMap := make(map[string][]int64)

	encodings, err := wrapper.AsyncEncodeBatchPair[int64](s.aseMap[model].tokenizer, queries, answers, true)
	if err != nil {
		return nil, nil, err
	}

	for _, encoding := range encodings {
		tokenMap[entity.InputIds] = append(tokenMap[entity.InputIds], util.SliceToBytes(encoding.Ids)...)
		tokenMap[entity.AttentionMask] = append(tokenMap[entity.AttentionMask], util.SliceToBytes(encoding.Masks)...)
		tokenMap[entity.TokenType] = append(tokenMap[entity.TokenType], util.SliceToBytes(encoding.Types)...)
	}

	batchSize := int64(len(queries))
	shapeMap[entity.InputIds] = []int64{batchSize, int64(len(encodings[0].Ids))}
	shapeMap[entity.AttentionMask] = []int64{batchSize, int64(len(encodings[0].Masks))}
	shapeMap[entity.TokenType] = []int64{batchSize, int64(len(encodings[0].Types))}

	return tokenMap, shapeMap, nil
}
