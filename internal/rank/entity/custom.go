package entity

import (
	base_factory "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/factory"
	base_model "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/frame/base/model"
)

const TOML_MODEL = "rank_models"

var ModelFactory = base_factory.NewFactory(TOML_MODEL, NewEmptyInst)

type EmptyInstance struct{}

// ModelConfig 模型配置
type ModelConfig struct {
	base_model.BaseModel
	Timeout        int `toml:"timeout"`
	BatchSize      int `toml:"batch_size"`
}

func NewEmptyInst(option *ModelConfig) (*EmptyInstance, error) {
	return &EmptyInstance{}, nil
}
