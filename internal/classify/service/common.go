package service

import (
	selferrors "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/classify/model"
	pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
)

func (s *Service) ValidateParameters(req *pandora_proto.PandoraRequestMessage[model.Request]) *errtypes.SelfError {

	// 兼容老协议
	if len(req.Payload.Texts) > 0 {
		req.Payload.Queries = req.Payload.Texts
	}

	if len(req.Payload.Queries) == 0 {
		return selferrors.CommonError_InvalidInput.Detaild("queries is empty")
	}

	return nil
}

func (s *Service) ValidateTimelinessParameters(req *pandora_proto.PandoraRequestMessage[model.TimelinessRequest]) *errtypes.SelfError {

	// 兼容老协议
	if len(req.Payload.Texts) > 0 {
		req.Payload.Queries = req.Payload.Texts
	}

	if len(req.Payload.Queries) == 0 {
		return selferrors.CommonError_InvalidInput.Detaild("queries is empty")
	}

	for _, query := range req.Payload.Queries {
		if query.LabelId == 0 {
			query.LabelId = query.Domain
		}
		if query.Query == "" {
			query.Query = query.Text
		}
	}

	return nil
}
