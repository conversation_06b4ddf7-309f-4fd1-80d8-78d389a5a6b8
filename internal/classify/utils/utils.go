/*
 * @Author: hfliang <EMAIL>
 * @Date: 2024-11-25 15:32:48
 * @LastEditors: hfliang <EMAIL>
 * @LastEditTime: 2024-11-25 15:50:07
 * @FilePath: /lynxiao-ai-timeliness/src/utils/utils.go
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package utils

import (
	"regexp"
	"strconv"
	"strings"
	"time"

	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/classify/consts"

	"github.com/dlclark/regexp2"
)

/*
 一些项目公用的工具函数
*/

// interface{}转string类型，失败时设定默认值
func GetStringOrDefault(input interface{}, defaultValue string) string {
	if val, ok := input.(string); ok {
		return val
	} else {
		return defaultValue
	}
}

// interface{}转int类型，失败时设定默认值
func GetIntOrDefault(input interface{}, defaultValue int) int {
	// int类型有多种，暂时都转成int
	switch val := input.(type) {
	case int:
		return val
	case int16:
		return int(val)
	case int32:
		return int(val)
	case int64:
		return int(val)
	default:
		return defaultValue
	}
}

// interface{}转bool类型，失败时设定默认值
func GetBoolOrDefault(input interface{}, defaultValue bool) bool {
	if val, ok := input.(bool); ok {
		return val
	} else {
		return defaultValue
	}
}

// interface{}转bool类型，失败时设定默认值
func GetFloat32OrDefault(input interface{}, defaultValue float32) float32 {

	if val, ok := input.(float32); ok {
		return val
	} else {
		if val, ok := input.(float64); ok {
			return float32(val)
		} else {
			return defaultValue
		}

	}

}

func Argmax(socres [][]float32) []int {
	var res []int
	for _, score := range socres {
		max := score[0]
		index := 0
		for i, s := range score {
			if s > max {
				max = s
				index = i
			}
		}
		res = append(res, index)
	}
	return res
}

func BeforeOrAfterDates(text string, domain int32) int {
	text = dataPrepare(text)
	var year, month, day string

	year, month, day = findTwoYears(text)
	if year == "" {
		year, month, day = findDatesInText2(text)
	}
	if year == "" {
		year, month, day = findDatesInText(text)
	}
	if year == "" {
		year, month, day = findYearsInText(text)
	}
	// 当前时间
	currentTime := time.Now()
	currentYear := currentTime.Year()

	// 0911修改：没有匹配到日期不能赋值成当天日期
	if year == "" {
		return 0
	}

	yearInt, _ := strconv.Atoi(year)
	monthInt, _ := strconv.Atoi(month)
	dayInt, _ := strconv.Atoi(day)

	if len(year) == 2 {
		yearPrefix := "20"
		if yearInt > currentYear%100 {
			yearPrefix = "19"
		}
		year = yearPrefix + year
	}

	switch domain {
	case consts.DOMAIN_SPORTS:
		objDate := time.Date(yearInt, time.Month(monthInt), dayInt, 0, 0, 0, 0, time.Local)
		return objDate.Compare(currentTime)
	default:
		// 仅比较年份
		objYear, err := strconv.Atoi(year)
		if err != nil {
			return 0
		}
		if objYear < currentYear {
			return -1
			// 0910修改：社会考试领域命中2024当前年份，认为是强时效
		} else if objYear == currentYear {
			return 10
		} else {
			return 1
		}
	}
}

var numMap = map[rune]int{
	'〇': 0, '零': 0,
	'一': 1, '二': 2, '三': 3, '四': 4,
	'五': 5, '六': 6, '七': 7, '八': 8, '九': 9,
	'十': 10,
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func chineseToArabic(cnum string) int {
	num := 0
	cnumRunes := []rune(cnum)
	if strings.HasPrefix(cnum, "十") {
		if len(cnum) == 1 {
			return 10
		}
		num = 10
		cnumRunes = cnumRunes[1:]
	}
	for i, cn := range cnumRunes {
		if val, ok := numMap[cn]; ok {
			if cn == '十' {
				if i == 0 {
					num = 10
				} else if i < len(cnumRunes)-1 {
					prev := numMap[cnumRunes[i-1]]
					num += 10*prev - prev
				} else {
					prev := numMap[cnumRunes[i-1]]
					num += 10 * prev
				}
			} else {
				num += val
			}
		}
	}
	return num
}

// 处理文本中的时间表达和空格
func dataPrepare(text string) string {
	currentYear := time.Now().Year()
	text = strings.ReplaceAll(text, "后年", strconv.Itoa(currentYear+2)+"年")
	text = strings.ReplaceAll(text, "明年", strconv.Itoa(currentYear+1)+"年")
	text = strings.ReplaceAll(text, "去年", strconv.Itoa(currentYear-1)+"年")
	text = strings.ReplaceAll(text, "前年", strconv.Itoa(currentYear-2)+"年")

	// 使用正则表达式匹配数字之间的空格并替换为零宽空格
	re := regexp2.MustCompile(`(?<=\d) (?=\d)`, 0)
	for match, _ := re.FindStringMatch(text); match != nil; match, _ = re.FindNextMatch(match) {
		text = text[:match.Index] + "\u200B" + text[match.Index+1:]
	}

	// 删除所有剩余的空格
	text = strings.ReplaceAll(text, " ", "")
	// 将零宽空格替换回空格
	text = strings.ReplaceAll(text, "\u200B", " ")

	return text
}

func convertChineseDate(text string) string {
	// 转换年份
	reYear := regexp.MustCompile(`[〇零一二三四五六七八九]{4}年`)
	yearMatch := reYear.FindString(text)
	if yearMatch != "" {
		yearC := strings.TrimSuffix(yearMatch, "年")
		year := ""
		for _, c := range yearC {
			year += strconv.Itoa(numMap[c])
		}
		text = strings.ReplaceAll(text, yearC+"年", year+"年")
	}

	// 转换月份
	reMonth := regexp.MustCompile(`[一二三四五六七八九十]{1,3}月`)
	monthMatch := reMonth.FindString(text)
	if monthMatch != "" {
		monthC := strings.TrimSuffix(monthMatch, "月")
		month := chineseToArabic(monthC)
		text = strings.ReplaceAll(text, monthC+"月", strconv.Itoa(month)+"月")
	}

	// 转换日号
	reDay := regexp.MustCompile(`[一二三四五六七八九十]{1,3}(日|号)`)
	dayMatch := reDay.FindString(text)
	if dayMatch != "" {
		dayC := strings.TrimSuffix(dayMatch, "日")
		dayC = strings.TrimSuffix(dayC, "号")
		day := chineseToArabic(dayC)
		text = strings.ReplaceAll(text, dayC+"日", strconv.Itoa(day)+"号")
		text = strings.ReplaceAll(text, dayC+"号", strconv.Itoa(day)+"号")
	}

	return text
}

// 识别文本中包含的两个年份，可能紧挨着出现，也可能有不同的连接符。
func findTwoYears(text string) (string, string, string) {
	currentTime := time.Now()
	currentMonth := currentTime.Month()
	currentDay := currentTime.Day()
	month := strconv.Itoa(int(currentMonth))
	day := strconv.Itoa(currentDay)

	// 0903 修改点，结尾不能是分
	// 匹配四位数年份，可能紧挨着，也可能有非数字字符分隔（但不包括数字，防止匹配到一个很长的数字）
	re := regexp2.MustCompile(`(\d{4})\D*?(\d{4})(?!分)`, regexp2.None)
	if match, _ := re.FindStringMatch(text); match != nil {
		year1, _ := strconv.Atoi(match.GroupByNumber(1).String())
		year2, _ := strconv.Atoi(match.GroupByNumber(2).String())
		year := strconv.Itoa(max(year1, year2))
		return year, month, day
	}

	// 0903 修改点，结尾不能是分
	re = regexp2.MustCompile(`(\d{2,4})(?!分)(?!\.\D*?\d)(?![：:月]\s*)\D+(\d{2,4})(?!分)`, regexp2.None)
	if match, _ := re.FindStringMatch(text); match != nil {
		year1, _ := strconv.Atoi(match.GroupByNumber(1).String())
		year2, _ := strconv.Atoi(match.GroupByNumber(2).String())
		year := strconv.Itoa(max(year1, year2))
		return year, month, day
	}

	return "", "", ""
}

// 解析文本中的日期，并与当前日期比较，判断相对时间关系
func findDatesInText2(text string) (string, string, string) {
	currentTime := time.Now()
	currentYear := currentTime.Year()
	currentMonth := int(currentTime.Month())
	currentDay := currentTime.Day()

	text = convertChineseDate(text)

	reDateInfo := regexp.MustCompile(`[^\d]*(?:(\d{2,4})年)?(?:(\d{1,2})月)?(?:(\d{1,2})(?:日|号))?`)
	dateInfo := reDateInfo.FindStringSubmatch(text)
	if dateInfo == nil || (dateInfo[1] == "" && dateInfo[2] == "" && dateInfo[3] == "") {
		return "", "", ""
	}

	var year, month, day string
	if dateInfo[1] != "" {
		year = dateInfo[1]
	} else {
		year = strconv.Itoa(currentYear)
	}
	if dateInfo[2] != "" {
		month = dateInfo[2]
	} else {
		month = strconv.Itoa(currentMonth)
	}
	if dateInfo[3] != "" {
		day = dateInfo[3]
	} else {
		day = strconv.Itoa(currentDay)
	}

	return year, month, day
}

// 识别文本中的各种日期格式
func findDatesInText(text string) (string, string, string) {
	pattern := `(?:(\d{2,4})[.-])?(\d{1,2})[.-](\d{1,2})`
	re := regexp2.MustCompile(pattern, regexp2.None)
	if match, _ := re.FindStringMatch(text); match != nil {
		year := match.GroupByNumber(1).String()
		month := match.GroupByNumber(2).String()
		day := match.GroupByNumber(3).String()

		if year == "" {
			year = strconv.Itoa(time.Now().Year())
		}

		return year, month, day
	}
	return "", "", ""
}

// 识别文本中的年份
func findYearsInText(text string) (string, string, string) {
	//  0903 修改点：结尾不能是分
	pattern := `(?<!\d)(\d{4})(?!\d)(?!\s*(个|千|万|亿|元|块|版|米|人|m|k|w|分))`
	re := regexp2.MustCompile(pattern, regexp2.None)
	if match, _ := re.FindStringMatch(text); match != nil {
		year, _ := strconv.Atoi(match.GroupByNumber(1).String())
		currentMonth := time.Now().Month()
		currentDay := time.Now().Day()
		return strconv.Itoa(year), strconv.Itoa(int(currentMonth)), strconv.Itoa(currentDay)
	}
	return "", "", ""
}
