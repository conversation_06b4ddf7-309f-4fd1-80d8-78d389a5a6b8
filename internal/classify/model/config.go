package model

type ModelConfig struct {
	Config ClassifyBootConfig `toml:"classify"`
}

type ClassifyBootConfig struct {
	ConfigPath string `toml:"config_path"`
}

type ServiceConfig struct {
	Domain     *ModelInfo `toml:"domain"`     // 领域
	Emergency  *ModelInfo `toml:"emergency"`  // 突发
	Timeliness *ModelInfo `toml:"timeliness"` // 时效性

}
type ConfigASE struct {
	AppID     string `json:"appid"`
	ApiKey    string `json:"apikey"`
	ApiSecret string `json:"api_secret"`
}

type ModelInfo struct {
	Enabled bool   `json:"enabled"`
	Model   *Model `json:"model"`
}

type Model struct {
	Default     string     `json:"default"`
	Versions    []*Version `json:"versions"`
	VersionsMap map[string]*Version
}

type Version struct {
	Name       string   `json:"name"`
	ResDim     int      `json:"res_dim"`
	Token      string   `json:"token"`
	Model      string   `json:"model"`
	Timeout    int      `json:"timeout"`
	Lables     []string `json:"labels"`
	OutputName string   `json:"output_name"`
}
