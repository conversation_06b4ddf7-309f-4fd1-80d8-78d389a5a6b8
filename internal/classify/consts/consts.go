package consts

const (
	AseInstance_Domain     = "domain"
	AseInstance_Emergency  = "emergency"
	AseInstance_Timeliness = "timeliness"
)

const (
	MODEL_NAME = "classify"
	TOML_NAME  = "classify"
)

var DomainMap = map[string]int{
	"编程":   5014,
	"历史":   5007,
	"美食":   5005,
	"汽车":   5012,
	"软件":   5104,
	"数码":   5105,
	"前沿科技": 5106,
	"升学":   5101,
	"社会考试": 5102,
	"职场技能": 5103,
	"古文":   5108,
	"文学作品": 5109,
	"财经知识": 5110,
	"行业信息": 5111,
	"其他教育": 5002,
	"其他财经": 5112,
	"医疗":   5001,
	"体育":   5010,
	"旅游":   5004,
	"法律":   5003,
	"军事":   5006,
	"生活":   5009,
	"娱乐":   5015,
	"涉政安全": 105,
	"其他":   5015,
}

const (
	TIMELINESS_STRONG int32 = 0 // 时效性强
	TIMELINESS_NORMAL int32 = 1 // 时效性中
	TIMELINESS_WEAK   int32 = 2 // 时效性弱

	DOMAIN_OTHER  int32 = 0
	DOMAIN_SPORTS int32 = 5010
	DOMAIN_EXAM   int32 = 5102
)

var TimelinessDomainMap = map[int32]string{
	DOMAIN_OTHER:  "其他",
	DOMAIN_SPORTS: "体育",
	DOMAIN_EXAM:   "社会考试",
}

// 0: '中时效',1: '强时效', 2: '中时效', 3: '弱时效',
// 4: '强时效', 5: '中时效'
var LabelMap = map[int]int32{
	0: TIMELINESS_NORMAL,
	1: TIMELINESS_STRONG,
	2: TIMELINESS_NORMAL,
	3: TIMELINESS_WEAK,
	4: TIMELINESS_STRONG,
	5: TIMELINESS_NORMAL,
}

const (
	// InputIds 模型输入token
	InputIds string = "input_ids"
	// AttentionMask 模型输入mask
	AttentionMask string = "attention_mask"
	// Logits 模型输出
	Logits string = "logits"
)
