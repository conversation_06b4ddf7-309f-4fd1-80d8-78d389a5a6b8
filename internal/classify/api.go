package classify

import (
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/classify/config"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/classify/model"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/classify/service"
	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
)

// 初始化函数
func Init() error {

	// 解析自定义toml配置块
	modelConfig := &model.ModelConfig{}

	err := goboot.UnmarshalConf(modelConfig)
	if err != nil {
		return err
	}

	// 初始化全局服务
	service.GService = &service.Service{}
	// 加载服务配置
	c, err := config.LoadServiceConfig(modelConfig.Config.ConfigPath)
	if err != nil {
		return err
	}
	// 将加载的配置赋值给全局服务
	service.GService.Config = c

	// 注册路由
	service.GService.RegisterRouter()

	return nil
}
