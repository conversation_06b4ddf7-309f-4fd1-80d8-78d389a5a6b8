{"ase": [{"Module": "ase", "Code": 21001, "Message": "code:21001, message:无法生成ASE请求json序列化数据, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "ase", "Code": 21002, "Message": "code:21002, message:无法生成ASE请求的请求体数据, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "ase", "Code": 21003, "Message": "code:21003, message:引擎计算失败, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "ase", "Code": 21004, "Message": "code:21004, message:返回数据base64解码失败, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "ase", "Code": 21005, "Message": "code:21005, message:返回数据json反序列化解码失败, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "ase", "Code": 21006, "Message": "code:21006, message:请求ASE超时, detail:, tags:{}, parents:[]", "State": "running"}], "bizSearch": [{"Module": "bizSearch", "Code": 15001, "Message": "code:15001, message:timeout, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "bizSearch", "Code": 15002, "Message": "code:15002, message:无法生成Biz请求json序列化数据, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "bizSearch", "Code": 15003, "Message": "code:15003, message:无法生成Biz请求的请求体数据, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "bizSearch", "Code": 15004, "Message": "code:15004, message:召回失败, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "bizSearch", "Code": 15005, "Message": "code:15005, message:返回数据解码失败, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "bizSearch", "Code": 15006, "Message": "code:15006, message:请求返回结果code不为0, detail:, tags:{}, parents:[]", "State": "running"}], "classify": [{"Module": "classify", "Code": 13001, "Message": "code:13001, message:exec result unmarshal error, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "classify", "Code": 13002, "Message": "code:13002, message:timeout error, detail:, tags:{}, parents:[]", "State": "running"}], "common": [{"Module": "common", "Code": 10001, "Message": "code:10001, message:invalid input, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "common", "Code": 10002, "Message": "code:10002, message:missing field, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "common", "Code": 10003, "Message": "code:10003, message:invalid json, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "common", "Code": 10004, "Message": "code:10004, message:parse failed, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "common", "Code": 10005, "Message": "code:10005, message:internal error, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "common", "Code": 10006, "Message": "code:10006, message:router unsupported, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "common", "Code": 10007, "Message": "code:10007, message:unknow error, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "common", "Code": 10008, "Message": "code:10008, message:test discard error, detail:, tags:{}, parents:[]", "State": "discard"}], "embedding": [{"Module": "embedding", "Code": 16001, "Message": "code:16001, message:model not support, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "embedding", "Code": 16002, "Message": "code:16002, message:model input marshal failed, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "embedding", "Code": 16003, "Message": "code:16003, message:model output unmarshal failed, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "embedding", "Code": 16004, "Message": "code:16004, message:ase http request failed, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "embedding", "Code": 16005, "Message": "code:16005, message:rank infer failed, detail:, tags:{}, parents:[]", "State": "running"}], "intent": [{"Module": "intent", "Code": 14001, "Message": "code:14001, message:向量计算模块启动错误, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "intent", "Code": 14002, "Message": "code:14002, message:请求向量计算超时, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "intent", "Code": 14003, "Message": "code:14003, message:向量计算错误, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "intent", "Code": 14004, "Message": "code:14004, message:创建向量库对象错误, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "intent", "Code": 14005, "Message": "code:14005, message:向量库搜索错误, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "intent", "Code": 14006, "Message": "code:14006, message:lac分词器未初始化, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "intent", "Code": 14007, "Message": "code:14007, message:lac分词计算错误, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "intent", "Code": 14008, "Message": "code:14008, message:lac分词计算超时, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "intent", "Code": 14009, "Message": "code:14009, message:lac分词结果为空, detail:, tags:{}, parents:[]", "State": "running"}], "modelserver": [{"Module": "modelserver", "Code": 22001, "Message": "code:22001, message:model infer failed, detail:, tags:{}, parents:[]", "State": "running"}], "mongo": [{"Module": "mongo", "Code": 24001, "Message": "code:24001, message:Connection Error : , detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "mongo", "Code": 24002, "Message": "code:24002, message:<PERSON> Error : , detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "mongo", "Code": 24003, "Message": "code:24003, message:Find Error : , detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "mongo", "Code": 24004, "Message": "code:24004, message:Disconnect Error : , detail:, tags:{}, parents:[]", "State": "running"}], "postProcess": [{"Module": "postProcess", "Code": 23001, "Message": "code:23001, message:filter failed, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "postProcess", "Code": 23002, "Message": "code:23002, message:sift failed, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "postProcess", "Code": 23003, "Message": "code:23003, message:distinct failed, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "postProcess", "Code": 23004, "Message": "code:23004, message:limit failed, detail:, tags:{}, parents:[]", "State": "running"}], "prerank": [{"Module": "prerank", "Code": 12001, "Message": "code:12001, message:model not support, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "prerank", "Code": 12002, "Message": "code:12002, message:model input marshal failed, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "prerank", "Code": 12003, "Message": "code:12003, message:model output unmarshal failed, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "prerank", "Code": 12004, "Message": "code:12004, message:ase http request failed, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "prerank", "Code": 12005, "Message": "code:12005, message:prerank infer failed, detail:, tags:{}, parents:[]", "State": "running"}], "quBase": [{"Module": "quBase", "Code": 17001, "Message": "code:17001, message:marshal failed, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "quBase", "Code": 17002, "Message": "code:17002, message:unmarshal failed, detail:, tags:{}, parents:[]", "State": "running"}], "qubaike": [{"Module": "qubaike", "Code": 19001, "Message": "code:19001, message:mongo search request failed, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "qubaike", "Code": 19002, "Message": "code:19002, message:driect mongo search failed, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "qubaike", "Code": 19003, "Message": "code:19003, message:mongo no documents, detail:, tags:{}, parents:[]", "State": "running"}], "rank": [{"Module": "rank", "Code": 11001, "Message": "code:11001, message:model not support, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "rank", "Code": 11002, "Message": "code:11002, message:model input marshal failed, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "rank", "Code": 11003, "Message": "code:11003, message:model output unmarshal failed, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "rank", "Code": 11004, "Message": "code:11004, message:ase http request failed, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "rank", "Code": 11005, "Message": "code:11005, message:rank infer failed, detail:, tags:{}, parents:[]", "State": "running"}], "recallVector": [{"Module": "recallVector", "Code": 20001, "Message": "code:20001, message:calc Embedding failed, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "recallVector", "Code": 20002, "Message": "code:20002, message:milvus agent search err, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "recallVector", "Code": 20003, "Message": "code:20003, message:mongo agent search failed, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "recallVector", "Code": 20004, "Message": "code:20004, message:service timeout, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "recallVector", "Code": 20005, "Message": "code:20005, message:embedding model search err, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "recallVector", "Code": 20006, "Message": "code:20006, message:query black list err, detail:, tags:{}, parents:[]", "State": "running"}], "rerank": [{"Module": "rerank", "Code": 18001, "Message": "code:18001, message:请求数据为空, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "rerank", "Code": 18002, "Message": "code:18002, message:请求query为空, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "rerank", "Code": 18003, "Message": "code:18003, message:请求数据id重复, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "rerank", "Code": 18004, "Message": "code:18004, message:traceId为空, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "rerank", "Code": 18005, "Message": "code:18005, message:内部逻辑错误, detail:, tags:{}, parents:[]", "State": "running"}, {"Module": "rerank", "Code": 18006, "Message": "code:18006, message:内部数据转换错误, detail:, tags:{}, parents:[]", "State": "running"}]}