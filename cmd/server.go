package cmd

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
)

// 创建根命令
var rootCmd = &cobra.Command{
	Use:   "lynxiao-serach",
	Short: "Search AI Applications",
	Long:  `lynxiao ai search applications`,
}

type ServerConfig struct {
	ConfigPath string `json:"config_path"`
}

var GServerConfig *ServerConfig = &ServerConfig{}

var serverCmd *cobra.Command = &cobra.Command{
	Use:   "server",
	Short: "lynxiao  search ai server",
}

func Init() {
	// 注册 -name 标志
	serverCmd.Flags().StringVarP(&GServerConfig.ConfigPath, "config", "c", "", "config path:file or dir")
	// 将 helloCmd 添加到根命令
	rootCmd.AddCommand(serverCmd)
	rootCmd.AddCommand(errorCmd)

	// 执行根命令
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}
