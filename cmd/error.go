package cmd

import (
	"encoding/json"
	"fmt"
	"os"
	"time"

	_ "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error"
	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"
	"github.com/spf13/cobra"
)

type printError struct {
	Module      string   //模块
	Code        int      // 错误码
	Message     string   // 错误信息
	State  string //状态
}

//Generate ErrorCode table
var errorCmd = &cobra.Command{
    Use:   "error",
    Short: "Generate Error Code Table",
    Run: func(cmd *cobra.Command, args []string) {
        // 将错误码写入本地文件
        file, err := os.Create(fmt.Sprintf("error_codes_%s.json", time.Now().Format("2006-01-02_15-04-05")))
        if err != nil {
            fmt.Println("创建文件失败:", err)
            os.Exit(1)
        }
        
        encoder := json.NewEncoder(file)
        encoder.SetIndent("", "  ")  // 设置缩进，使输出更易读


		var printErrors = map[string][]printError{}

		for _,v := range errtypes.Global_Errors {
			_,ok := printErrors[v.Module()]
			if !ok {
				printErrors[v.Module()] = make([]printError, 0,len(errtypes.Global_Errors))
			}
			printErrors[v.Module()] = append(printErrors[v.Module()], printError{
				Module: v.Module(),
				Code: v.Code(),
				Message: v.String(),
				State: v.State(),
			})
		}

        if err := encoder.Encode(printErrors); err != nil {
            fmt.Println("写入文件失败:", err)
            os.Exit(1)
        }
		file.Close()
		os.Exit(0)
    },
}
