# 🔄 智能重排服务 (Rerank Service) - 完整版

[![Go 版本](https://img.shields.io/badge/Go-1.23.3-blue.svg)](https://golang.org/)
[![许可证](https://img.shields.io/badge/License-Apache%202.0-green.svg)](LICENSE)
[![构建状态](https://img.shields.io/badge/Build-Passing-brightgreen.svg)]()
[![测试覆盖率](https://img.shields.io/badge/Coverage-85%25-yellow.svg)]()

> 🚀 基于 Go 语言构建的高性能、领域专用搜索结果智能重排服务，支持13种重排策略

## ✨ 核心特性

- 🎯 **13种重排策略** - 医疗5版本、体育4策略、新闻2版本、通用1版本、历史1版本
- ⚡ **高性能处理** - 支持并发处理，QPS 可达 1000+
- 🧠 **智能评分** - 多维度特征融合，包括文本匹配、时效性、权威性等
- 🔧 **灵活配置** - 丰富的参数调优选项，适应不同业务场景
- 📊 **完善监控** - 全面的指标监控和可观测性支持
- 🐳 **云原生** - 支持 Docker 容器化和 Kubernetes 部署

## 📋 完整重排策略列表

### 🏥 医疗重排策略 (5种)

| 模型标识 | 常量名称 | 调用方法 | 版本特性 | 适用场景 |
|----------|----------|----------|----------|----------|
| `5001_V20250516` | Medical_V0516 | Rank0516 | 产品策略排序、超短文本处理 | 生产环境主推 |
| `5001_V20250310` | Medical_V0310 | Rank0310 | 标准医疗重排、TitleSeg分词 | 平衡性能效果 |
| `5001_V20250620` | Medical_V0620 | Rank0310 | 优化版本、限制16条处理 | 高频查询优化 |
| `5001_V20251218` | Medical_V1218 | Rank1218 | 时效性增强、TimeStrengthReg | 时间敏感查询 |
| `1/5001/5001_V1120/5001_V20251120` | MEDICAL/HEALTH/V1120/Medical_V1120 | Rank1120 | 经典算法、稳定可靠 | 兜底策略 |

### ⚽ 体育重排策略 (4种)

| 意图/时效性 | 策略描述 | 算法实现 | 适用场景 |
|-------------|----------|----------|----------|
| HOTTOPIC | 热点话题重排 | newsRerank | 体育热点、话题讨论 |
| 强时效性 (0) | 实时赛事重排 | newsRerank | 比赛直播、即时比分 |
| 中时效性 (1) | 赛事回顾重排 | mediumScore | 赛事分析、球队动态 |
| 弱时效性 (2) | 历史数据重排 | weakScore | 球员资料、历史记录 |

### 📰 新闻重排策略 (2种)

| 模型标识 | 版本特性 | 核心功能 | 适用场景 |
|----------|----------|----------|----------|
| `news_V20250421` | 基础新闻重排 | 时间匹配优化、精确时间处理 | 标准新闻查询 |
| `news_V20250522` | 多样性新闻重排 | 主题去重、避免内容聚集 | 多样性要求高 |

### 🌐 通用重排策略 (1种)

| 策略 | 特性 | 适用场景 |
|------|------|----------|
| 通用重排 | 基础文本匹配、轻量级算法 | 兜底策略、未匹配领域 |

### 📚 历史重排策略 (1种)

| 模型标识 | 常量名称 | 特性 | 适用场景 |
|----------|----------|------|----------|
| `5007` | HISTORY | 历史内容重排 | 历史相关查询 |

## 🏗️ 完整系统架构

### 重排策略路由架构

```mermaid
graph TB
    subgraph "请求入口"
        A[HTTP 请求] --> B[RerankService]
        B --> C[performRanking]
    end
    
    subgraph "策略路由层"
        C --> D{switch domainID}
        
        D -->|MEDICAL/V1120/Medical_V1120/HEALTH| E1[medicalRerank.Rank1120]
        D -->|V1218/Medical_V1218| E2[medicalRerank.Rank1218]
        D -->|Medical_V0310/Medical_V0620| E3[medicalRerank.Rank0310]
        D -->|Medical_V0516| E4[medicalRerank.Rank0516]
        D -->|SPORTS| E5[sportsRerank.Rank]
        D -->|News_V20250421/News_V20250522| E6[newsRerank.Rank20250421]
        D -->|HISTORY| E7[historyRerank.Rank]
        D -->|default| E8[commonRerank.Rank]
    end
    
    subgraph "医疗重排算法"
        E1 --> F1[V1120: 基础医疗重排<br/>时效性检测+医学实体识别]
        E2 --> F2[V1218: 增强医疗重排<br/>优化时效性处理]
        E3 --> F3[V0310/V0620: 标准医疗重排<br/>分词优化+质量评分]
        E4 --> F4[V0516: 最新医疗重排<br/>产品策略+超短文本处理]
    end
    
    subgraph "体育重排算法"
        E5 --> G1{Intent判断}
        G1 -->|HOTTOPIC| G2[热点话题: newsRerank算法]
        G1 -->|NEWS/其他| G3{TimeLiness判断}
        G3 -->|0强时效| G4[实时赛事: newsRerank算法]
        G3 -->|1中时效| G5[赛事回顾: mediumScore算法]
        G3 -->|2弱时效| G6[历史数据: weakScore算法]
    end
    
    subgraph "新闻重排算法"
        E6 --> H1{版本判断}
        H1 -->|V20250421| H2[基础新闻重排<br/>时间匹配+范围提取]
        H1 -->|V20250522| H3[多样性新闻重排<br/>主题去重+交集检测]
    end
    
    subgraph "其他重排算法"
        E7 --> I1[历史重排算法]
        E8 --> I2[通用重排算法]
    end
    
    F1 --> J[结果返回]
    F2 --> J
    F3 --> J
    F4 --> J
    G2 --> J
    G4 --> J
    G5 --> J
    G6 --> J
    H2 --> J
    H3 --> J
    I1 --> J
    I2 --> J
    
    style E1 fill:#e8f5e8
    style E2 fill:#f3e5f5
    style E3 fill:#fff3e0
    style E4 fill:#e1f5fe
    style E5 fill:#fce4ec
    style E6 fill:#e0f2f1
    style E7 fill:#f1f8e9
    style E8 fill:#f5f5f5
```

## 🎯 算法特性矩阵

| 特性功能 | V0516 | V0310 | V0620 | V1218 | V1120 | 体育 | 新闻 | 通用 | 历史 |
|----------|-------|-------|-------|-------|-------|------|------|------|------|
| **产品策略排序** | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **超短文本处理** | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **库优先级排序** | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **医学实体识别** | ✅ | ✅ | ✅ | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ |
| **时效性检测** | ❌ | ❌ | ❌ | ✅ | ✅ | ✅ | ✅ | ❌ | ❌ |
| **分词优化** | ❌ | TitleSeg | TitleSeg0620 | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| **处理数量限制** | 无 | 无 | 16条 | 无 | 无 | 无 | 无 | 无 | 无 |
| **意图识别** | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ | ❌ |
| **时效性分级** | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ | ❌ |
| **时间匹配** | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ✅ | ❌ | ❌ |
| **多样性排序** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ |
| **主题去重** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ |
| **突发新闻检测** | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ |

## 🚀 快速开始

### 环境要求

- Go 1.23.3 或更高版本
- Docker（可选）

### 安装部署

```bash
# 克隆仓库
git clone https://github.com/your-org/rerank-service.git
cd rerank-service

# 安装依赖
go mod tidy

# 编译服务
go build -tags=rerank -o rerank-server

# 启动服务
./rerank-server server -c ./config/rerank
```

## 📖 API 使用示例

### 医疗重排示例

```json
{
  "header": {"traceId": "medical-001"},
  "payload": {
    "model": "5001_V20250516",
    "topK": 10,
    "rankCollections": ["医疗权威库", "专业医学库"],
    "data": [{
      "query": "糖尿病症状治疗",
      "docs": [{"id": "1", "title": "糖尿病的早期症状", "_rank_score": 0.95}]
    }]
  }
}
```

### 体育重排示例

```json
{
  "header": {"traceId": "sports-001"},
  "payload": {
    "model": "5010",
    "intent": "NEWS",
    "timeliness": "0",
    "data": [{
      "query": "世界杯决赛结果",
      "docs": [{"id": "1", "title": "世界杯决赛精彩回顾", "_rank_score": 0.92}]
    }]
  }
}
```

### 新闻重排示例

```json
{
  "header": {"traceId": "news-001"},
  "payload": {
    "model": "news_V20250522",
    "data": [{
      "query": "2024年科技新闻",
      "docs": [{"id": "1", "title": "AI技术突破性进展", "_rank_score": 0.88}]
    }]
  }
}
```

## 🔧 配置说明

### 重排策略配置

```toml
# config/rerank/rerank.toml

# 医疗重排配置
[medical]
enable_entity_recognition = true
authority_weight = 0.4
recency_weight = 0.3
enable_product_strategy = true  # V0516专用

# 体育重排配置
[sports]
enable_live_events = true
time_decay_factor = 0.1
diversity_threshold = 0.8
intent_detection = true

# 新闻重排配置
[news]
enable_breaking_news = true
source_credibility_weight = 0.5
diversity_window = 5
enable_topic_dedup = true  # V20250522专用

# 通用重排配置
[common]
basic_text_matching = true
simple_authority_scoring = true
```

## 📊 性能基准

| 重排策略 | 平均延迟 | QPS | 内存使用 | 准确性(NDCG@10) |
|----------|----------|-----|----------|-----------------|
| 医疗V0516 | 85ms | 1200+ | 1.8GB | 0.87 |
| 医疗V0310 | 75ms | 1400+ | 1.5GB | 0.85 |
| 医疗V0620 | 45ms | 2200+ | 1.2GB | 0.83 |
| 体育重排 | 70ms | 1500+ | 1.4GB | 0.82 |
| 新闻重排 | 90ms | 1100+ | 1.6GB | 0.88 |
| 通用重排 | 35ms | 2800+ | 0.8GB | 0.75 |

## 🤝 参与贡献

我们欢迎社区贡献！请查看我们的[贡献指南](CONTRIBUTING.md)了解详情。

## 📄 开源许可

本项目采用 Apache License 2.0 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 📞 技术支持

- 📧 **邮箱**: [<EMAIL>](mailto:<EMAIL>)
- 💬 **问题反馈**: [GitHub Issues](https://github.com/your-org/rerank-service/issues)
- 📖 **文档**: [项目 Wiki](https://github.com/your-org/rerank-service/wiki)

---

<p align="center">
  <strong>⭐ 支持13种重排策略的智能重排服务 ⭐</strong>
</p>

<p align="center">
  用 ❤️ 构建，来自重排服务社区
</p>
