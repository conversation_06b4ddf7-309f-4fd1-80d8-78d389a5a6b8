# 1. 工程目录

- main.go:

  - 该文件是整个程序的入口,内部包含了以下过程:

  ```go
  package main
  
  import (
  	"time"
  
  	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/app"
  	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/cmd"
  	goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
  )
  
  func main() {
  	//命令行初始化，获取config path
  	cmd.Init()
  
  	//1.根据配置文件的路径,解析目录下的所有配置文件
  	//2.根据配置文件启动对应的基础组件
  	err := goboot.InitFromConfig(cmd.GServerConfig.ConfigPath)
  	if err != nil {
  		panic(err)
  	}
  
  	//检查log,elklog,tlb,http server等组件是否配置正确;
  	//配置不正确,则panic
  	goboot.Logger().Must()
  	goboot.Elklog().Must()
  	goboot.TlbSdk().Must()
  	goboot.HttpServers().Must()
  
  	//启动app,会根据go build -tags中的参数,按需编译加载组件
  	app.Init()
  
  	//启动监听服务
  	goboot.RunServer()
  
  	//捕获退出信号,进行tlb的去注册;
  	//sleep 15秒,等待客户端更新tlb注册信息后,再退出
  	goboot.Singal(nil, 15*time.Second)
  }
  ```

- App:

  - export.go:main.go 调用的入口

  ```go
  package app
  
  import "fmt"
  
  type InitFunc func() error
  
  var InitFuncs = map[string]InitFunc{}
  
  //供main.go中调用的接口;
  //对注册到InitFuncs中的组件进行启动
  func Init() {
  	for key, fn := range InitFuncs {
  		fmt.Println("start init", key)
  		err := fn()
  		if err != nil {
  			panic(fmt.Sprintf("init %s fail: %s", key, err.Error()))
  		}
  		fmt.Println("success init", key)
  	}
  }
  ```

  - xxx.go: 如 prerank.go

  ```go
  //go:build prerank
  // +build prerank
  
  //go:build prerank 是指若go build -tags包含了prerank参数;
  //则该文件会被进行编译,进而执行该文件的init函数(注册业务);
  //若不包含,则不会执行init进行组件业务注册.
  package app
  
  import (
  	"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/prerank"
  )
  
  func init() {
  	InitFuncs["prerank"] = prerank.Init
  }
  ```

- Cicd:

  - go1_23_2_ubuntu.dockerfile:基于1.23.2版本的最小镜像单元,无特殊需求可使用该镜像;

- Cmd:

  - export.go: 注册根命令;

  - xxx.go: 如prerank.go:

    ```go
    //go:build prerank
    
    //控制编译:go:build prerank
    package cmd
    
    import (
    	"fmt"
    )
    
    var Prerank string
    
    type PrerankConfig struct {
    	Name string `json:"name"`
    }
    
    var PN = ""
    
    func init() {
    
    	fmt.Println("prerank cmd")
    
    	// 如果编译prerank,则注册--prerank_name 的参数
    	ServerCmd.Flags().StringVarP(&PN, "prerank_name", "", "", "prerank name")
    }
    ```

- Config:

  - 以物理服务为单位进行组织,如:qu和prerank,是两个物理服务,则分为两个文件夹:config/prerank和config/qu;
  - 单个物理服务内,分为基础配置和业务配置:
    - goboot.toml: 基础配置;
    - qu.toml: qu业务配置.

- Internal:按业务组织分文件夹组织,如qu和prerank各自单独一个文件夹;且提供Init函数,进行路由注册:

  ```go
  package prerank
  
  import (
  "fmt"
  
  goboot "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go"
  "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora"
  pandora_proto "code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/third_party/pandora/proto"
  )
  
  type Request struct {
  Name string
  }
  
  type Response struct {
  }
  
  //pandora 协议接口
  var Prerank_Pandora = pandora.NewPandoraProto[Request, Response]()
  
  //pandora 接口回调
  func pandoraProcess(ctx *pandora_context.PandoraContext, req *pandora_proto.PandoraRequestMessage[Request]) *pandora_proto.PandoraResponseMessage[Response] {
  fmt.Println("prerank  process")
  resp := pandora_proto.NewPandoraResponseMessage[Response]()
  return resp
  }
  
  //提供init函数
  func Init() error {
  fmt.Println("prerank init")
  router := goboot.HttpServers().DefaultInstance().Router
  //执行路由注册
  v1 := router.Group("/prerank")
  {
  v1.GET("/api/v2", Prerank_Pandora.WrapperGin(pandoraProcess))
  }
  
  return nil
  }
  ```

# 2. 新建业务

## 2.1 新增app 注册

- 在app 文件夹下,新增xxx.go文件,xxx为对应的业务名称;

- xxx.go 进行如下编写:注意prerank 替换为xxx 业务名;

  	```go
  	//go:build prerank
  	// +build prerank
  	//go:build prerank 是指若go build -tags包含了prerank参数;
  	//则该文件会被进行编译,进而执行该文件的init函数(注册业务);
  	//若不包含,则不会执行init进行组件业务注册.
  	package app
  	
  	import (
  		"code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/internal/prerank"
  	)
  	
  	func init() {
  		InitFuncs["prerank"] = prerank.Init
  	}

  ```
  
  ```

## 2.2 新增internal业务

- 在internal 中新增文件夹xxx(业务名),并对外提供Init() error函数;
- 在Init() error函数中进行路由注册,或其它业务实例初始化(tokenizer实例初始化);
- 参考相关文件实现;

#3. 新增命令行(非必须)
参考cmd/下的prerank.go,在serverCmd下注册flag解析

# 3. 自定义错误码

## 错误码设计：

> 1. 使用五位数字作为自定义错误码，前两位为业务类型，后三位为错误类型;
> 2. 错误码从10000开始，10xxx为通用错误码，11xxx为精排错误码，12xxx为粗排错误码，以此类推
> 3. 错误码只增不减，废弃错误码使用注释说明

## 使用说明：

1. error目录下，**errors.go**是自定义错误码的主类，实现了error接口

2. **common.go**定义了所有业务通用错误码，使用10xxx，同时定义了各业务错误码前缀

   ```go
   // 错误码前缀
   const (
   	// 通用错误
   	CommonError int = (10 + iota) * 1000
   	// 精排错误
   	RankError
   	// 粗排错误
   	PrerankError
   	// 各业务根据需求往后加
   )
   ```

   **注意**：因为使用了`iota`，新业务前缀只能往后添加，否者会影响其他业务的前缀

3. 各业务的错误码，单独创建一个文件，例如：**prerank.go**，定义自己的错误码

   ```go
   package selferrors
   
   // 粗排错误码
   const (
   	ErrorCodeModelNotSupport = PrerankError + iota
   	ErrorCodeMarshalFailed
   	ErrorCodeUnmarshalFailed
   	ErrorCodeAseRequestFailed
   	ErrorCodeInferTimeout
   	ErrorCodeInferFailed
   )
   
   var (
   	// PrerankModelNotSupport 模型不支持
   	PrerankModelNotSupport = newSelfError(ErrorCodeModelNotSupport, "model not support")
   	// PrerankMarshalFailed 模型输入序列化失败
   	PrerankMarshalFailed = newSelfError(ErrorCodeMarshalFailed, "model input marshal failed")
   	// PrerankUnmarshalFailed 模型输出反序列化失败
   	PrerankUnmarshalFailed = newSelfError(ErrorCodeUnmarshalFailed, "model output unmarshal failed")
   	// PrerankAseRequestFailed ase推理请求失败
   	PrerankAseRequestFailed = newSelfError(ErrorCodeAseRequestFailed, "ase http request failed")
   	// PrerankInferFailed 推理失败
   	PrerankInferFailed = newSelfError(ErrorCodeInferFailed, "rank infer failed")
   )
   ```

# 4. 新增CICD

-	在cicd文件夹下,新增dockerfile 文件进行归档即可;
-	docker file的命名,需要注名:go的版本+额外组件+基础操作系统

# 5. 编译

## 5.1 单个组件编译

-	执行go build -tags=xxx -o server, xxx 为业务名,与app 文件夹下的//go:build xxx 对应;
-	执行后,xxx 业务会编译到server的可执行文件中;

## 5.2 多个组件编译

- 执行go build -tags=xx1,xx2,xx3 -o server, xx1,xx2,xx3 为业务名,与app 文件夹下的//go:build xxx 对应;
- 执行后,xx1,xx2,xx3 业务会同时被编译到server的可执行文件中;

## 5.3 所有能力服务编译
- 执行go build -tags=all,boot_all,cloud_unlock -o lynxiao-ai-search

# 6. 运行

- shell执行命令：{可执行文件} server -c {配置文件路径},eg: ./lynxiao-ai-search server -c ./config/recall-vector
- 所有能力服务共同便后，shell执行命令：{可执行文件} server -c {配置文件路径},eg: ./lynxiao-ai-search server -c ./config/all