package selferrors

import "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"

var (
	// 预处理后请求数据为空
	QuBaseError_ReqEmptyError = errtypes.QuBaseError.NewSelfError("请求数据为空")
	// 预处理后请求数据为空
	QuBaseError_ReqPreEmptyError = errtypes.QuBaseError.NewSelfError("预处理后请求数据为空")
	// QuBaseMarshalFailed 序列化失败
	QuBaseError_MarshalFailed = errtypes.QuBaseError.NewSelfError("marshal failed")
	// QuBaseUnmarshalFailed 反序列化失败
	QuBaseError_UnmarshalFailed = errtypes.QuBaseError.NewSelfError("unmarshal failed")
	// 内部逻辑错误
	QuBaseError_InternelError = errtypes.QuBaseError.NewSelfError("内部逻辑错误")
	// 干预错误
	QuBaseError_InterveneError = errtypes.QuBaseError.NewSelfError("干预错误")
)
