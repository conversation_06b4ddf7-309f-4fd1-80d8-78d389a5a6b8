package selferrors

import "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"

const (
	// PrerankError 前排错误
	Timeout             = "timeout"
	MsgBizJsonMarshal   = "无法生成Biz请求json序列化数据"
	MsgBizRequestFailed = "无法生成Biz请求的请求体数据"
	MsgBizCalcFailed    = "召回失败"
	MsgResponseDecode   = "返回数据解码失败"
	MsgCodeFailed       = "请求返回结果code不为0"
)

var (
	// PrerankModelNotSupport 模型不支持
	BizSearchError_Timeout        = errtypes.BizSearchError.NewSelfError(Timeout)
	BizSearchError_JsonMarshal    = errtypes.BizSearchError.NewSelfError(MsgBizJsonMarshal)
	BizSearchError_RequestFailed  = errtypes.BizSearchError.NewSelfError(MsgBizRequestFailed)
	BizSearchError_CalcFailed     = errtypes.BizSearchError.NewSelfError(MsgBizCalcFailed)
	BizSearchError_ResponseDecode = errtypes.BizSearchError.NewSelfError(MsgResponseDecode)
	BizSearchError_CodeFailed     = errtypes.BizSearchError.NewSelfError(MsgCodeFailed)
)
