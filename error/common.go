package selferrors

import "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"

var (
	// Success 成功
	SuccessTypes_Success = errtypes.SuccessTypes.NewSelfError("success")
)
var (
	
	// InvalidInput 无效输入
	CommonError_InvalidInput = errtypes.CommonError.NewSelfError("invalid input")
	// MissingField 缺少字段
	CommonError_MissingField = errtypes.CommonError.NewSelfError("missing field")
	// InvalidJSON 无效json
	CommonError_InvalidJSON = errtypes.CommonError.NewSelfError("invalid json")
	// ParseFailed 请求解析失败
	CommonError_ParseFailed = errtypes.CommonError.NewSelfError("parse failed")
	// InternalError 内部错误
	CommonError_InternalError = errtypes.CommonError.NewSelfError("internal error")
	// RouterUnSupported 路由不支持
	CommonError_RouterUnSupported = errtypes.CommonError.NewSelfError("router unsupported")
	// UnKouwnError 未知错误
	CommonError_UnKouwnError = errtypes.CommonError.NewSelfError("unknow error")
	// TypeConvertFailed类型转化失败
	CommonError_TypeConvertFailed = errtypes.CommonError.NewSelfError("type convert failed")

	//对于不使用的错误码setdiscard
	CommonError_TestDiscardFailed = errtypes.CommonError.NewSelfError("test discard error").SetDiscard()
)
