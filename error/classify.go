package selferrors

import "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"

const ()

const (
	ExecResultUnmarshalError = "exec result unmarshal error"
	TimeoutError             = "timeout error"
)

var (
	// ASE能力返回的结果数据进行base64解码成功后的序列化，真实结果序列化的结果
	ClassifyError_ExecResultUnmarshalError = errtypes.ClassifyError.NewSelfError(ExecResultUnmarshalError)
	// 整体请求超时
	ClassifyError_Timeout = errtypes.ClassifyError.NewSelfError(TimeoutError)
)
