package selferrors

import "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"

var (
	// PrerankModelNotSupport 模型不支持
	PrerankError_ModelNotSupport = errtypes.PreRankError.NewSelfError("model not support")
	// PrerankMarshalFailed 模型输入序列化失败
	PrerankError_MarshalFailed = errtypes.PreRankError.NewSelfError("model input marshal failed")
	// PrerankUnmarshalFailed 模型输出反序列化失败
	PrerankError_UnmarshalFailed = errtypes.PreRankError.NewSelfError("model output unmarshal failed")
	// PrerankAseRequestFailed ase推理请求失败
	PrerankError_AseRequestFailed = errtypes.PreRankError.NewSelfError("ase http request failed")
	// PrerankInferFailed 推理失败
	PrerankError_InferFailed = errtypes.PreRankError.NewSelfError("prerank infer failed")
)
