package errtypes

type errModule struct {
	module     string
	baseNum    int
	currentNum int
	state      string
	selferrors []*SelfError
}

func newErrorModule(code int, module string) *errModule {
	return &errModule{
		module:     module,
		baseNum:    code,
		currentNum: code,
		state:      "running",
		selferrors: make([]*SelfError, 0, 64),
	}
}

func (em *errModule) NewSelfError(message string) *SelfError {
	em.currentNum += 1
	se := newSelfError(em.module, em.currentNum, message)

	if em.state == "discard" {
		se.SetDiscard()
	}

	em.selferrors = append(em.selferrors, se)

	return se
}

func (em *errModule) SetDiscard() *errModule {
	em.state = "discard"
	return em
}