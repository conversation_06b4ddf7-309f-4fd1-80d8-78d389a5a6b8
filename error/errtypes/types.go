package errtypes

import (
	"code.iflytek.com/Y_RDG-TURING/skynet/skynet-pandora-go/basis/structure/unsafe/berror"
)

var Global_Errors = []*SelfError{}

// SelfError 自定义错误,实现了error接口
type SelfError struct {
	module string //模块
	state  string //状态:"running" or "discard":正常|废弃
	berror.BError
}

func newSelfError(module string, code int, msg string) *SelfError {
	se := &SelfError{module: module, state: "running"}
	se.BError = *berror.NewBError(code, msg)
	Global_Errors = append(Global_Errors, se)
	return se
}

// 模块 信息
func (s *SelfError) Module() string {
	return s.module
}

// 状态信息
func (s *SelfError) State() string {
	return s.state
}

// 状态信息
func (s *SelfError) SetDiscard() *SelfError {
	s.state = "discard"
	return s
}

func (s *SelfError) Derived(parent error) *SelfError {
	news := &SelfError{module: s.module, state: s.state}
	news.BError = *s.BError.Derived(parent)
	return news
}

func (s *SelfError) Detaild(detail string) *SelfError {
	news := &SelfError{module: s.module, state: s.state}
	news.BError = *s.BError.Detaild(detail)
	return news
}

func (s *SelfError) Message() string {
	return s.BError.String()
}
