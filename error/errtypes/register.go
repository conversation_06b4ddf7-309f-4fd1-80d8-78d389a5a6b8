package errtypes

// 错误码前缀
//不要在动这个顺序，只准往后加
const (
	// 通用错误
	commonErrorCode int = (10 + iota) * 1000
	// 精排错误
	rankErrorCode
	// 粗排错误
	prerankErrorCode
	// query分类错误码
	classifyErrorCode
	// query意图错误码
	intentErrorCode
	// 聚合搜索错误码
	bizSearchErrorCode
	// 向量计算错误码
	embeddingErrorCode
	// 基础QU错误码
	quBaseErrorCode
	// 重排错误码
	rerankErrorCode
	// qubaike错误码
	qubaikeErrorCode
	// 向量召回错误码
	recallVectorErrorCode
	// ase错误码
	aseErrorCode
	// modelserver错误码
	modelserverErrorCode
	// 后处理错误码
	processErrorCode
	// Mongo 相关错误码
	mongoErrorCode
	// 分词计算错误码
	segErrorCode
	// 召回干预错误码
	RecallInterveneErrorCode
	// 各业务根据需求往后加
)

//独立success
const SuccessCode int = -1
var (
	SuccessTypes         = newErrorModule(SuccessCode, "success")
	CommonError          = newErrorModule(commonErrorCode, "common")
	RankError            = newErrorModule(rankErrorCode, "rank")
	PreRankError         = newErrorModule(prerankErrorCode, "prerank")
	ClassifyError        = newErrorModule(classifyErrorCode, "classify")              // query分类错误码
	IntentError          = newErrorModule(intentErrorCode, "intent")                  // query意图错误码
	BizSearchError       = newErrorModule(bizSearchErrorCode, "bizSearch")            // 聚合搜索错误码
	EmbeddingError       = newErrorModule(embeddingErrorCode, "embedding")            // 向量计算错误码
	QuBaseError          = newErrorModule(quBaseErrorCode, "quBase")                  // 基础QU错误码
	RerankError          = newErrorModule(rerankErrorCode, "rerank")                  // 重排错误码
	AseError             = newErrorModule(aseErrorCode, "ase")                        // ase错误码
	QubaikeError         = newErrorModule(qubaikeErrorCode, "qubaike")                // qubaike错误码
	RecallVectorError    = newErrorModule(recallVectorErrorCode, "recallVector")      // 向量召回错误码
	ModelserverError     = newErrorModule(modelserverErrorCode, "modelserver")        // modelserver错误码
	ProcessError         = newErrorModule(processErrorCode, "postProcess")            // 后处理错误码
	MongoError           = newErrorModule(mongoErrorCode, "mongo")                    // Mongo 相关错误码
	SegError             = newErrorModule(segErrorCode, "seg")                        //分词计算错误码
	RecallInterveneError = newErrorModule(RecallInterveneErrorCode, "sliceIntervene") //切片召回干预错误码
)

const Success = 0
const SuccessMsg = "success"
