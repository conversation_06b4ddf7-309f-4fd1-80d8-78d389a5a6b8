package selferrors

import "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"

var (
	// 请求数据为空
	RerankError_ReqDataEmptyError = errtypes.RerankError.NewSelfError("请求数据为空")
	// 请求query为空
	RerankError_ReqQueryEmptyError = errtypes.RerankError.NewSelfError("请求query为空")
	// 请求数据id重复
	RerankError_ReqIDDuplicateError = errtypes.RerankError.NewSelfError("请求数据id重复")
	// traceId为空
	RerankError_TracrIDEmpty = errtypes.RerankError.NewSelfError("traceId为空")
	// 内部逻辑错误
	RerankError_UnknowError = errtypes.RerankError.NewSelfError("内部逻辑错误")
	// 内部数据转换错误
	RerankError_DataTransferError = errtypes.RerankError.NewSelfError("内部数据转换错误")
)
