package selferrors

import "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"

var (
	// RankModelNotSupport 模型不支持
	RankError_ModelNotSupport = errtypes.RankError.NewSelfError("model not support")
	// RankMarshalFailed 模型输入序列化失败
	RankError_MarshalFailed = errtypes.RankError.NewSelfError("model input marshal failed")
	// RankUnmarshalFailed 模型输出反序列化失败
	RankError_UnmarshalFailed = errtypes.RankError.NewSelfError("model output unmarshal failed")
	// RankAseRequestFailed ase推理请求失败
	RankError_AseRequestFailed = errtypes.RankError.NewSelfError("ase http request failed")
	// RankInferFailed 推理失败
	RankError_InferFailed = errtypes.RankError.NewSelfError("rank infer failed")
)
