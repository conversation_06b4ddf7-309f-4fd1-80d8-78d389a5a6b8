package selferrors

import "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"

var (
	//请求参数解析错误
	SegError_SegReqParserError = errtypes.SegError.NewSelfError("请求参数解析错误")
	//请求数据为空
	SegError_SegReqEmptyError = errtypes.SegError.NewSelfError("请求数据为空")
	//预处理后请求数据为空
	SegError_SegReqPreEmptyError = errtypes.SegError.NewSelfError("预处理后请求数据为空")
	//预处理错误
	SegError_SegPreprocessError = errtypes.SegError.NewSelfError("query预处理错误")
	// 第三方服务不存在
	SegError_SegThirdServiceNotFoundError = errtypes.SegError.NewSelfError("第三方服务不存在")
	// 下游超时
	SegError_SegTimeoutError = errtypes.SegError.NewSelfError("下游超时")
	//调用分词计算超时
	SegError_SegSegTimeoutError = errtypes.SegError.NewSelfError("调用分词计算超时")
	//调用权重计算超时
	SegError_SegAseTimeoutError = errtypes.SegError.NewSelfError("调用权重计算超时")
	//分词错误
	SegError_SegSegError = errtypes.SegError.NewSelfError("分词错误")
	//未知错误
	SegError_SegUnknownError = errtypes.SegError.NewSelfError("未知错误")
	//内部数据转换错误
	SegError_SegDataTransferError = errtypes.SegError.NewSelfError("内部数据转换错误")
	//ASE请求认证错误
	SegError_SegAseReqAuthError = errtypes.SegError.NewSelfError("ASE请求认证错误")
	//ASE请求错误
	SegError_SegAseReqErrot = errtypes.SegError.NewSelfError("ASE请求错误")
	//ASE推理错误
	SegError_SegAseInferError = errtypes.SegError.NewSelfError("ASE推理错误")
	// Tokenizer错误
	SegError_TokenizerError = errtypes.SegError.NewSelfError("推理错误")
	//推理实例为空
	SegError_InferInstanceIsEmptyError = errtypes.SegError.NewSelfError("推理实例为空")
	// 推理错误
	SegError_InferError = errtypes.SegError.NewSelfError("推理错误")
	//干预资源错误
	SegError_SegInterveneError = errtypes.SegError.NewSelfError("资源干预错误")
)
