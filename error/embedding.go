package selferrors

import "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"

var (
	EmbeddingError_ModelNotSupport  = errtypes.EmbeddingError.NewSelfError("model not support")
	EmbeddingError_MarshalFailed    = errtypes.EmbeddingError.NewSelfError("model input marshal failed")
	EmbeddingError_UnmarshalFailed  = errtypes.EmbeddingError.NewSelfError("model output unmarshal failed")
	EmbeddingError_AseRequestFailed = errtypes.EmbeddingError.NewSelfError("ase http request failed")
	EmbeddingError_InferFailed      = errtypes.EmbeddingError.NewSelfError("embedding infer failed")
	EmbeddingError_TokenizerInitFailed     = errtypes.EmbeddingError.NewSelfError("tokenizer init failed")
)
