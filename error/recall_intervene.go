package selferrors

import "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"

var (
	// 预处理后请求数据为空
	RecallInterveneError_ReqEmptyError = errtypes.RecallInterveneError.NewSelfError("请求数据为空")
	// 预处理后请求数据为空
	RecallInterveneError_ReqPreEmptyError = errtypes.RecallInterveneError.NewSelfError("预处理后请求数据为空")
	// MarshalFailed 序列化失败
	RecallInterveneError_MarshalFailed = errtypes.RecallInterveneError.NewSelfError("marshal failed")
	// UnmarshalFailed 反序列化失败
	RecallInterveneError_UnmarshalFailed = errtypes.RecallInterveneError.NewSelfError("unmarshal failed")
	// 内部逻辑错误
	RecallInterveneError_InternelError = errtypes.RecallInterveneError.NewSelfError("内部逻辑错误")
	// 干预错误
	RecallInterveneError_InterveneError = errtypes.RecallInterveneError.NewSelfError("干预错误")
)
