package selferrors

import "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"

// 向量召回错误码

var (
	RecallVectorError_EmbeddingCalcFailed        = errtypes.RecallVectorError.NewSelfError("calc Embedding failed")
	RecallVectorError_MilvusAgentSearchFailed    = errtypes.RecallVectorError.NewSelfError("milvus agent search err")
	RecallVectorError_MongoAgentSearchFailed     = errtypes.RecallVectorError.NewSelfError("mongo agent search failed")
	RecallVectorError_CtxTimeoutFailed           = errtypes.RecallVectorError.NewSelfError("service timeout")
	RecallVectorError_EmbeddingModelSearchFailed = errtypes.RecallVectorError.NewSelfError("embedding model search err")
	RecallVectorError_BlackListSearchFailed      = errtypes.RecallVectorError.NewSelfError("query black list err")
)
