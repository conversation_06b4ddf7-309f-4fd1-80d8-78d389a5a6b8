package selferrors

import "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"

var (
	ProcessError_FilterFailed   = errtypes.ProcessError.NewSelfError("filter failed")
	ProcessError_SiftFailed     = errtypes.ProcessError.NewSelfError("sift failed")
	ProcessError_DistinctFailed = errtypes.ProcessError.NewSelfError("distinct failed")
	ProcessError_LimitFailed    = errtypes.ProcessError.NewSelfError("limit failed")
)
