package selferrors

import "code.iflytek.com/HY_RDG-AISS/lynxiao-ai/lynxiao-ai-search/error/errtypes"

const (
	ConnectionError = "Connection Error : "
	PingError       = "Ping Error : "
	FindError       = "Find Error : "
	DisconnectError = "Disconnect Error : "
)

var (
	MongoError_ConnectionError = errtypes.MongoError.NewSelfError(ConnectionError)
	MongoError_PingError       = errtypes.MongoError.NewSelfError(PingError)
	MongoError_FindError       = errtypes.MongoError.NewSelfError(FindError)
	MongoError_DisconnectError = errtypes.MongoError.NewSelfError(DisconnectError)
)
